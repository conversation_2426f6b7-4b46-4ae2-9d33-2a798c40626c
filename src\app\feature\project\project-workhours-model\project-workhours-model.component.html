<mat-dialog-title>

    <div fxFlex fxLayout="row" fxLayoutAlign="space-between" class="mb-1">

        <h2 mat-dialog-title>Estimated Work Hours</h2>

    </div>
    <hr class="mb-1">

</mat-dialog-title>

<mat-dialog-content>
    <form #workHoursForm="ngForm">
        <mat-card-content>
            <mat-form-field class="example-full-width">
                <input matInput type="number" name="level5" placeholder="Level 5"
                    [(ngModel)]="projectEstHoursDTO.estHoursLevel5" min="0" pattern="^[0-9]{0,}$" #inputAm="ngModel">
                <mat-error *ngIf="inputAm.touched && inputAm.invalid">
                    <small class="mat-text-warn" *ngIf="inputAm?.errors.pattern">Invalid Input</small>
                </mat-error>
            </mat-form-field>
            <mat-form-field class="example-full-width">
                <input matInput type="number" name="level4" placeholder="Level 4"
                    [(ngModel)]="projectEstHoursDTO.estHoursLevel4" min="0" pattern="^[0-9]{0,}$" #inputDm="ngModel">
                <mat-error *ngIf="inputDm.touched && inputDm.invalid">
                    <small class="mat-text-warn" *ngIf="inputDm?.errors.pattern">Invalid Input</small>
                </mat-error>
            </mat-form-field>
            <mat-form-field class="example-full-width">
                <input matInput type="number" name="level3" placeholder="Level 3"
                    [(ngModel)]="projectEstHoursDTO.estHoursLevel3" min="0" pattern="^[0-9]{0,}$" #inputTl="ngModel">
                <mat-error *ngIf="inputTl.touched && inputTl.invalid">
                    <small class="mat-text-warn" *ngIf="inputTl?.errors.pattern">Invalid Input</small>
                </mat-error>
            </mat-form-field>
            <mat-form-field class="example-full-width">
                <input matInput type="number" name="level2" placeholder="Level 2"
                    [(ngModel)]="projectEstHoursDTO.estHoursLevel2" min="0" pattern="^[0-9]{0,}$" #inputSr="ngModel">
                <mat-error *ngIf="inputSr.touched && inputSr.invalid">
                    <small class="mat-text-warn" *ngIf="inputSr?.errors.pattern">Invalid Input</small>
                </mat-error>
            </mat-form-field>
            <mat-form-field class="example-full-width">
                <input matInput type="number" name="level1" placeholder="Level 1"
                    [(ngModel)]="projectEstHoursDTO.estHoursLevel1" min="0" pattern="^[0-9]{0,}$" #inputJr="ngModel">
                <mat-error *ngIf="inputJr.touched && inputJr.invalid">
                    <small class="mat-text-warn" *ngIf="inputJr?.errors.pattern">Invalid Input</small>
                </mat-error>
            </mat-form-field>

        </mat-card-content>
    </form>

</mat-dialog-content>


<mat-dialog-actions fxLayoutAlign="end">
    <button class="bt-flat" type="submit" (click)="closeModal()">Cancel</button>
    <button mat-raised-button class="bt-sfl" type="submit" (click)="saveWorkHours()"
        [style.cursor]="workHoursForm.form.invalid ? 'not-allowed' : 'pointer'" [disabled]="workHoursForm.form.invalid">
        Save
    </button>
</mat-dialog-actions>