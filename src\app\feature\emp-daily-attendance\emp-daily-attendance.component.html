<div class="body-content" fxLayout="column" fxFlex="100">
  <div fxFlex fxLayout="row" fxLayoutAlign="space-between" class="mb-1">
    <mat-card-title class="p-10">Attendance Logs</mat-card-title>
  </div>
  <hr class="header-divider">
    <div fxLayout="row column" fxLayoutAlign="start center" fxFlex="100" class="sfl-card">
      <mat-card-actions class="filter-input ml-1 mr-1">
        <mat-form-field>
          <input matInput placeholder="Start date" name="startDate" aria-label="start-date" (focus)="sDate.open()" (click)="sDate.open()"
                 [matDatepicker]="sDate" [max]="selectedEndDate"
                 [required]="filterWorklog.filterBy === freeSearch" [(ngModel)]="selectedStartDate">
          <mat-datepicker-toggle matSuffix [for]="sDate"></mat-datepicker-toggle>
          <mat-datepicker #sDate></mat-datepicker>
          <button mat-icon-button matSuffix class="float-center" matTooltip="clear" *ngIf="selectedStartDate"
            (click)="resetFilter('startDate')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1">
        <mat-form-field>
          <input matInput placeholder="End date" name="endDate" aria-label="end-date" (focus)="eDate.open()" (click)="eDate.open()"
                 [matDatepicker]="eDate" [min]="selectedStartDate"
                 [required]="filterWorklog.filterBy === freeSearch" [(ngModel)]="selectedEndDate">
          <mat-datepicker-toggle matSuffix [for]="eDate"></mat-datepicker-toggle>
          <mat-datepicker #eDate></mat-datepicker>
          <button mat-icon-button matSuffix class="float-center" matTooltip="clear" *ngIf="selectedEndDate"
            (click)="resetFilter('endDate')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-button-group ml-1">
        <button mat-raised-button class="bt-sfl mr-10px" type="submit" (click)="filterList()">Filter</button>
        <button class="bt-flat" type="button" (click)="resetAllFilter()">Reset All</button>
      </mat-card-actions>
    </div>

  <div class="sfl-card">
    <div class="p-25" fxLayoutAlign="center center" fxFlex="100" *ngIf="showLoader">
      <mat-progress-spinner color='warn' mode="indeterminate"></mat-progress-spinner>
    </div>

    <div *ngIf="!showLoader && noDataFound" class="w-100 mt-40 no-data-found-text">No Attendance Found</div>

    <div class="w-100" *ngIf="!showLoader && !noDataFound">
      <table class="w-auto" mat-table matSort matSortDisableClear [dataSource]="dataSource"
             [matSortActive]="pageable.sort" [matSortDirection]="pageable.direction" (matSortChange)="getSorting($event)">
        <ng-container matColumnDef="empName">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-sm="80"> Employee Name </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-sm="80">
            {{element?.userName}}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="clockIn">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-sm="80"> Clock-In Time </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-sm="80">
            {{element?.clockIn | date: 'HH:mm:ss | dd-MM-yyyy' : 'UTC'}}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="clockOut">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-sm="80"> Clock-Out Time </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-sm="80">
            {{element?.clockOut | date: 'HH:mm:ss | dd-MM-yyyy' : 'UTC'}}
          </mat-cell>
        </ng-container>

        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
      </table>
      <mat-paginator [length]="empAttandance.totalElements" [pageSizeOptions]="[10, 20, 25]"
      [pageIndex]="pageable.page" [pageSize]="pageable.size" (page)="getPagination($event)" showFirstLastButtons>
    </mat-paginator>
    </div>
  </div>
  </div>
