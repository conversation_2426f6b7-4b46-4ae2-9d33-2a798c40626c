import { Pageable, Sort } from 'src/app/shared/utils/utils';
import { Variable } from '../../shared';
import { Client } from '../client/client.model';

export class Project {
  id: number;
  name: string;
  active: boolean;
  description: string;
  clientId: number;
  clientDTO: Client;
  startDate: string | Date;
  projectKey: string;

  constructor() {
    this.active = true;
  }
}

export class FilterProject {
  clientName: String;
  projectName: String;
  projectStatus: String;
}

export class ProjectPageable {
  content: Project[];
  pageable: Pageable;
  totalElements: number;
  totalPages: number;
  last: boolean;
  numberOfElements: number;
  first: boolean;
  sort: Sort;
  size: number;
  number: number;
}

export class ProjectDevelopers {
  id: number;
  projectDetailsId: number;
  projectDeveloperId: number;
  projectDevelopersName: string;
  type: string;
  status: boolean;
}

export class ProjectDetails {
  active: boolean;
  accountManagerFullName: string;
  accountManagerId: number;
  deliveryManagerFullName: string;
  deliveryManagerId: number;
  projectDevelopers: Array<ProjectDevelopers>;
  id: number;
  projectId: number;
  projectName: string;
  teamLeadFullName: string;
  teamLeadId: number;
  projectDocuments: Array<ProjectDocument>;
  startDate: string | Date;
  constructor() {
    this.projectDevelopers = this.projectDevelopers ? this.projectDevelopers : [];
    this.projectDocuments = this.projectDocuments ? this.projectDocuments : [];
  }
}

export class WorkHoursReportDTO {
  projectId: number;
  levelId: number;
  estHours: number;
  calHours: string;
}

export class ProjectDocument {
  id: number;
  documentName: string;
  documentURL: string;
  projectDetailsId: number;
  status: boolean;
}

export class ProjectEstHoursDTO {
  id: number;
  estHoursLevel1: number;
  estHoursLevel2: number;
  estHoursLevel3: number;
  estHoursLevel4: number;
  estHoursLevel5: number;
  projectId: number;
}

export const PROJECT_DISPLAY_COLUMNS = [Variable.NAME, Variable.ACTIVE, Variable.CLIENT_CLIENT_NAME, Variable.DESCRIPTION, Variable.ACTION];

export const MemberTypesSecondary = [
  {
    title: 'Developer',
    value: Variable.DEVELOPER,
  },
  {
    title: 'QA',
    value: Variable.QA,
  },
];

export const MemberTypesPrimary = [
  {
    title: 'Account Manager',
    value: Variable.ACCOUNT_MANAGER,
  },
  {
    title: 'Delivery Manager',
    value: Variable.DELIVERY_MANAGER,
  },
  {
    title: 'Tech Lead',
    value: Variable.TECHNICAL_LEAD,
  },
];

export const WORK_HOURS_REPORT_COLUMNS = ['levelId', 'estHours', 'calHours'];

export interface ProjectIdDialogData {
  projectId: number;
}

export interface UserModelDialogData {
  title: string;
  type: string;
  isEdit: boolean;
  userId: number;
}
