import { Injectable } from "@angular/core";
import { Subject } from "rxjs";
import { AppConfig } from "../../app.config";
import { createRequestOption, HttpClientService, PageableQuery, Variable } from "../../shared";
import { BasicProfile, EmployeeIdParam, ProfilePicture, UserLevelDto } from "./profile.model";
import { Employee, EmployeeRequest } from "../employee-management/employee-management.model";

@Injectable({
  providedIn: "root",
})
export class ProfileService {
  invokeEvent: Subject<any> = new Subject();

  constructor(private http: HttpClientService) {}

  callMethodProfileComponent() {
    this.invokeEvent.next();
  }

  getUserDetails(userId) {
    return this.http.get(AppConfig.EMPLOYEE_DETAIL + userId);
  }

  updateProfile(basicProfileList: BasicProfile, uaaUserId: string) {
    return this.http.put(AppConfig.EMPLOYEE_DETAIL + uaaUserId, basicProfileList);
  }

  updateProfilePicture(profilePicture: ProfilePicture) {
    const formData: FormData = new FormData();
    formData.append(Variable.ID, profilePicture.id);
    formData.append(Variable.FILE, profilePicture.file, profilePicture.file.name);
    return this.http.post(AppConfig.SET_EMPLOYEE_PROFILE_PICTURE, formData);
  }

  getKudosByUserId(employeeIdParam: EmployeeIdParam, pageableObject: PageableQuery) {
    return this.http.post(AppConfig.USER_AWARDS_FILTER, employeeIdParam, {
      params: createRequestOption(pageableObject),
    });
  }

  deleteAddress(id: number) {
    return this.http.delete(AppConfig.USER_ADDRESSES + id);
  }

  getLevels() {
    return this.http.get(AppConfig.MASTER_LEVELS);
  }

  getDesignations() {
    return this.http.get(AppConfig.DESIGNATIONS);
  }

  setUserLevel(userLevel: UserLevelDto) {
    return this.http.post(AppConfig.USER_LEVEL, userLevel);
  }

  updateUserLevel(userLevel: UserLevelDto) {
    return this.http.put(AppConfig.USER_LEVEL, userLevel);
  }

  updateEmployee(emp: EmployeeRequest) {
    return this.http.put(`${AppConfig.EMPLOYEE_MANAGEMENT_DETAIL}`, emp);
  }

  getRoles() {
    return this.http.get(AppConfig.ROLES);
  }
}
