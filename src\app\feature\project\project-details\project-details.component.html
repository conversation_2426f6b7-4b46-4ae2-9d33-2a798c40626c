<div class="body-content" fxLayout="column" fxFlex="100">
  <!-- Page Header -->
  <div fxFlex fxLayout="row" fxLayoutAlign="space-between" class="mb-1">
    <div class="p-10 card-title">
      <span class="title">{{ project.name }}</span>
      <span class="custom-chip" [ngClass]="project.active ? 'custom-chip-success' : 'custom-chip-danger'">{{
        project.active ? ACTIVE_PROJECT : INACTIVE_PROJECT
      }}</span>
    </div>
    <div>
      <button mat-raised-button class="bt-sfl mr-10px" (click)="addWorkHours()" *ngIf="isActive">
        Add Estimated WorkHours
      </button>
      <button mat-raised-button class="bt-sfl mr-10px" (click)="generateWorkHoursReport(project.id)" *ngIf="isActive">
        Generate WorkHours Report
      </button>
      <button mat-raised-button class="bt-sfl mr-10px" (click)="generateForm()" *ngIf="false">
        Generate Form
      </button>
      <button mat-raised-button class="bt-sfl" [routerLink]="empWorklogURL"
        [queryParams]="{ projectname: project.name }" *ngIf="isActive">
        View Worklog
      </button>
    </div>
  </div>

  <hr class="header-divider">

  <!-- Team Members -->
  <div class="sfl-card" fxFlex="100" fxLayout="column">
    <div fxFlex fxLayout="row" fxLayoutAlign="space-between" class="mb-1">
      <mat-card-title class="p-10">
        Team Allocation Details
      </mat-card-title>
    </div>
    <mat-card-content fxLayout="column">
      <div fxLayout="row">
        <div fxFlex *ngIf="!projectDetails.accountManagerId && isActive">
          <div class="ui-card bordered" (click)="addUser(VARIABLE.ACCOUNT_MANAGER)">
            <mat-icon>add</mat-icon>
            <span class="text">
              Add Account Manager
            </span>
          </div>
        </div>

        <div fxFlex *ngIf="projectDetails.accountManagerId">
          <div class="ui-card colored">
            <button mat-icon-button class="edit-member-button" matTooltip="Edit"
              (click)="editUser(VARIABLE.ACCOUNT_MANAGER, projectDetails.accountManagerId, projectDetails.estAmHours)"
              *ngIf="isActive">
              <mat-icon>edit</mat-icon>
            </button>
            <div fxLayout="column" fxLayoutAlign="space-around start">
              <div>
                <span class="name">{{ projectDetails.accountManagerFullName }}</span>
                <span class="designation">Account Manager</span>
              </div>
            </div>
          </div>

        </div>

        <div fxFlex *ngIf="!projectDetails.deliveryManagerId && isActive">
          <div class="ui-card bordered" (click)="addUser(VARIABLE.DELIVERY_MANAGER)">
            <mat-icon>add</mat-icon>
            <span class="text">
              Add Delivery Manager
            </span>
          </div>
        </div>

        <div fxFlex *ngIf="projectDetails.deliveryManagerId">
          <div class="ui-card colored">
            <button mat-icon-button class="edit-member-button" matTooltip="Edit"
              (click)="editUser(VARIABLE.DELIVERY_MANAGER, projectDetails.deliveryManagerId, projectDetails.estDmHours)"
              *ngIf="isActive">
              <mat-icon>edit</mat-icon>
            </button>
            <div fxLayout="column" fxLayoutAlign="space-around start">
              <div>
                <span class="name">{{ projectDetails.deliveryManagerFullName }}</span>
                <span class="designation">Delivery Manager</span>
              </div>
            </div>
          </div>
        </div>

        <div fxFlex *ngIf="!projectDetails.teamLeadId && isActive">
          <div class="ui-card bordered" (click)="addUser(VARIABLE.TECHNICAL_LEAD)">
            <mat-icon>add</mat-icon>
            <span class="text">
              Add Technical Lead
            </span>
          </div>
        </div>

        <div fxFlex *ngIf="projectDetails.teamLeadId">
          <div class="ui-card colored">
            <button mat-icon-button class="edit-member-button" matTooltip="Edit"
              (click)="editUser(VARIABLE.TECHNICAL_LEAD, projectDetails.teamLeadId, projectDetails.estTlHours)"
              *ngIf="isActive">
              <mat-icon>edit</mat-icon>
            </button>
            <div fxLayout="column" fxLayoutAlign="space-around start">
              <div>
                <span class="name">{{ projectDetails.teamLeadFullName }}</span>
                <span class="designation">Technical Lead</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Developers & QAs -->
      <div class="sfl-card" fxFlex="100" fxLayout="column">
        <div fxFlex fxLayout="row" fxLayoutAlign="space-between" class="mb-1">
          <mat-card-title class="p-10">Developers &amp; QA</mat-card-title>
          <button mat-mini-fab color="warn" matTooltip="Add Member" matTooltipPosition="before"
            (click)="addUser(VARIABLE.SECONDARY)" *ngIf="isActive">
            <mat-icon>add</mat-icon>
          </button>
        </div>
        <mat-card-content>
          <div fxLayout="row wrap">
            <div fxFlex
              *ngIf="projectDetails && projectDetails.projectDevelopers && !projectDetails.projectDevelopers.length">
              <h3 class="no-members">No Members</h3>
            </div>
            <div fxFlex="0 1 calc(25%)" *ngFor="let user of projectDetails.projectDevelopers; let i = index">
              <div class="ui-card">
                <button mat-icon-button class="edit-member-button" matTooltip="Delete" (click)="deleteUser(i)"
                  *ngIf="isActive">
                  <mat-icon>delete</mat-icon>
                </button>
                <div fxLayout="column" fxLayoutAlign="space-around start">
                  <div>
                    <span class="name">{{ user.projectDevelopersName }}</span>
                    <span class="designation">{{user.type}}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </mat-card-content>
      </div>
    </mat-card-content>
  </div>

  <!-- Project Docs URLs -->
  <div class="sfl-card" fxFlex="100" fxLayout="column">
    <div fxFlex fxLayout="row" fxLayoutAlign="space-between" class="mb-1">
      <mat-card-title class="p-10">Project Docs</mat-card-title>
      <button mat-raised-button class="bt-sfl" (click)="addDocument()" *ngIf="isActive">Add Document</button>
    </div>
    <mat-card-content>
      <div class="card-contents">
        <div fxLayout="row wrap">
          <div fxFlex
            *ngIf="projectDetails && projectDetails.projectDocuments && !projectDetails.projectDocuments.length">
            <h3 class="no-members">No Documents</h3>
          </div>

          <div fxFlex="50" *ngFor="let document of projectDetails.projectDocuments; let i = index">
            <a [href]="document.documentURL" target="_blank">
              <div fxLayout="row" class="file">
                <div class="icon" fxFlex="10">
                  <mat-icon>attach_file</mat-icon>
                </div>
                <div class="details" fxFlex="90">
                  <a [href]="document.documentURL" class="file-name" target="_blank">{{ document.documentName }}</a>
                  <button type="button" mat-icon-button class="delete-file" (click)="deleteDocument($event, i)"
                    *ngIf="isActive">
                    <mat-icon color="warn">close</mat-icon>
                  </button>
                </div>
              </div>
            </a>
          </div>
        </div>
      </div>
    </mat-card-content>
  </div>

  <!-- Generated Forms Listing -->
  <div class="sfl-card" fxFlex="100" fxLayout="column" *ngIf="false">
    <div fxFlex fxLayout="row" fxLayoutAlign="space-between" class="mb-1">
      <mat-card-title class="p-10">Generated Forms</mat-card-title>
    </div>
    <mat-card-content>
      <div class="card-contents">
        <div fxLayout="row wrap">
          <sfl-generate-form class="w-100" [projectId]="projectId" #_ref_generateFormModal></sfl-generate-form>
        </div>
      </div>
    </mat-card-content>
  </div>

</div>