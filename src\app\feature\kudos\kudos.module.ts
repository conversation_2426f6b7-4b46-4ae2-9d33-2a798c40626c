import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { RouterModule } from '@angular/router';
import { SharedModule } from '../../shared/shared.module';
import { kudosRoutes } from './kudos.route';
import { KudosComponent } from './kudos.component';
import { KudosService } from './kudos.service';
import { MatPaginatorModule } from '@angular/material';
import {KudosDetailComponent} from './kudos-modals/kudos-detail/kudos-detail.component';
import {GivenKudosDetailComponent} from './kudos-modals/given-kudos-detail/given-kudos-detail.component';
import {GiveKudosComponent} from './kudos-modals/give-kudos/give-kudos.component';
import {SaveKudosComponent} from './kudos-modals/save-kudos/save-kudos.component';
import {KudosListComponent} from './kudos-list/kudos-list.component';
import { KudosInstructionsComponent } from './kudos-modals/give-kudos/kudos-instructions/kudos-instructions.component';
import {EmployeeManagementService} from '../employee-management/employee-management.service';


@NgModule({
  imports: [
    RouterModule.forChild(kudosRoutes),
    SharedModule,
    MatPaginatorModule
  ],
  exports: [
    MatPaginatorModule
  ],
  declarations: [
    KudosComponent,
    GivenKudosDetailComponent,
    GiveKudosComponent,
    GivenKudosDetailComponent,
    SaveKudosComponent,
    KudosDetailComponent,
    KudosListComponent,
    KudosInstructionsComponent
  ],
  entryComponents: [KudosInstructionsComponent],
  providers: [
    KudosService,
    EmployeeManagementService
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})

export class KudosModule {}
