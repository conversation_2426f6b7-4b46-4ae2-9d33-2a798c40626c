import { Injectable } from '@angular/core';
import { AppConfig } from '../../app.config';
import {createRequestOption, HttpClientService, PageableQuery} from '../../shared';
import {FilterKudos, GivenKudos, Kudos} from './kudos.model';


@Injectable()
export class KudosService {

  constructor(private http: HttpClientService) { }

  getFilterKudos(parameters: FilterKudos, pageableObject: PageableQuery) {
    return this.http.post(AppConfig.USER_AWARDS_FILTER, parameters, {
      params: createRequestOption(pageableObject)
    });
  }

  getKudos() {
    return this.http.get(AppConfig.AWARDS);
  }

  deleteKudos(kudos: Kudos) {
    return this.http.delete(AppConfig.AWARDS + kudos.id);
  }

  addKudos(formData: FormData) {
    return this.http.post(AppConfig.AWARDS_UPLOAD_IMAGE, formData);
  }

  updateKudos(formData: FormData, id: number) {
    return this.http.put(AppConfig.AWARDS_UPLOAD_IMAGE + id, formData);
  }

  giveKudosToEmployee(employeeAwards: GivenKudos) {
    return this.http.post(AppConfig.EMPLOYEE_AWARDS, employeeAwards);
  }

}
