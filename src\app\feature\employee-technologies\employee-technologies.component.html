<div class="body-content" fxLayout="column" fxFlex="100">
  <div fxLayout="row" fxLayoutAlign="space-between" class="mb-1">
    <mat-card-title class="p-10">Employee Technologies</mat-card-title>
    <button mat-raised-button class="bt-sfl" (click)="openEmployeeTechnologyDialog(addText, false)">Add New Employee Technologies</button>
  </div>

  <hr class="header-divider" />

  <div fxLayout="row" fxFlex="100" class="sfl-card">
    <div class="filter-input ml-1 mr-1">
      <mat-form-field floatLabel="never">
        <input matInput type="text" #filter (keyup)="applyFilter(filter.value)" placeholder="Filter" aria-label="filter" />
      </mat-form-field>
    </div>
  </div>

  <div class="sfl-card">
    <div class="w-100" *ngIf="!showLoader && !noDataFound">
      <table class="w-auto" mat-table matSort [dataSource]="dataSource" matSortActive="userName" matSortDirection="asc" matSortDisableClear>
        <ng-container matColumnDef="userName">
          <mat-header-cell *matHeaderCellDef mat-sort-header> Employee Name </mat-header-cell>
          <mat-cell *matCellDef="let element">
            {{ element?.userName }}
          </mat-cell>
        </ng-container>

        <ng-container matColumnDef="technologyId">
          <mat-header-cell *matHeaderCellDef mat-sort-header> Employee Technology </mat-header-cell>
          <mat-cell *matCellDef="let element">
            {{ getNameForTechnology(element?.technologyDTOS) }}
          </mat-cell>
        </ng-container>

        <ng-container matColumnDef="editTechnology">
          <mat-header-cell *matHeaderCellDef> Action </mat-header-cell>
          <mat-cell *matCellDef="let element">
            <button mat-icon-button (click)="openSaveModal(element)">
              <mat-icon>edit</mat-icon>
            </button>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns"></mat-row>
      </table>
      <mat-paginator [pageSizeOptions]="[10, 20, 25]" showFirstLastButtons></mat-paginator>
    </div>
  </div>
</div>
