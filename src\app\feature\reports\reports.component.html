<div class="body-content" fxLayout="column" fxFlex="100">
  <div fxFlex fxLayout="row" fxLayoutAlign="space-between" class="mb-1">
    <mat-card-title class="p-10">Reports</mat-card-title>
    <div>
      <button mat-raised-button class="bt-sfl mr-10px" *ngIf="hasPermission(ReportType.PROJECT_REPORT)" (click)="exportProjectTimeSheet()" [disabled]="getReportDisabled(FilterName.PROJECT)">
        Export TimeSheet (Project Hours) 
        <mat-progress-spinner
          *ngIf="isExportingProject"
          [diameter]="20"
          [strokeWidth]="3"
          mode="indeterminate"
          style="display: inline-block"
        ></mat-progress-spinner>
      </button>
      <button mat-raised-button class="bt-sfl mr-10px" *ngIf="hasPermission(ReportType.DEVELOPER_REPORT)" (click)="exportDeveloperTimeSheet()" [disabled]="getReportDisabled(FilterName.EMPLOYEE)">
        Export TimeSheet (Developer Hours) 
        <mat-progress-spinner
          *ngIf="isExportingDeveloper"
          [diameter]="20"
          [strokeWidth]="3"
          mode="indeterminate"
          style="display: inline-block"
        ></mat-progress-spinner>
      </button>
    </div>
  </div>
  <hr class="header-divider" />
  <div fxLayout="row column" fxLayoutAlign="start center" fxFlex="100" class="sfl-card">
    <mat-card-actions class="filter-input ml-1 mr-1">
      <mat-form-field>
        <mat-select [(ngModel)]="monthOrWeek" (selectionChange)="setRangeDates()">
          <mat-option value="1"> Month </mat-option>
          <mat-option value="2"> Week </mat-option>
        </mat-select>
      </mat-form-field>
    </mat-card-actions>

    <mat-card-actions fxLayout="row" fxLayoutAlign="space-between center" class="filter-input ml-1 mr-1">
      <button mat-icon-button (click)="previousRange()">
        <mat-icon aria-hidden="false" aria-label="Example home icon">arrow_back_ios</mat-icon>
      </button>
      <mat-form-field class="date-range cursor-pointer">
        <span (click)="sDate.open()">{{ dateRangeStr }}</span>
        <input
          style="display: none"
          matInput
          name="startDate"
          aria-label="start-date"
          (focus)="sDate.open()"
          (click)="sDate.open()"
          [matDatepicker]="sDate"
          [(ngModel)]="currentDate"
          (ngModelChange)="setRangeDates()"
        />
        <mat-datepicker #sDate></mat-datepicker>
      </mat-form-field>
      <button mat-icon-button (click)="nextRange()">
        <mat-icon aria-hidden="false" aria-label="Example home icon">arrow_forward_ios</mat-icon>
      </button>
    </mat-card-actions>

    <mat-card-actions class="filter-input ml-5 mr-1" *ngIf="hasPermission(ReportType.PROJECT_REPORT)">
      <mat-form-field>
        <mat-select
          placeholder="Select Project"
          name="projectIds"
          (ngModelChange)="setFilterParam(FilterName.PROJECT)"
          [(ngModel)]="filterParam.projectIds"
          multiple
        >
        <mat-option (click)="selectAllProjects()" *ngIf="projects.length" [value]="0">Select All</mat-option>
        <mat-option *ngFor="let project of projects" [value]="project?.id">{{ project?.name }}</mat-option>
        </mat-select>
        <button mat-icon-button matSuffix matTooltip="clear" *ngIf="filterParam?.projectIds?.length" (click)="resetFilter(FilterName.PROJECT)">
          <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
        </button>
      </mat-form-field>
    </mat-card-actions>
    
    <mat-card-actions class="filter-input ml-4 mr-1" *ngIf="hasPermission(ReportType.DEVELOPER_REPORT)">
      <mat-form-field>
        <mat-select
          placeholder="Select Employees"
          name="uaaUserIds"
          (selectionChange)="setFilterParam(FilterName.EMPLOYEE)"
          [(ngModel)]="filterParam.uaaUserIds"
          multiple
        >
          <mat-option (click)="selectAllEmployees()" *ngIf="developers.length" [value]="0">Select All</mat-option>
          <mat-option *ngFor="let developer of developers" [value]="developer?.id">{{ developer?.fullName }}</mat-option>
        </mat-select>
        <button mat-icon-button matSuffix matTooltip="clear" *ngIf="filterParam?.uaaUserIds?.length" (click)="resetFilter(FilterName.EMPLOYEE)">
          <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
        </button>
      </mat-form-field>
    </mat-card-actions>

    <mat-card-actions
      class="filter-input ml-1 mr-1"
      *ngIf="(filterParam.projectIds?.length || filterParam.uaaUserIds?.length)"
    >
      <button class="bt-flat" type="button" (click)="resetFilter(FilterName.ALL)">Reset Filter</button>
    </mat-card-actions>
  </div>
</div>
