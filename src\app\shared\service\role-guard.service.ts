import { Location } from "@angular/common";
import { Injectable } from "@angular/core";
import { ActivatedRouteSnapshot, CanActivate, RouterStateSnapshot } from "@angular/router";
import { Observable } from "rxjs";
import { Messages } from "..";
import { SharedService } from "./shared.service";
import { SnackBarService } from "./snack-bar.service";

@Injectable({
  providedIn: "root",
})
export class RoleGuardService implements CanActivate {
  constructor(private sharedService: SharedService, private snackBar: SnackBarService, private location: Location) {}

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> | Promise<boolean> | boolean {
    const allowedRoles = route.data.allowedRoles;
    const currentRoles = this.sharedService.getRole();
    if (currentRoles && currentRoles.filter((ele) => allowedRoles.includes(ele)).length) {
      return true;
    }
    this.snackBar.error(Messages.UNAUTHORIZED);
    this.location.back();
    return false;
  }
}
