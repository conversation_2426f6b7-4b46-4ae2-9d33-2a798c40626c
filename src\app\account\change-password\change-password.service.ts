import { Injectable } from '@angular/core';
import { HttpClientService } from 'src/app/shared';
import { AppConfig } from '../../app.config';
import { ChangePassword } from './change-password.model';


@Injectable({
  providedIn: 'root'
})
export class ChangePasswordService {

  constructor(private http: HttpClientService) { }

  setNewPassword(changePassword: ChangePassword) {
    return this.http.post(AppConfig.CHANGE_PASSWORD_URL, changePassword);
  }

}
