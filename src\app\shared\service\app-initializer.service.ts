import { Injectable } from '@angular/core';
import { RefreshTokenService } from './refresh-token.service';

@Injectable({
  providedIn: 'root'
})
export class AppInitializerService {

  constructor(private refreshTokenService: RefreshTokenService) {}

  /**
   * Initialize the application
   * This function is called during app startup via APP_INITIALIZER
   */
  initializeApp(): Promise<void> {
    return new Promise((resolve) => {
      try {
        // Initialize token management on startup
        this.refreshTokenService.initializeOnStartup();
        resolve();
      } catch (error) {
        console.error('Error during app initialization:', error);
        resolve(); // Don't block app startup even if there's an error
      }
    });
  }
}

/**
 * Factory function for APP_INITIALIZER
 */
export function appInitializerFactory(appInitializerService: AppInitializerService) {
  return () => appInitializerService.initializeApp();
}
