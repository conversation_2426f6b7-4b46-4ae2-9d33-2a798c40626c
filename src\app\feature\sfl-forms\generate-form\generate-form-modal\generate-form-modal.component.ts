import {Component, Inject, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {SharedService, Variable} from '../../../../shared';
import {MAT_DIALOG_DATA, MatDialog, MatDialogRef} from '@angular/material/dialog';
import {ActivatedRoute, Router} from '@angular/router';
import {Subscription} from 'rxjs';
import {SflFormsService} from '../../sfl-forms.service';
import {FORM_GENERATOR, GenerateForm, SflFormsModel} from '../../sfl-forms.model';
import {MatAutocompleteSelectedEvent} from '@angular/material/autocomplete';
import {PreviewModalComponent} from '../../preview-modal/preview-modal.component';
import {Location} from '@angular/common';
import {GenerateFormComponent} from '../generate-form.component';
import * as moment from 'moment';

@Component({
  selector: 'sfl-generate-form-modal',
  templateUrl: './generate-form-modal.component.html'
})
export class GenerateFormModalComponent implements OnInit, OnDestroy {
  createdForms: SflFormsModel[] = [];
  subscription: Subscription = new Subscription();
  generateForm: GenerateForm = new GenerateForm();
  formGenerator = FORM_GENERATOR;
  isPreviewModalOpen = false;

  constructor(
    private matDialog: MatDialog,
    @Inject(MAT_DIALOG_DATA) public data: GenerateForm,
    public dialogRef: MatDialogRef<GenerateFormComponent>,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private sharedService: SharedService,
    private sflFormsService: SflFormsService,
    private location: Location
  ) {
    this.activatedRoute.queryParams.subscribe((params) => {
      if (params && params.generateFormModal && params.projectId) {
        this.generateForm.projectId = params.projectId;
      }
    });
  }

  ngOnInit() {
    this.activatedRoute.queryParams.subscribe((params) => {
      if (params && params.generateForm) {
        setTimeout(() => {
          if (params.projectId) {
            this.generateForm.projectId = params.projectId;
          }
        }, 0);
      }
    });
    if (this.data.generateFormModel) {
      this.generateForm = this.data.generateFormModel;
    } else {
      this.generateForm.projectId = this.data.directory.projectId;
      this.setStartDateEndDate();
    }
    if (!this.generateForm.projectId) {
      this.location.back();
    }
    this.generateForm.createdBy = this.sharedService.getUaaUserId();
    this.getCreatedForms();
  }

  getCreatedForms() {
    this.subscription.add(
      this.sflFormsService.getCreatedFormsForProject(
        this.generateForm.projectId, this.sharedService.getUaaUserId()
      ).subscribe((res: SflFormsModel[]) => {
        this.createdForms = res;
      })
    );
  }

  removeGenerator(generator: string): void {
    const index = this.generateForm.fillableBy.indexOf(generator);
    if (index >= 0) {
      this.generateForm.fillableBy.splice(index, 1);
    }
  }

  selectedGenerator(event: MatAutocompleteSelectedEvent): void {
    this.generateForm.fillableBy.push(event.option.value);
  }

  saveForm() {
    if (this.data.generateFormModel) {
      this.updateGenerateForm();
    } else {
      this.addGenerateForm();
    }
  }

  addGenerateForm() {
    // fixing the time to avoid month/date/year change cases, for example when date is 01-01-2019 should remain same in DB as well.
    this.generateForm.startDate = moment(this.generateForm.startDate).utcOffset(Variable.IST_OFFSET).set({hour: 19, minute: 30, second: 0}).format();
    this.generateForm.endDate = moment(this.generateForm.endDate).utcOffset(Variable.IST_OFFSET).set({hour: 23, minute: 59, second: 59}).format();

    this.subscription.add(this.sflFormsService.createGenerateForm(this.generateForm).subscribe(() => {
      this.closeDialog(true);
    }));
  }

  updateGenerateForm() {
    this.generateForm.startDate = moment(this.generateForm.startDate).utcOffset(Variable.IST_OFFSET).set({hour: 19, minute: 30, second: 0}).format();
    this.generateForm.endDate = moment(this.generateForm.endDate).utcOffset(Variable.IST_OFFSET).set({hour: 23, minute: 59, second: 59}).format();

    this.subscription.add(this.sflFormsService.updateGenerateForm(this.generateForm).subscribe(() => {
      this.closeDialog(true);
    }));
  }

  previewForm(id: string) {
    if (!this.isPreviewModalOpen) {
      this.isPreviewModalOpen = true;
      this.subscription.add(
        this.sflFormsService.getCreatedFormsById(id).subscribe((res: SflFormsModel) => {
          this.matDialog.open(PreviewModalComponent, {
            data: res
          });
          this.isPreviewModalOpen = false;
        })
      );
    }
  }

  setStartDateEndDate() {
    this.generateForm.startDate = new Date();
    this.generateForm.endDate = new Date(this.generateForm.startDate);
    this.generateForm.endDate.setDate(this.generateForm.startDate.getDate() + 15 );
  }

  closeDialog(result = false): void {
    this.dialogRef.close(result);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
