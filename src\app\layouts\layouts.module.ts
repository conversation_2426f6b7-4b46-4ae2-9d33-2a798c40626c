import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { SharedModule } from '../shared/shared.module';
import { errorRoute } from './error/error.route';
import { SideNavBarService } from './side-nav-bar/side-nav-bar.service';
import {EmployeeManagementService} from '../feature/employee-management/employee-management.service';
import {
  ErrorComponent,
  TopBarComponent,
  FooterComponent,
  SideNavBarComponent,
  OutsideLayoutComponent
} from '.';


@NgModule({
  imports: [RouterModule.forRoot([...errorRoute]), SharedModule],
  declarations: [
    ErrorComponent,
    TopBarComponent,
    FooterComponent,
    SideNavBarComponent,
    OutsideLayoutComponent,
  ],
  entryComponents: [],
  exports: [RouterModule],
  providers: [
    SideNavBarService,
    EmployeeManagementService
  ]
})
export class LayoutsModule {}
