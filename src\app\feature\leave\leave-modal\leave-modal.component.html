<h2 mat-dialog-title>Leave</h2>
<hr class="mb-1">

<mat-dialog-content>
  <form #leaveUpdateForm="ngForm">
    <mat-form-field>
      <input matInput [matDatepicker]="startDatePicker" placeholder="Choose a Start Date" name="start_date" aria-label="start-date"
             #start_date="ngModel" [(ngModel)]="leave.fromDate" [max]="leave.toDate" (click)="startDatePicker.open()" required
             (dateChange)="calculateLeaveDates('start_date')">
      <mat-datepicker-toggle matSuffix [for]="startDatePicker"></mat-datepicker-toggle>
      <mat-datepicker #startDatePicker></mat-datepicker>
      <mat-error *ngIf="start_date.touched && start_date.invalid">
        <small class="mat-text-warn" *ngIf="start_date?.errors.required">Start date is required.</small>
      </mat-error>
    </mat-form-field>

    <div fxLayout="row" class="half-day-section">
      <div fxFlex>
        <mat-slide-toggle [disabled]="!leave.fromDate" name="halfDayToggle" [(ngModel)]="leave.halfDay" color="warn"
                          (change)="calculateLeaveDates('halfDay')">Half Day</mat-slide-toggle>
      </div>
    </div>

    <mat-form-field *ngIf="leave.halfDay">
      <mat-label>Select Day Half</mat-label>
      <mat-select #dayHalfInput="ngModel" name="selectedDayHalf" [(ngModel)]="leave.whichHalf" required>
        <mat-option *ngFor="let half of dayHalf" [value]="half.value">
          {{half.name}}
        </mat-option>
      </mat-select>
      <mat-error *ngIf="dayHalfInput.touched && dayHalfInput.invalid">
        <small class="mat-text-warn" *ngIf="dayHalfInput?.errors.required">Day half is required.</small>
      </mat-error>
    </mat-form-field>

    <mat-form-field>
      <input matInput [matDatepicker]="endDatePicker" placeholder="Choose a End Date" name="end_date" aria-label="end-date"
             #end_date="ngModel" [(ngModel)]="leave.toDate" [min]="leave.fromDate" (click)="endDatePicker.open()"
             [required]="!leave.noOfDay" [disabled]="leave.halfDay" (dateChange)="calculateLeaveDates('end_date')">
      <mat-datepicker-toggle matSuffix [for]="endDatePicker"></mat-datepicker-toggle>
      <mat-datepicker #endDatePicker></mat-datepicker>
      <mat-error *ngIf="end_date.touched && end_date.invalid">
        <small class="mat-text-warn" *ngIf="end_date?.errors.required">End Date is required.</small></mat-error>
    </mat-form-field>
    <mat-form-field>
      <input matInput type="number" placeholder="No of days" min="0" name="noOfDays" pattern="[1-9]{1}[0-9]*" aria-label="days"
             [(ngModel)]="leave.noOfDay" #noOfDays="ngModel" [required]="!leave.toDate" [disabled]="leave.halfDay"
             (change)="calculateLeaveDates('noOfDay')">
      <mat-error *ngIf="noOfDays.touched && noOfDays.invalid">
        <small class="mat-text-warn" *ngIf="noOfDays?.errors.required">Day is required.</small>
        <small class="mat-text-warn" *ngIf="noOfDays?.errors.pattern">Days between 0 and 20.</small>
      </mat-error>
    </mat-form-field>
    <mat-form-field>
      <mat-select placeholder="Select type of leave" name="leaveType" [(ngModel)]="leave.leaveType" #leaveType="ngModel"
                  required>
        <mat-option *ngFor="let leaveType of leaveTypes" [value]="leaveType">{{leaveType}}</mat-option>
      </mat-select>
      <mat-error *ngIf="leaveType.touched && leaveType.invalid">
        <small class="mat-text-warn" *ngIf="leaveType?.errors.required">Leave Type is required.</small>
      </mat-error>
    </mat-form-field>
    <mat-form-field>
      <textarea matInput placeholder="Reason" name="leave_Reason" maxlength="255" minlength="1" aria-label="reason"
                [(ngModel)]="leave.leaveReason" (change)="leave.leaveReason = leave.leaveReason.trim()" #leaveReason="ngModel"
                required></textarea>
      <mat-error *ngIf="leaveReason.touched && leaveReason.invalid">
        <small class="mat-text-warn" *ngIf="leaveReason?.errors.required">Leave Reason is required.</small>
      </mat-error>
    </mat-form-field>
  </form>
</mat-dialog-content>
<hr>

<mat-dialog-actions fxLayoutAlign="end">
  <button class="bt-flat" type="submit" (click)="closeDialog()">Cancel</button>
  <button mat-raised-button class="bt-sfl" type="submit" (click)="saveLeave()"
    [style.cursor]="leaveUpdateForm.form.invalid ? 'not-allowed' : 'pointer'" [disabled]="leaveUpdateForm.form.invalid">
    Save
  </button>
</mat-dialog-actions>
