<div class="body-content" fxLayout="column" fxFlex="100">
  <div fxFlex fxLayout="row" fxLayoutAlign="space-between" class="mb-1">
    <mat-card-title class="p-10">Employees Worklog</mat-card-title>
    <div>
      <button mat-raised-button class="bt-sfl mr-10px" (click)="getWorklogExcel()">Export Employee Worklog</button>
      <button mat-raised-button class="bt-sfl" [routerLink]="missedWorklogURL">Worklog Not Filled</button>
    </div>
  </div>

  <hr class="header-divider">

  <form #filter="ngForm" (ngSubmit)="applyFilter()">
    <div fxLayout="row column" fxLayoutAlign="start center" fxFlex="100" class="sfl-card">
      <mat-card-actions class="filter-input ml-1 mr-1">
        <mat-form-field>
          <mat-select placeholder="Select Project" name="projectName" [(ngModel)]="filterWorklog.projectName">
            <mat-option *ngFor="let project of projects" [value]="project.name">{{project?.name}}</mat-option>
          </mat-select>
          <button mat-icon-button matSuffix class="float-center" matTooltip="clear"
                  *ngIf="filterWorklog.projectName" (click)="resetFilter('projectName')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1">
        <mat-form-field>
          <mat-select placeholder="Select Client" name="clientName" [(ngModel)]="filterWorklog.clientName">
            <mat-option *ngFor="let client of clients" [value]="client.clientName">{{client?.clientName}}</mat-option>
          </mat-select>
          <button mat-icon-button matSuffix class="float-center" matTooltip="clear" *ngIf="filterWorklog.clientName"
            (click)="resetFilter('clientName')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1">
        <mat-form-field>
          <mat-select placeholder="Select Work Type" name="workType" [(ngModel)]="filterWorklog.workType">
            <mat-option *ngFor="let workType of workTypes" [value]="workType.worktypes">{{workType?.worktypes}}
            </mat-option>
          </mat-select>
          <button mat-icon-button matSuffix class="float-center" matTooltip="clear" *ngIf="filterWorklog.workType"
            (click)="resetFilter('workType')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1">
        <mat-form-field>
          <mat-select placeholder="Select Username" name="uaaUserId" [(ngModel)]="filterWorklog.uaaUserId">
            <mat-option *ngFor="let user of users" [value]="user.id">{{user?.fullName}}</mat-option>
          </mat-select>
          <button mat-icon-button matSuffix class="float-center" matTooltip="clear" *ngIf="filterWorklog.uaaUserId"
            (click)="resetFilter('uaaUserId')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1">
        <mat-form-field>
          <mat-select placeholder="Date Filter" name="filterBy" [(ngModel)]="filterWorklog.filterBy">
            <mat-option *ngFor="let dateWise of dateWiseFilter" [value]="dateWise.replace(' ', '')">{{dateWise}}
            </mat-option>
          </mat-select>
          <button mat-icon-button matSuffix class="float-center" matTooltip="clear" *ngIf="filterWorklog.filterBy"
            (click)="resetFilter('filterBy')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1" *ngIf="filterWorklog.filterBy === uptoYear">
        <mat-form-field>
          <input matInput type="text" placeholder="Year" name="upToYear" aria-label="year" minlength="4" maxlength="4" pattern="\d*"
                 [required]="filterWorklog.filterBy === uptoYear" [(ngModel)]="filterWorklog.upToYear" #uptoYearInput="ngModel" />
          <button mat-icon-button matSuffix matTooltip="clear" *ngIf="filterWorklog.upToYear"
                  (click)="resetFilter('upToYear')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
          <mat-error *ngIf="uptoYearInput.touched && !uptoYearInput.valid">
            <small class="mat-text-warn" *ngIf="uptoYearInput?.errors.required">Year is required.</small>
            <small class="mat-text-warn"
                   *ngIf="uptoYearInput.errors.minlength || uptoYearInput.errors.maxlength || uptoYearInput.errors.pattern">
              Invalid Year.</small>
          </mat-error>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1" *ngIf="filterWorklog.filterBy === freeSearch">
        <mat-form-field>
          <input matInput placeholder="Start date" name="startDate" aria-label="start-date" (focus)="sDate.open()" (click)="sDate.open()"
                 [matDatepicker]="sDate" [max]="filterWorklog.endDate"
                 [required]="filterWorklog.filterBy === freeSearch" [(ngModel)]="filterWorklog.startDate">
          <mat-datepicker-toggle matSuffix [for]="sDate"></mat-datepicker-toggle>
          <mat-datepicker #sDate></mat-datepicker>
          <button mat-icon-button matSuffix class="float-center" matTooltip="clear" *ngIf="filterWorklog.startDate"
            (click)="resetFilter('startDate')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1" *ngIf="filterWorklog.filterBy === freeSearch">
        <mat-form-field>
          <input matInput placeholder="End date" name="endDate" aria-label="end-date" (focus)="eDate.open()" (click)="eDate.open()"
                 [matDatepicker]="eDate" [min]="filterWorklog.startDate"
                 [required]="filterWorklog.filterBy === freeSearch" [(ngModel)]="filterWorklog.endDate">
          <mat-datepicker-toggle matSuffix [for]="eDate"></mat-datepicker-toggle>
          <mat-datepicker #eDate></mat-datepicker>
          <button mat-icon-button matSuffix class="float-center" matTooltip="clear" *ngIf="filterWorklog.endDate"
            (click)="resetFilter('endDate')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-button-group ml-1">
        <button mat-raised-button class="bt-sfl mr-10px" type="submit"
                [disabled]="filter.form.invalid">Filter</button>
        <button class="bt-flat" type="button" (click)="resetAllFilter()">Reset All</button>
      </mat-card-actions>
    </div>
  </form>

  <div class="sfl-card">
    <div class="p-25" fxLayoutAlign="center center" fxFlex="100" *ngIf="showLoader">
      <mat-progress-spinner color='warn' mode="indeterminate"></mat-progress-spinner>
    </div>

    <div *ngIf="!showLoader && noDataFound" class="w-100 mt-40 no-data-found-text">No Worklog Found.</div>

    <div class="w-100" *ngIf="!showLoader && !noDataFound">
      <table class="w-auto" mat-table matSort matSortDisableClear [dataSource]="dataSource"
             [matSortActive]="pageable.sort" [matSortDirection]="pageable.direction" (matSortChange)="getSorting($event)">
        <ng-container matColumnDef="projectName">
          <mat-header-cell *matHeaderCellDef mat-sort-header> Project Name </mat-header-cell>
          <mat-cell *matCellDef="let element">
            {{element?.projectName}}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="worktype">
          <mat-header-cell *matHeaderCellDef mat-sort-header> Work Type </mat-header-cell>
          <mat-cell *matCellDef="let element">
            {{element?.worktypeName}}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="userName">
          <mat-header-cell *matHeaderCellDef mat-sort-header> User Name </mat-header-cell>
          <mat-cell *matCellDef="let element">
            {{element?.userName}}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="date">
          <mat-header-cell *matHeaderCellDef mat-sort-header> Date </mat-header-cell>
          <mat-cell *matCellDef="let element">
            {{element?.date | dateToIst | date: dateFormat}} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="hours">
          <mat-header-cell *matHeaderCellDef mat-sort-header> Hour </mat-header-cell>
          <mat-cell *matCellDef="let element">
            {{element?.hours}} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="note">
          <mat-header-cell *matHeaderCellDef mat-sort-header> Description </mat-header-cell>
          <mat-cell *matCellDef="let element">
            <span matTooltipClass="table-mat-tooltip" [matTooltip]="element?.note">
              {{element?.note}}
            </span>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
      </table>
      <mat-paginator [length]="worklogPageable.totalElements" [pageSizeOptions]="[5, 20, 25]"
        [pageIndex]="pageable.page" [pageSize]="pageable.size" (page)="getPagination($event)" showFirstLastButtons>
      </mat-paginator>
    </div>
  </div>

</div>
