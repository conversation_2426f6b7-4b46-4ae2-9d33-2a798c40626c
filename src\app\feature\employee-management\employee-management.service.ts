import { Injectable } from '@angular/core';
import { AppConfig } from '../../app.config';
import {HttpClientService} from '../../shared';
import { Employee } from './employee-management.model';


@Injectable()
export class EmployeeManagementService {

  constructor(private http: HttpClientService) { }

  getEmployees() {
    return this.http.get(AppConfig.GET_EMPLOYEE_DETAIL + "Active");
  }

  getUserBySearchTerm(searchTerm: string) {
    return this.http.get(AppConfig.SEARCH_EMPLOYEE_DETAIL + searchTerm);
  }

  updateEmployeeStatus(employee: Employee) {
    return this.http.patch(AppConfig.EMPLOYEE_MANAGEMENT_DETAIL.concat('/').concat(String(employee.id)), employee );
  }

  getEmployeeById(id: number) {
    return this.http.get(AppConfig.GET_EMPLOYEE_MANAGEMENT_DETAIL.concat('/').concat(String(id)));
  }

}
