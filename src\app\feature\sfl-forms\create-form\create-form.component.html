<div class="body-content" fxLayout="column" fxFlex="100">
  <div fxFlex fxLayout="row" fxLayoutAlign="space-between" class="mb-1">
    <mat-card-title class="p-10">Forms Template</mat-card-title>
  </div>

  <hr class="header-divider">

  <div class="sfl-card" fxLayout="column">
    <form #createForm="ngForm">
      <mat-vertical-stepper linear>

        <mat-step label="Basic Form Details">
          <mat-form-field fxFlex.gt-lg="60" fxFlex.gt-md="75" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
            <input matInput type="text" placeholder="Form Name" name="formName" aria-label="form-name"
                   [(ngModel)]="sflFormsModel.formName" #formName="ngModel" required>
            <mat-error *ngIf="formName.touched && formName.invalid">
              <small class="mat-text-warn" *ngIf="formName?.errors.required">Form Name is required.</small>
            </mat-error>
          </mat-form-field>
          <mat-form-field fxFlex.gt-lg="60" fxFlex.gt-md="75" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
            <input matInput type="text" placeholder="Form Type" name="formType" aria-label="form-type" [(ngModel)]="sflFormsModel.formType"
                   (change)="checkFormType()" #formType="ngModel" [disabled]="formID !== null" [required]="formID === null">
            <mat-error *ngIf="formType.touched && formType.invalid">
              <small class="mat-text-warn" *ngIf="formType?.errors?.required">Form Type is required.</small>
            </mat-error>
            <div class="mat-form-field-subscript-wrapper" style="margin-top: 1.54167em;" *ngIf="isFormTypeExist">
              <span class="mat-error" role="alert">
                <small class="mat-text-warn ng-star-inserted">Form Type is already exist.</small>
              </span>
            </div>
          </mat-form-field>
          <mat-form-field fxFlex.gt-lg="60" fxFlex.gt-md="75" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
            <textarea matInput placeholder="Form Description" name="description" aria-label="description"
                      [(ngModel)]="sflFormsModel.description" maxlength="255" minlength="1"></textarea>
          </mat-form-field>
          <mat-form-field fxFlex.gt-lg="60" fxFlex.gt-md="75" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
            <mat-chip-list #generatorList>
              <mat-chip *ngFor="let generator of sflFormsModel.generators" (removed)="removeGenerator(generator)">
                {{generator | replaceString: '-' | titlecase}}
                <mat-icon matChipRemove>cancel</mat-icon>
              </mat-chip>
              <input name="generators" placeholder="Who can generate this form" aria-label="generator" [matAutocomplete]="auto" [matChipInputFor]="generatorList">
            </mat-chip-list>
            <mat-autocomplete #auto="matAutocomplete" (optionSelected)="selectedGenerator($event)">
              <mat-option *ngFor="let generator of formGenerator" [value]="generator">
                {{generator | replaceString: '-' | titlecase}}
              </mat-option>
            </mat-autocomplete>
          </mat-form-field>
          <div>
            <button type="button" mat-button class="bt-sfl" matStepperNext>Next</button>
          </div>
        </mat-step>

        <mat-step label="Add Questions">
          <mat-card class="question-card wd-fit-content mb-1" *ngFor="let question of sflFormsModel.questionsDTOS;  let i=index">

            <div class="mb-1" fxLayout="row column" fxLayoutAlign="start">
              <div class="pt-10" fxLayout="row column" fxFlex="5">{{i+1}}.</div>
              <div fxLayout="row column" fxFlex="90">
                <mat-form-field>
                  <input matInput placeholder="Input your question title here" aria-label="q-title"
                         [(ngModel)]="question.text" name="questionText_{{i}}" required #questionText="ngModel">
                  <mat-error *ngIf="questionText.touched && questionText.invalid">
                    <small class="mat-text-warn" *ngIf="questionText?.errors.required">Question Text is required.</small>
                  </mat-error>
                </mat-form-field>

                <mat-form-field *ngIf="question.type === questionTypes.TEXT">
                  <input matInput placeholder="Enter your answer" aria-label="q-text" readonly>
                </mat-form-field>

                <mat-form-field *ngIf="question.type === questionTypes.TEXT_AREA">
                  <textarea matInput placeholder="Enter your answer" aria-label="q-textarea" readonly></textarea>
                </mat-form-field>

                <div *ngIf="question.type === questionTypes.RADIO">
                  <div *ngFor="let option of question.optionsDTOS; let j=index">
                    <mat-radio-group class="d-inline-block">
                      <mat-radio-button disabled></mat-radio-button>
                    </mat-radio-group>

                    <mat-form-field class="w-250px">
                      <input matInput placeholder="Please enter a name for this option" aria-label="q-radio-title"
                             [(ngModel)]="option.optionName" name="optionName_{{i}}_{{j}}" required #optionName="ngModel">
                      <mat-error *ngIf="optionName.touched && optionName.invalid">
                        <small class="mat-text-warn" *ngIf="optionName?.errors.required">Option Text is required.</small>
                      </mat-error>
                    </mat-form-field>

                    <mat-icon class="cursor-pointer ml-1 v-align-middle" (click)="removeOption(i, j)" matTooltip="Remove">delete</mat-icon>
                    <mat-icon *ngIf="j > 0" class="cursor-pointer ml-1 v-align-middle" (click)="moveOption(i, j, j-1)" matTooltip="Move option up">arrow_upward</mat-icon>
                    <mat-icon *ngIf="j < (sflFormsModel.questionsDTOS[i].optionsDTOS.length - 1)" class="cursor-pointer ml-1 v-align-middle" (click)="moveOption(i, j, j+1)" matTooltip="Move option down">arrow_downward</mat-icon>
                  </div>

                  <button mat-raised-button (click)="addOption(i)">
                    <mat-icon>add</mat-icon>
                    <span class="ml-1">Add option</span>
                  </button>
                </div>

                <table *ngIf="question.type === questionTypes.MULTI_RADIO">
                    <thead>
                      <tr>
                        <th></th>
                        <th class="px-1" *ngFor="let option of question.optionsDTOS; let j=index">
                          <mat-form-field class="w-100px">
                            <input matInput placeholder="Option {{j+1}}" aria-label="q-multi-option"
                                   [(ngModel)]="option.optionName" name="optionName_{{i}}_{{j}}" required #optionName="ngModel">
                            <mat-error *ngIf="optionName.touched && optionName.invalid">
                              <small class="mat-text-warn" *ngIf="optionName?.errors.required">Option Text is required.</small>
                            </mat-error>
                            <button mat-icon-button matSuffix matTooltip="Remove" (click)="removeOption(i, j)">
                              <mat-icon class="filter-clear-icon">delete</mat-icon>
                            </button>
                          </mat-form-field>
                        </th>

                        <th class="px-1">
                          <button mat-raised-button (click)="addOption(i)">
                            <mat-icon>add</mat-icon>
                          </button>
                        </th>
                      </tr>
                    </thead>

                    <tbody>
                      <tr *ngFor="let subQuestionDTO of question.subQuestionsDTOS; let j=index">
                        <td>
                          <mat-form-field>
                            <input matInput placeholder="Statement {{j+1}}" aria-label="sub-question"
                                   [(ngModel)]="subQuestionDTO.text" name="subQuestion_{{i}}_{{j}}" required #subQuestion="ngModel">
                            <mat-error *ngIf="subQuestion.touched && subQuestion.invalid">
                              <small class="mat-text-warn" *ngIf="subQuestion?.errors.required">Statement is required.</small>
                            </mat-error>
                          </mat-form-field>
                        </td>
                        <td class="text-center" *ngFor="let option of question.optionsDTOS">
                          <mat-radio-group>
                            <mat-radio-button disabled></mat-radio-button>
                          </mat-radio-group>
                        </td>
                        <td class="px-1">
                          <div class="d-inline-block w-max-content">
                            <mat-icon class="cursor-pointer v-align-middle" (click)="removeSubQuestion(i, j)" matTooltip="Remove">delete</mat-icon>
                            <mat-icon *ngIf="j > 0" class="cursor-pointer ml-1 v-align-middle" (click)="moveSubQuestion(i, j, j-1)" matTooltip="Move up">arrow_upward</mat-icon>
                            <mat-icon *ngIf="j < (sflFormsModel.questionsDTOS[i].subQuestionsDTOS.length - 1)" class="cursor-pointer ml-1 v-align-middle" (click)="moveSubQuestion(i, j, j+1)" matTooltip="Move down">arrow_downward</mat-icon>
                          </div>
                        </td>
                      </tr>
                      <tr>
                        <td>
                          <button mat-raised-button (click)="addSubQuestion(i)">
                            <mat-icon>add</mat-icon>
                            <span class="ml-1">Add statement</span>
                          </button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
              </div>
            </div>

            <hr class="header-divider">

            <div fxLayout="row">
              <div class="question-ml" fxFlex.gt-lg="50" fxFlex.gt-md="50" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                <mat-form-field>
                  <input matInput placeholder="Input your question hint here" aria-label="q-hint" [(ngModel)]="question.hint" name="questionHint_{{i}}">
                </mat-form-field>
              </div>
              <div fxLayoutAlign="end center" fxFlex.gt-lg="25" fxFlex.gt-md="25" fxFlex.gt-sm="50" fxFlex.gt-xs="50">
                <mat-slide-toggle name="questionRequired_{{i}}" [(ngModel)]="question.required" color="warn">Required</mat-slide-toggle>
              </div>
              <div fxLayoutAlign="end center" fxFlex.gt-lg="25" fxFlex.gt-md="25" fxFlex.gt-sm="50" fxFlex.gt-xs="50">
                <mat-icon color="warn" class="cursor-pointer ml-1" (click)="removeQuestion(i)" matTooltip="Remove">delete</mat-icon>
                <mat-icon *ngIf="i > 0" class="cursor-pointer ml-1" (click)="moveQuestion(i, i-1)" matTooltip="Move question up">arrow_upward</mat-icon>
                <mat-icon *ngIf="i < (sflFormsModel.questionsDTOS.length - 1)" class="cursor-pointer ml-1" (click)="moveQuestion(i, i+1)" matTooltip="Move question down">arrow_downward</mat-icon>
              </div>
            </div>
          </mat-card>

          <div class="mt-10px mb-1">
            <button mat-raised-button (click)="changeQuestionAddBtn=!changeQuestionAddBtn">
              <mat-icon>add</mat-icon>
              <span *ngIf="!changeQuestionAddBtn" class="ml-1">Add new</span>
            </button>

            <mat-button-toggle-group *ngIf="changeQuestionAddBtn" class="ml-1" name="questionTypes">
              <mat-button-toggle
                *ngFor="let questionTypeValue of questionTypes.DROP_DOWN"
                (click)="addQuestion(questionTypeValue); changeQuestionAddBtn=!changeQuestionAddBtn"
                value="questionTypeValue">
                {{questionTypeValue | replaceString: '_' | titlecase}}
              </mat-button-toggle>
            </mat-button-toggle-group>
          </div>

          <div class="mt-10px">
            <button type="button" mat-button matStepperPrevious>Back</button>
            <button type="button" mat-button class="bt-sfl" matStepperNext>Next</button>
          </div>
        </mat-step>

        <mat-step label="Preview the form">
          You are now done.
          <div>
            <button type="button" mat-raised-button (click)="previewForm()" [style.cursor]="createForm.form.invalid ? 'not-allowed' : 'pointer'" [disabled]="createForm.form.invalid">Preview this form</button>
          </div>
          <div class="mt-40">
            <button type="button" mat-button matStepperPrevious>Back</button>
          </div>
        </mat-step>

      </mat-vertical-stepper>
    </form>
  </div>

  <div class="p-10" fxLayoutAlign="end">
    <button class="bt-flat" type="submit" [routerLink]="formURL">Cancel</button>
    <button mat-raised-button class="bt-sfl" type="submit" (click)="saveForm()"
            [style.cursor]="createForm.form.invalid ? 'not-allowed' : 'pointer'" [disabled]="createForm.form.invalid">
      Save
    </button>
  </div>
</div>
