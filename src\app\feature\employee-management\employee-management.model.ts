import { Variable } from "../../shared";
import { Address, UserLevelDto } from "../profile/profile.model";

export class Employee {
  id: number;
  firstName: string;
  lastName: string;
  fullName: string;
  email: string;
  birthDate: Date;
  joinDate: Date;
  phoneNo: number;
  imageUrl: string;
  userName: string;
  addresses: Address[];
  secondaryPhoneNo: string;
  maritalStatus: boolean;
  anniversaryDate: Date;
  status: boolean;
  designationId: number;
  designationName: string;
  file: any;
  userLevelDTO: UserLevelDto;
  primaryTechnology: string;
  showInSchedule: boolean;
  authorityId: number;
}

export class EmployeeRequest {
  id: number;
  login: string;
  firstName: string;
  lastName: string;
  activated: boolean;
  email: string;
  authorityId: number;
  phoneNo: number;
  designationId: number;
  designationName?: string;
  langKey?: string;
  createdBy?: string;
  createdDate?: string;
  imageUrl?: string;
  lastModifiedBy?: string;
  lastModifiedDate?: string;
}

export class Search {
  id: number;
  firstName: string;
  lastName: string;
  userName: string;
  searchTerm: string;
}

export const EMPLOYEE_LIST_DISPLAY_COLUMNS = ["fullName", Variable.EMAIL, Variable.PHONE_NO, "userLevelDTO.empLevelId"];
export const SUPERADMIN_EMPLOYEE_LIST_DISPLAY_COLUMNS = ["fullName", Variable.EMAIL, Variable.PHONE_NO, "userLevelDTO.empLevelId", Variable.STATUS, Variable.ACTION];
