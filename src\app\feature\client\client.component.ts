import { Component, OnInit, <PERSON><PERSON>hild, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { MatDialog, MatSort, MatPaginator, MatTableDataSource } from '@angular/material';
import { Subscription } from 'rxjs';
import { PageableQuery, Variable } from '../../shared';
import { ClientService } from './client.service';
import { ClientModalComponent } from './client-modal/client-modal.component';
import { Client, FilterClient, ClientPageable, CLIENT_DISPLAY_COLUMNS } from './client.model';
import {finalize} from 'rxjs/operators';


@Component({
  selector: 'sfl-client',
  templateUrl: './client.component.html'
})
export class ClientComponent implements OnInit, OnDestroy {
  @ViewChild(MatSort, {static: false}) sort: MatSort;
  @ViewChild(MatPaginator, {static: false}) paginator: MatPaginator;

  showLoader = true;
  noDataFound = true;
  displayedColumns = CLIENT_DISPLAY_COLUMNS;
  clients: Client[] = [];
  dataSource = new MatTableDataSource<Client>();
  subscription: Subscription = new Subscription();
  clientPageable: ClientPageable = new ClientPageable();
  pageable: PageableQuery = new PageableQuery();
  filterClient: FilterClient = new FilterClient();

  constructor(
    private matDialog: MatDialog,
    private clientService: ClientService,
  ) {
    this.pageable.page = 0;
    this.pageable.size = 10;
    this.pageable.sort = Variable.CLIENT_NAME;
    this.pageable.direction = Variable.ASC;
  }

  ngOnInit() {
    this.onLoadData();
    this.subscription.add(this.clientService.invokeEvent.subscribe(() => {
      this.onLoadData();
    }));
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

  onLoadData() {
    this.getClient();
    this.applyFilter();
  }

  saveClient(client: Client) {
    this.matDialog.open(ClientModalComponent, {
      width: Variable.BOX_WIDTH_VALUE,
      data: client
    });
  }

  getSorting(event) {
    this.pageable.sort = event.active;
    this.pageable.direction = event.direction;
    this.pageable.page = 0;
    this.applyFilter(false);
  }

  getPagination(event) {
    this.pageable.size = event.pageSize;
    this.pageable.page = event.pageIndex;
    this.applyFilter(false);
  }

  applyFilter(set_page_zero = true) {
    this.pageable.page = set_page_zero ? 0 : this.pageable.page;

    this.showLoader = true;
    this.noDataFound = true;
    const currentSort = this.pageable.sort;
    this.pageable.sort = this.pageable.sort + ',' + this.pageable.direction;
    this.subscription.add(this.clientService.getFilterClients(this.filterClient, this.pageable)
      .pipe(
        finalize(() => {
          this.noDataFound = this.dataSource.data.length <= 0;
          this.showLoader = false;
        })
      )
      .subscribe((res: ClientPageable) => {
        this.clientPageable = res;
        this.dataSource = new MatTableDataSource<Client>(this.clientPageable.content);
        this.pageable.size = this.clientPageable.pageable.pageSize;
        this.pageable.page = this.clientPageable.pageable.pageNumber;
      })
    );
    this.pageable.sort = currentSort;
  }

  resetFilter(parameter: string) {
    this.filterClient[parameter] = undefined;
  }

  resetAllFilter() {
    this.filterClient = new FilterClient();
    this.applyFilter();
  }

  getClient() {
    this.subscription.add(this.clientService.getClients().subscribe((res: Client[]) => {
      this.clients = res;
    }));
  }

  updateClientStatus(element: Client) {
    this.subscription.add(
      this.clientService.updateClient(element).subscribe(() => { })
    );
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
