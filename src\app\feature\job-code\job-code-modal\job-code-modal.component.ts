import { Component, Inject, <PERSON><PERSON><PERSON><PERSON>, OnInit } from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material";
import { Subscription, forkJoin } from "rxjs";
import { SnackBarService, Variable } from "src/app/shared";
import { Client } from "../../client/client.model";
import { ClientService } from "../../client/client.service";
import { Project } from "../../project/project.model";
import { ProjectService } from "../../project/project.service";
import { Technology } from "../../technology/technology.model";
import { TechnologyService } from "../../technology/technology.service";
import { JobCode, JobCodeModal } from "../job-code";
import { JobCodeComponent } from "../job-code.component";
import { JobCodeService } from "../job-code.service";

@Component({
  selector: "app-job-code-modal",
  templateUrl: "./job-code-modal.component.html",
  styleUrls: ["./job-code-modal.component.css"],
})
export class JobCodeModalComponent implements OnInit, OnDestroy {
  subscription: Subscription = new Subscription();
  isJobCodeEdit = false;
  isSaving = false;
  isViewMode = false;
  isEditMode = false;
  dataLoaded = false;
  projects: Project[] = [];
  clients: Client[] = [];
  technologies: Technology[] = [];
  jobCodeData: JobCode;
  title = "Add Job Code";
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: JobCodeModal,
    public dialogRef: MatDialogRef<JobCodeComponent>,
    private projectService: ProjectService,
    private technologyService: TechnologyService,
    private jobCodeService: JobCodeService,
    private snackBarService: SnackBarService,
    private clientService: ClientService
  ) {}

  ngOnInit(): void {
    this.jobCodeData = new JobCode();
    if (this.data?.obj?.id) {
      this.isEditMode = this.data?.isEdit;
      this.isViewMode = !this.isEditMode;
      const { id, name, clientId, projectId, technologyId, tagName, isActive, isBillable, description } = this.data.obj;
      this.jobCodeData = {
        id,
        name,
        clientId,
        projectId,
        technologyId,
        tagName,
        isActive,
        isBillable,
        description,
      };
      if (this.isEditMode) {
        this.title = "Edit Job Code";
      } else {
        this.title = "View Job Code";
      }
    }
    this.getData();
  }

  getData() {
    this.subscription.add(
      forkJoin([this.getClients(), this.getProjects(), this.getTechnologies()]).subscribe(([clients, projects, technologies]: [Client[], Project[], Technology[]]) => {
        this.clients = clients;
        this.projects = projects;
        this.technologies = technologies;
        this.dataLoaded = true;
      })
    );
  }

  getClients() {
    return this.clientService.getClients();
  }

  getProjects() {
    return this.projectService.getActiveProjects();
  }

  getTechnologies() {
    return this.technologyService.getTechnologies();
  }

  setJobCode() {
    setTimeout(() => {
      const jobCodeAbbreviationArr: string[] = [];
      if (this.jobCodeData?.clientId) {
        const name = this.clients.find((client) => client.id === this.jobCodeData.clientId).clientName;
        jobCodeAbbreviationArr.push(this.getFirstThreeAlphaNumeric(name));
      }
      if (this.jobCodeData?.projectId) {
        const name = this.projects.find((project) => project.id === this.jobCodeData.projectId).name;
        jobCodeAbbreviationArr.push(this.getFirstThreeAlphaNumeric(name));
      }
      if (this.jobCodeData?.technologyId) {
        const name = this.technologies.find((tech) => tech.id === this.jobCodeData.technologyId).name;
        jobCodeAbbreviationArr.push(this.getFirstThreeAlphaNumeric(name));
      }
      this.jobCodeData.name = jobCodeAbbreviationArr.join("_");
    }, 0);
  }

  private getFirstThreeAlphaNumeric(str: string): string {
    let strWithCharAndNum = str.replace(/[^a-zA-Z0-9]/g, "").toUpperCase();
    if (strWithCharAndNum.length > 3) {
      return strWithCharAndNum.substring(0, 3);
    } else {
      return strWithCharAndNum;
    }
  }

  closeDialog(dataModified = false): void {
    this.isEditMode = false;
    this.isViewMode = false;
    this.dialogRef.close(dataModified);
  }

  saveJobCode() {
    this.isSaving = true;
    if (this.isViewMode) {
      this.isViewMode = false;
      this.isEditMode = true;
      this.title = "Edit Job Code";
      this.isSaving = false;
      return;
    }
    if (this.isEditMode) {
      this.updateJobCode();
    } else {
      this.addNewJobCode();
    }
  }

  addNewJobCode() {
    this.subscription.add(
      this.jobCodeService.saveJobCode(this.jobCodeData).subscribe(() => {
        this.snackBarService.success(Variable.JOB_CODE_ADDED);
        this.isSaving = false;
        this.closeDialog(true);
      })
    );
  }

  updateJobCode() {
    this.subscription.add(
      this.jobCodeService.updateJobCode(this.jobCodeData).subscribe(() => {
        this.snackBarService.success(Variable.JOB_CODE_UPDATED);
        this.isSaving = false;
        this.closeDialog(true);
      })
    );
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
