import { DatePipe } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource } from '@angular/material';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { DateUtils, PageableQuery, SharedService } from 'src/app/shared';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { ClockInOutDTO } from '../dashboard/dashboard.model';
import { DashboardService } from '../dashboard/dashboard.service';
import { DAILY_ATTENDANCE_DISPLAY_COLUMNS, FilterAttendance, FilterWorklog, Worklog, WorklogPageable, WORKLOG_DISPLAY_COLUMNS, WorkTypes } from '../worklog/worklog.model';
import { ClockInOutPageable } from './emp-daily-attendance.model';

@Component({
  selector: 'app-emp-daily-attendance',
  templateUrl: './emp-daily-attendance.component.html',
  styleUrls: ['./emp-daily-attendance.component.css']
})
export class EmpDailyAttendanceComponent implements OnInit {

  @ViewChild(MatSort, {static: false}) sort: MatSort;
  @ViewChild(MatPaginator, {static: false}) paginator: MatPaginator;

  showLoader = true;
  noDataFound = true;
  uaaUserId: number;
  displayedColumns: string[];
  dateFormat = Variable.MM_DD_YYYY;
  dateWiseFilter: string[] = Variable.DATE_WISE_FILTER_LIST;
  filterWorklog: FilterAttendance = new FilterAttendance();
  pageable: PageableQuery = new PageableQuery();
  dataSource = new MatTableDataSource<ClockInOutDTO>([]);
  subscription: Subscription = new Subscription();
  freeSearch = Variable.FREE_SEARCH.replace(' ', '');
  uptoYear = Variable.UPTO_YEAR.replace(' ', '');
  empAttandance: ClockInOutPageable = new ClockInOutPageable();
  isButtonVisible = true;
  empAttendanceDTO: ClockInOutDTO = new ClockInOutDTO();
  selectedStartDate: string;
  selectedEndDate: string;

  constructor(
    private myDashboardService: DashboardService,
    private sharedService: SharedService,
    private datePipe: DatePipe
  ) { }

  ngOnInit() {
    this.onLoadData();
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

  onLoadData() {
    this.getFilterList();
  }

  filterList(set_page_zero = true) {
    this.pageable.page = set_page_zero ? 0 : this.pageable.page;
    this.showLoader = true;
    this.noDataFound = true;
    const currentSort = this.pageable.sort;
    this.filterWorklog.filterBy = this.freeSearch;
    this.pageable.sort = this.pageable.sort + ',' + this.pageable.direction;
    if (this.filterWorklog.filterBy !== this.freeSearch) {
      this.filterWorklog.clockIn = undefined;
      this.filterWorklog.clockOut = undefined;
    }
    this.filterWorklog.clockIn = this.datePipe.transform(this.selectedStartDate, "yyyy-MM-dd'T'HH:mm:ss") + ".000Z";
    this.filterWorklog.clockOut = this.datePipe.transform(this.selectedEndDate, "yyyy-MM-dd'T'23:59:00") + ".000Z";
    this.filterWorklog.uaaUserId = this.sharedService.getUserId();
    this.subscription.add(this.myDashboardService.empClockInOutPageable(this.filterWorklog, this.pageable)
      .pipe(
        finalize(() => {
          this.noDataFound = this.dataSource.data.length <= 0;
          this.showLoader = false;
        })
      )
      .subscribe((res: ClockInOutPageable) => {
        this.empAttandance = res;
        this.displayedColumns = DAILY_ATTENDANCE_DISPLAY_COLUMNS;
        this.dataSource = new MatTableDataSource<ClockInOutDTO>(res.content);
        this.pageable.size = this.empAttandance.pageable.pageSize;
        this.pageable.page = this.empAttandance.pageable.pageNumber;
      })
    );
    this.pageable.sort = currentSort;
    this.filterWorklog.clockIn = this.datePipe.transform(this.selectedStartDate, "yyyy-MM-dd'T'HH:mm:ss");
    this.filterWorklog.clockOut = this.datePipe.transform(this.selectedEndDate, "yyyy-MM-dd'T'HH:mm:ss");
  }

  getFilterList(set_page_zero = true) {
    this.pageable.page = set_page_zero ? 0 : this.pageable.page;
    this.showLoader = true;
    this.noDataFound = true;
    const currentSort = this.pageable.sort;
    this.filterWorklog.filterBy = this.freeSearch;
    this.pageable.sort = 'clockIn' + ',' + 'desc';
    if (this.filterWorklog.filterBy !== this.freeSearch) {
      this.filterWorklog.clockIn = undefined;
      this.filterWorklog.clockOut = undefined;
    }
    this.filterWorklog.uaaUserId = this.sharedService.getUserId();
    this.subscription.add(this.myDashboardService.empClockInOutPageable(this.filterWorklog, this.pageable)
      .pipe(
        finalize(() => {
          this.noDataFound = this.dataSource.data.length <= 0;
          this.showLoader = false;
        })
      )
      .subscribe((res: ClockInOutPageable) => {
        this.empAttandance = res;
        this.displayedColumns = DAILY_ATTENDANCE_DISPLAY_COLUMNS;
        this.dataSource = new MatTableDataSource<ClockInOutDTO>(res.content);
        this.pageable.size = this.empAttandance.pageable.pageSize;
        this.pageable.page = this.empAttandance.pageable.pageNumber;
      })
    );
    this.pageable.sort = currentSort;
    this.filterWorklog.clockIn = this.datePipe.transform(this.selectedStartDate, "yyyy-MM-dd'T'HH:mm:ss");
    this.filterWorklog.clockOut = this.datePipe.transform(this.selectedEndDate, "yyyy-MM-dd'T'HH:mm:ss");
  }

  getPagination(event) {
    this.pageable.size = event.pageSize;
    this.pageable.page = event.pageIndex;
    this.getFilterList(false);
  }

  resetAllFilter() {
    this.filterWorklog = new FilterAttendance();
    this.selectedStartDate = null;
    this.selectedEndDate = null;
    this.getFilterList();
  }

  getSorting(event) {
    this.pageable.sort = event.active;
    this.pageable.direction = event.direction;
    this.pageable.page = 0;
    this.getFilterList();
  }

  resetFilter(parameter: string) {
    this.filterWorklog[parameter] = undefined;
  }

}
