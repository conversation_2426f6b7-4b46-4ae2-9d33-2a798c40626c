import { Chart } from 'chart.js';
import {Variable} from '../../shared';

export class Utils {
  public static getBarChart(elementId, label, data, title, xTitle, yTitle, responsive?: boolean) {
    return new Chart(elementId, {
      type: Variable.BAR,
      data: {
        labels: label,
        datasets: [
          {
            label: Variable.NUMBER_OF_HOURS,
            data: data,
            backgroundColor: Variable.BASIC_CHART_COLOR
          }
        ]
      },
      options: {
        plugins: {
          labels: {
            fontSize: 0
          }
        },
        responsive: (responsive !== false),
        maintainAspectRatio: false,
        legend: {
          display: false
        },
        title: {
          text: title,
          display: true,
          fontSize: 18
        },
        dataset: {
          maxBarThickness: 25
        },
        scales: {
          xAxes: [
            {
              scaleLabel: {
                display: true,
                labelString: xTitle
              },
              ticks: {
                autoSkip: false
              }
            }
          ],
          yAxes: [
            {
              scaleLabel: {
                labelString: yTitle,
                display: true
              },
              ticks: {
                beginAtZero: true
              }
            }
          ]
        }
      }
    });
  }

  public static getPieChart(elementId, label, data, title) {
    return new Chart(elementId, {
      type: Variable.PIE,
      data: {
        labels: label,
        datasets: [
          {
            label: Variable.NUMBER_OF_HOURS,
            data: data,
            backgroundColor: (() => {
              const bgcolors = [];
              for (let i = 0; i < data.length; ++i) {
                bgcolors.push(Variable.PIE_CHART_COLORS[i % Variable.PIE_CHART_COLORS.length]);
              }
              return bgcolors;
            })()
          }
        ]
      },
      options: {
        tooltips: {
          callbacks: {
            label: function(tooltipItem, data1) {
              const dataset = data1.datasets[tooltipItem.datasetIndex];
              const labelName = data1.labels[tooltipItem.index];
              const total = dataset.data.reduce(function(previousValue, currentValue1) {
                return previousValue + currentValue1;
              });
              const currentValue = dataset.data[tooltipItem.index];
              const percentage = Math.floor((currentValue / total) * 100 + 0.5);
              return labelName + ' ' + percentage + Variable.PERCENTAGE;
            }
          }
        },
        title: {
          text: title,
          display: true,
          fontSize: 18
        },
        legend: {
          position: Variable.BOTTOM
        },
        cutoutPercentage: 50
      }
    });
  }
}
