import { <PERSON>mpo<PERSON>, Inject, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t, ViewChild } from "@angular/core";
import { AbstractControlDirective, NgForm } from "@angular/forms";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material";
import * as _ from "lodash";
import moment from "moment";
import { Subscription } from "rxjs";
import { Messages, SnackBarService, Variable } from "src/app/shared";
import { SweetAlertService } from "src/app/shared/service/sweetalert.service";
import { TimesheetComponent } from "../timesheet.component";
import { Log, ModalMode, WorkLogModalData, WorklogParams } from "../timesheet.model";
import { TimesheetService } from "../timesheet.service";
import { HttpErrorResponse } from "@angular/common/http";

@Component({
  selector: "app-add-edit-worklog",
  templateUrl: "./add-edit-worklog.component.html",
  styleUrls: ["./add-edit-worklog.component.css"],
})
export class AddEditWorklogComponent implements OnInit, OnD<PERSON>roy {
  @ViewChild("workLogForm", { static: false }) workLogForm: NgForm;
  isViewMode = false;
  isEditMode = false;
  title = "Add Worklog";
  dataHasBeenModified = false;
  jobCodeName = "";
  timeInputPattern: RegExp = /^(?:[0-9]+h(?:\s[0-5]?[0-9]m)?|\b[0-9]+h(?:\s[0-5]?[0-9]m)?|[0-5]?[0-9]m)$/;
  worklogData: WorklogParams;
  isSaving = false;
  todaysTotalSeconds = 0;
  deleteParam = {
    childId: "childId",
    parentId: "parentId",
  };
  subscription: Subscription = new Subscription();
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: WorkLogModalData,
    public dialogRef: MatDialogRef<TimesheetComponent>,
    private zone: NgZone,
    private timeSheet: TimesheetService,
    private alertService: SweetAlertService,
    private snackBarService: SnackBarService
  ) {}

  ngOnInit(): void {
    setTimeout(() => {
      this.focusOnTimeInput();
    }, 0);
    this.isEditMode = this.data.mode === ModalMode.EDIT_MODE;
    this.isViewMode = this.data.mode === ModalMode.VIEW_MODE;
    this.jobCodeName = this.data.jobCode.name;
    this.worklogData = new WorklogParams();
    this.worklogData.logDate = moment(this.data.date).format("YYYY-MM-DDTHH:mm:ssZ");
    this.worklogData.jobCodeId = this.data.jobCode.id;
    this.worklogData.uaaUserId = this.data.userId;
    this.worklogData.logs = this.data.worklogData?.logs ? _.cloneDeep(this.data.worklogData.logs) : [];
    this.worklogData.id = this.data.worklogData?.id;
    if (this.data.mode !== ModalMode.ADD_MODE) {
      this.title = this.isEditMode ? "Edit Worklog" : "View Worklog";
      this.setLogTimeField();
      this.setTotalTime();
    } else {
      this.addNewForm();
    }
    if (!this.isViewMode) {
      const todaysDate = moment(this.data.date).format("YYYY-MM-DDTHH:mm:ssZ").split("T")[0];
      this.todaysTotalSeconds = this.data.dailyTotal[`${this.data.userId}`][todaysDate] ?? 0;
    }
  }

  private setLogTimeField(): void {
    this.worklogData.logs.forEach((log) => {
      const hours = Math.floor(log.logTime / 3600);
      const minutes = Math.floor((log.logTime % 3600) / 60);
      const hoursStr = hours > 0 ? hours + "h " : "";
      const minutesStr = minutes > 0 ? minutes + "m" : "";
      log.logTimeStr = (hoursStr + minutesStr).trim();
    });
  }

  addNewForm() {
    this.worklogData.logs.push(this.newLogsForm);
  }

  get newLogsForm(): Log {
    return {
      id: null,
      logTime: null,
      logTimeStr: "",
      ticketLink: "",
      description: "",
      timeTrackId: null,
    };
  }

  checkIfFieldIsInvalid(control: AbstractControlDirective, i: number) {
    if (control.dirty || control.touched) {
      const value = this.worklogData.logs[i].logTimeStr;
      if (!value) {
        return "This field is required";
      }
      if (!this.timeInputPattern.test(value)) {
        return "Invalid format";
      }
      if (this.worklogData.logs[i].logTime > 24 * 60 * 60) {
        return "Max 24h can be logged";
      }
      return "";
    }
    return false;
  }

  closeDialog(): void {
    this.isViewMode = false;
    this.dialogRef.close(this.dataHasBeenModified);
  }

  saveWorkLog(): void {
    if (this.todaysTotalSeconds > 24 * 60 * 60) {
      this.snackBarService.error(Messages.LIMIT_EXCEEDED);
      return;
    }
    this.isSaving = true;
    if (this.isEditMode) {
      this.updateWorklog();
      return;
    }
    this.addNewWorklog();
  }

  private addNewWorklog(): void {
    this.subscription.add(
      this.timeSheet.saveWorklog(this.worklogData).subscribe(
        (res) => {
          this.isSaving = false;
          this.dataHasBeenModified = true;
          this.closeDialog();
        },
        (error: HttpErrorResponse) => {
          this.snackBarService.error(error.error.title);
          this.closeDialog();
        }
      )
    );
  }

  private updateWorklog(): void {
    this.subscription.add(
      this.timeSheet.updateWorklog(this.worklogData).subscribe(
        () => {
          this.isSaving = false;
          this.dataHasBeenModified = true;
          this.closeDialog();
        },
        (error: HttpErrorResponse) => {
          this.snackBarService.error(error.error.title);
          this.closeDialog();
        }
      )
    );
  }

  focusOnTimeInput() {
    this.zone.runOutsideAngular(() => {
      const inputElement = document.querySelector('[id="timeLog-0"]') as HTMLInputElement;
      if (inputElement) {
        inputElement.focus();
      }
    });
  }

  setTotalTime() {
    let totalTimeInSeconds = 0;
    this.worklogData.logs.forEach((log) => {
      let logTimeInSeconds = 0;
      const timeArr = log.logTimeStr.split(" ");
      timeArr.forEach((time) => {
        if (time.includes("h")) {
          const hours = Number(time.split("h")[0]);
          totalTimeInSeconds += hours * 60 * 60;
          logTimeInSeconds += hours * 60 * 60;
        }
        if (time.includes("m")) {
          const minutes = Number(time.split("m")[0]);
          totalTimeInSeconds += minutes * 60;
          logTimeInSeconds += minutes * 60;
        }
      });
      log.logTime = logTimeInSeconds;
    });
    this.worklogData.totalWorkLogTime = totalTimeInSeconds;
    const totalSeconds = totalTimeInSeconds;
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    let logTimeStr = "";
    if (hours) {
      logTimeStr += `${hours}h`;
    }
    if (minutes) {
      logTimeStr += ` ${minutes}m`;
    }
    this.worklogData.totalWorkLogTimeStr = logTimeStr.trim();
  }

  private isFormValid(): boolean {
    if (this.workLogForm) {
      return this.workLogForm.valid;
    }
    return false;
  }

  disableSaveButton(): boolean {
    if (this.isFormValid) {
      return this.worklogData?.logs.some((log) => {
        return !this.timeInputPattern.test(log.logTimeStr) || !log.logTime;
      });
    } else {
      return true;
    }
  }

  deleteLog(param: string, index: number | null, id: number): void {
    const confirmation = this.alertService.deleteAlert(Variable.THIS_WORK_LOG);
    confirmation.then((value) => {
      if (value === true) {
        if (id) {
          this.subscription.add(
            this.timeSheet.deleteWorklog(param, id).subscribe(() => {
              this.dataHasBeenModified = true;
              if (param === this.deleteParam.childId) {
                if (this.todaysTotalSeconds) {
                  this.todaysTotalSeconds = this.todaysTotalSeconds - (this.worklogData.logs[index]?.logTime ?? 0);
                }
                this.worklogData.logs.splice(index, 1);
                this.setTotalTime();
              } else {
                this.closeDialog();
              }
            })
          );
        } else {
          this.worklogData.logs.splice(index, 1);
          this.setTotalTime();
        }
      }
    });
  }

  removeTimeFromTotal(worklogData: Log): void {
    this.todaysTotalSeconds = this.todaysTotalSeconds - worklogData.logTime;
  }
  addTimeInTotal(worklogData: Log): void {
    this.todaysTotalSeconds += worklogData.logTime;
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
