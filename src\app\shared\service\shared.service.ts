import { Injectable } from "@angular/core";
import { Router } from "@angular/router";
import * as FileSaver from "file-saver";
import { LocalStorageService, SessionStorageService } from "ngx-webstorage";
import { AppConfig } from "../../app.config";

@Injectable()
export class SharedService {
  constructor(private $localStorage: LocalStorageService, private $sessionStorage: SessionStorageService, private router: Router) {}

  // Set and Get Role...
  storeRole(role) {
    this.$localStorage.store("role", role);
  }

  getRole() {
    return this.$localStorage.retrieve("role");
  }

  setUserName(username: string) {
    this.$localStorage.store("userName", username);
  }

  getUserName() {
    return this.$localStorage.retrieve("userName");
  }

  setUserToken(token: string) {
    this.$localStorage.store("token", token);
  }

  getUserToken() {
    return this.$localStorage.retrieve("token");
  }

  setUaaUserId(uaaUserId: number) {
    this.$localStorage.store("uaaUserId", uaaUserId);
  }

  getUaaUserId() {
    return this.$localStorage.retrieve("uaaUserId");
  }

  setUserId(userId: string) {
    this.$localStorage.store("userid", userId);
  }
  getUserId() {
    return this.$localStorage.retrieve("userid");
  }

  checkLogin() {
    return this.getUserToken !== null && this.getUserName() !== null && this.getUserId() !== null;
  }

  public isLoggedIn() {
    const token = this.getUserToken();
    return token != null;
  }

  logout() {
    this.setUserId(null);
    this.setUserName(null);
    this.setUserToken(null);
    this.storeRole(null);
    this.$localStorage.clear();
    this.router.navigate([AppConfig.LOGIN]).then(() => {
      window.location.reload();
    });
  }

  static saveAsExcelFile(buffer: any, fileName: string): void {
    const EXCEL_TYPE = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8";
    const EXCEL_EXTENSION = ".xlsx";
    const data: Blob = new Blob([buffer], {
      type: EXCEL_TYPE,
    });
    FileSaver.saveAs(data, `${fileName}${EXCEL_EXTENSION}`, { autoBom: false });
  }
}
