import { Injectable } from "@angular/core";
import { Router } from "@angular/router";
import * as FileSaver from "file-saver";
import { LocalStorageService, SessionStorageService } from "ngx-webstorage";
import { AppConfig } from "../../app.config";
import { RefreshTokenService, TokenData } from "./refresh-token.service";

@Injectable()
export class SharedService {
  private isInitialized = false;

  constructor(
    private $localStorage: LocalStorageService,
    private $sessionStorage: SessionStorageService,
    private router: Router,
    private refreshTokenService: RefreshTokenService
  ) {}

  /**
   * Initialize refresh token service if not already done
   */
  private ensureInitialized(): void {
    if (!this.isInitialized) {
      this.isInitialized = true;
      // Defer initialization to avoid blocking
      setTimeout(() => {
        this.refreshTokenService.initializeOnStartup();
      }, 500);
    }
  }

  // Set and Get Role...
  storeRole(role) {
    this.$localStorage.store("role", role);
  }

  getRole() {
    return this.$localStorage.retrieve("role");
  }

  setUserName(username: string) {
    this.$localStorage.store("userName", username);
  }

  getUserName() {
    return this.$localStorage.retrieve("userName");
  }

  setUserToken(token: string) {
    this.$localStorage.store("token", token);
  }

  getUserToken() {
    return this.refreshTokenService.getAccessToken();
  }

  /**
   * Set complete token data including refresh token and expires_in
   */
  setTokenData(tokenData: TokenData) {
    this.refreshTokenService.setTokenData(tokenData);
  }

  /**
   * Get refresh token
   */
  getRefreshToken(): string | null {
    return this.refreshTokenService.getRefreshToken();
  }

  /**
   * Get expires in value
   */
  getExpiresIn(): number | null {
    return this.refreshTokenService.getExpiresIn();
  }

  /**
   * Check if tokens are valid and not expired
   */
  hasValidTokens(): boolean {
    return this.refreshTokenService.hasValidTokens();
  }

  setUaaUserId(uaaUserId: number) {
    this.$localStorage.store("uaaUserId", uaaUserId);
  }

  getUaaUserId() {
    return this.$localStorage.retrieve("uaaUserId");
  }

  setUserId(userId: string) {
    this.$localStorage.store("userid", userId);
  }
  getUserId() {
    return this.$localStorage.retrieve("userid");
  }

  checkLogin() {
    this.ensureInitialized();
    return this.hasValidTokens() && this.getUserName() !== null && this.getUserId() !== null;
  }

  public isLoggedIn() {
    this.ensureInitialized();
    return this.hasValidTokens();
  }

  logout() {
    this.setUserId(null);
    this.setUserName(null);
    this.setUserToken(null);
    this.storeRole(null);
    this.refreshTokenService.clearAllTokens();
    this.$localStorage.clear();
    this.router.navigate([AppConfig.LOGIN]).then(() => {
      window.location.reload();
    });
  }

  static saveAsExcelFile(buffer: any, fileName: string): void {
    const EXCEL_TYPE = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8";
    const EXCEL_EXTENSION = ".xlsx";
    const data: Blob = new Blob([buffer], {
      type: EXCEL_TYPE,
    });
    FileSaver.saveAs(data, `${fileName}${EXCEL_EXTENSION}`, { autoBom: false });
  }
}
