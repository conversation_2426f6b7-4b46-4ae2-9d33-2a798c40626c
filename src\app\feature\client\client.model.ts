import { Pageable, Sort } from 'src/app/shared/utils/utils';
import {Variable} from '../../shared';

export class Client {
  id: number;
  login: string;
  password: string;
  cpassword: string;
  firstName: string;
  lastName: string;
  email: string;
  imageUrl: string;
  activated: boolean;
  langKey: string;
  authorities: Array<string>;
  phoneNumber: number;
  status: boolean;
  clientName: string;
  generateClientLogin: boolean;
  constructor() {
    this.generateClientLogin = false;
    this.langKey = this.langKey ? this.langKey : LANG_EN;
    this.activated = this.activated ? this.activated : false;
    this.status = true;
  }
}

export class FilterClient {
  clientName: String;
}

export class ClientPageable {
  content: Client[];
  pageable: Pageable;
  totalElements: number;
  totalPages: number;
  last: boolean;
  numberOfElements: number;
  first: boolean;
  sort: Sort;
  size: number;
  number: number;
}

export const LANG_EN = 'en';

export const CLIENT_DISPLAY_COLUMNS = [
  Variable.CLIENT_NAME,
  Variable.EMAIL,
  Variable.PHONE_NUMBER,
  Variable.STATUS,
  Variable.ACTION
];
