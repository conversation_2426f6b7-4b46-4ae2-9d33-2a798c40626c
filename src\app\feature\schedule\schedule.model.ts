import { Variable } from "../../shared";
import { Technology } from "../technology/technology.model";

export class ScheduleModel {
  id: number;
  designationName: string;
  per_day_hours: number;
  fromDate: string | Date;
  toDate: string | Date;
  note: string;
  total_hours: number;
  projectId: number;
  projectName: string;
  uaaUserId: number;
  uaaUserName: string;
  technologyDTOList: Array<Technology>;
}

export interface exportScheduleModal {
  startDate: Date;
  endDate: Date;
  scheduleData: ScheduleModel[];
}

export class ScheduleModal {
  action: string;
  schedule: ScheduleModel;
}

export class ScheduleFilters {
  projectName: string[];
  uaaUserId: number;
  clientName: string;
  filterBy: string;
  workload: string;
  technologyId: number[];
  startDate?: string;
  endDate?: string;
}

export const SCEDULE_DATE_WISE_FILTER_LIST = [Variable.FREE_SEARCH, Variable.NEXT_WEEK, Variable.NEXT_MONTH, Variable.NEXT_SIX_MONTHS, Variable.LAST_WEEK, Variable.LAST_MONTH];

export const WORK_LOAD_FILTER_LIST = [Variable.OVERL_OADED, Variable.UNDER_LOADED, Variable.NORMAL_LOADED];
