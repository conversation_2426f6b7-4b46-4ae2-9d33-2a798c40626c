import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ChangePasswordService } from './change-password.service';
import { Subscription } from 'rxjs';
import { SharedService } from '../../shared';
import { ChangePassword } from './change-password.model';
import {AppConfig} from '../../app.config';


@Component({
  selector: 'sfl-change-password',
  templateUrl: './change-password.component.html'
})
export class ChangePasswordComponent implements OnInit {
  changePassword: ChangePassword = new ChangePassword();
  subscription: Subscription = new Subscription();

  constructor(
    public changePasswordService: ChangePasswordService,
    private sharedService: SharedService,
    private router: Router
  ) { }

  ngOnInit() { }

  saveNewPassword() {
    this.changePassword.userId = this.sharedService.getUserId();
    this.changePasswordService.setNewPassword(this.changePassword).subscribe(() => {
      this.sharedService.logout();
      this.router.navigate([AppConfig.LOGIN]).then();
    });
  }

}
