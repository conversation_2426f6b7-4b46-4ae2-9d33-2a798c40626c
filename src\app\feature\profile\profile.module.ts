import { NgModule } from '@angular/core';
import { SharedModule } from '../../shared/shared.module';
import { RouterModule } from '@angular/router';
import { ProfileComponent } from './profile.component';
import { profileRoutes } from './profile.route';
import { EditProfileComponent } from './edit-profile/edit-profile.component';
import { ProfileService } from './profile.service';
import { DatePipe } from '@angular/common';
import { EditLevelComponent } from './edit-level/edit-level.component';


@NgModule({
  imports: [
    RouterModule.forChild(profileRoutes),
    SharedModule
  ],
  declarations: [
    ProfileComponent,
    EditProfileComponent,
    EditLevelComponent
  ],
  entryComponents: [
    EditLevelComponent
  ],
  providers: [
    ProfileService,
    DatePipe
  ]
})
export class ProfileModule { }
