import {Component, OnInit, Inject, OnD<PERSON>roy} from '@angular/core';
import {Subscription, throwError} from 'rxjs';
import {MatDialogRef, MAT_DIALOG_DATA, MatDialog} from '@angular/material';
import {SharedService, Variable, SnackBarService} from '../../../../shared';
import {GivenKudos, Kudos, KUDOS_ALREADY_GIVEN_ERROR_CODE, KUDOS_GIVEN_THREE_TIMES_ERROR_CODE} from '../../kudos.model';
import {KudosService} from '../../kudos.service';
import {Employee} from '../../../employee-management/employee-management.model';
import {KudosComponent} from '../../kudos.component';
import {Location} from '@angular/common';
import {KudosInstructionsComponent} from './kudos-instructions/kudos-instructions.component';
import {EmployeeManagementService} from '../../../employee-management/employee-management.service';


@Component({
  selector: 'sfl-give-kudos',
  templateUrl: './give-kudos.component.html'
})
export class GiveKudosComponent implements OnInit, OnDestroy {
  employees: Employee[] = [];
  givenKudos: GivenKudos;
  subscription: Subscription = new Subscription();
  kudos: Kudos[] = [];
  kudosMinLength = 150;
  kudosCharCount = 0;
  isPeerToPeer = false;

  constructor(
    public dialogRef: MatDialogRef<KudosComponent>,
    private sharedService: SharedService,
    private kudosService: KudosService,
    private employeeService: EmployeeManagementService,
    @Inject(MAT_DIALOG_DATA) public data: any,
    public location: Location,
    private matDialog: MatDialog,
    private snackBarService: SnackBarService
  ) {}

  ngOnInit() {
    this.givenKudos = new GivenKudos();
    this.getEmployees();
    this.getKudos();
    if (this.data.userID) {
      this.givenKudos.employeeProfileId = this.data.userID;
      this.location.back();
    }
  }

  getEmployees() {
    this.subscription.add(
      this.employeeService.getEmployees().subscribe((res: Employee[]) => {
        this.employees = res;
      })
    );
  }

  getKudos() {
    this.subscription.add(
      this.kudosService.getKudos().subscribe((res: Kudos[]) => {
        this.kudos = res;
      })
    );
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  giveKudos() {
    this.givenKudos.awardedById = this.sharedService.getUaaUserId();
    this.subscription.add(
      this.kudosService.giveKudosToEmployee(this.givenKudos).subscribe(
        () => {
          this.dialogRef.close();
        }, error => {
          if (error.status === 400) {
            if (error.error.applicationStatusCode === KUDOS_ALREADY_GIVEN_ERROR_CODE) {
              this.snackBarService.error(error.error.message);
              return throwError(error);
            } else if (error.error.applicationStatusCode === KUDOS_GIVEN_THREE_TIMES_ERROR_CODE) {
              this.snackBarService.error(error.error.message);
              return throwError(error);
            } else {
              return throwError(error);
            }
          }
      })
    );
  }

  checkKudosType() {
    this.kudos.forEach((kudo) => {
      if (kudo.id === this.givenKudos.awardsId) {
        if (kudo.name.toUpperCase() === 'PEER TO PEER HANDOUT') {
          this.isPeerToPeer = true;
          this.kudosMinLength = 250;
          this.openInstructions();
        } else {
          this.isPeerToPeer = false;
          this.kudosMinLength = 150;
        }
      }
    });
  }

  openInstructions() {
    const dialogRef = this.matDialog.open(KudosInstructionsComponent, {
      width: Variable.BOX_WIDTH_VALUE
    });
    this.subscription.add(
      dialogRef.afterClosed().subscribe(() => {})
    );
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
