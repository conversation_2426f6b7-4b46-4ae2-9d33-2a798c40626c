import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { RouterModule } from '@angular/router';
import { SharedModule } from '../../shared/shared.module';
import { employeeListRoutes } from './employee-management.route';
import { EmployeeManagementComponent } from './index';
import { EmployeeManagementService } from './employee-management.service';
import { EditLevelComponent } from '../profile/edit-level/edit-level.component';

@NgModule({
  imports: [
    RouterModule.forChild(employeeListRoutes),
    SharedModule
  ],
  declarations: [
    EmployeeManagementComponent
  ],
  entryComponents: [
    EditLevelComponent
  ],
  providers: [
    EmployeeManagementService
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})

export class EmployeeManagementModule { }
