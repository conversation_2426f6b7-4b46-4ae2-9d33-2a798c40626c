import { HttpErrorResponse } from "@angular/common/http";
import { Component, Inject, OnInit } from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material";
import * as moment from "moment-business-days";
import { Subscription } from "rxjs";
import { SnackBarService, Variable } from "../../../shared";
import { SweetAlertService } from "../../../shared/service/sweetalert.service";
import { Employee } from "../../employee-management/employee-management.model";
import { EmployeeManagementService } from "../../employee-management/employee-management.service";
import { Project } from "../../project/project.model";
import { ProjectService } from "../../project/project.service";
import { Technology } from "../../technology/technology.model";
import { TechnologyService } from "../../technology/technology.service";
import { ScheduleModal, ScheduleModel } from "../schedule.model";
import { ScheduleService } from "../schedule.service";

@Component({
  selector: "sfl-schedule-modal",
  templateUrl: "./schedule-modal.component.html",
})
export class ScheduleModalComponent implements OnInit {
  subscription: Subscription = new Subscription();
  projects: Project[] = [];
  users: Employee[] = [];
  technologies: Technology[] = [];
  data: ScheduleModel;
  workingDaysFilter: any;
  editTextLabel = Variable.EDIT;

  constructor(
    @Inject(MAT_DIALOG_DATA) public scheduleModal: ScheduleModal,
    private snackBarService: SnackBarService,
    private scheduleService: ScheduleService,
    private projectService: ProjectService,
    private technologyService: TechnologyService,
    private employeeService: EmployeeManagementService,
    public dialogRef: MatDialogRef<ScheduleModalComponent>,
    private alertService: SweetAlertService
  ) {
    this.workingDaysFilter = (d: Date): boolean => {
      const day = d.getDay();
      return day !== 0 && day !== 6;
    };
  }

  ngOnInit() {
    this.getProjects();
    this.getUsers();
    this.getTechnologies();
    this.data = this.scheduleModal.schedule;
    this.data.per_day_hours = this.data.per_day_hours ? this.data.per_day_hours : 8;
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  saveSchedule() {
    this.data.fromDate = moment(this.data.fromDate).utcOffset(Variable.IST_OFFSET).set({ hour: 0, minute: 0, second: 0 }).format();
    this.data.toDate = moment(this.data.toDate).utcOffset(Variable.IST_OFFSET).set({ hour: 23, minute: 59, second: 59 }).format();
    if (!this.data.total_hours) {
      this.data.total_hours = this.data.per_day_hours * this.getWorkingDays(this.data.toDate, this.data.fromDate);
    }
    if (!this.data.technologyDTOList) {
      this.data.technologyDTOList = [];
    }

    if (this.scheduleModal.action === this.editTextLabel) {
      this.subscription.add(
        this.scheduleService.editSchedule(this.data).subscribe({
          next: () => {
            this.dialogRef.close(true);
          },
          error: (error) => {
            this.snackBarService.error(error.error.message);
          },
        })
      );
    } else if (this.scheduleModal.action === Variable.ADD) {
      this.subscription.add(
        this.scheduleService.createSchedule(this.data).subscribe({
          next: () => {
            this.dialogRef.close(true);
          },
          error: (error: HttpErrorResponse) => {
            this.snackBarService.error(error.error.message);
          },
        })
      );
    }
  }

  deleteSchedule(id: number) {
    const confirmation = this.alertService.deleteAlert(Variable.THIS_SCHEDULE);
    confirmation.then((value) => {
      if (value === true) {
        if (this.scheduleModal.action === this.editTextLabel) {
          this.subscription.add(
            this.scheduleService.deleteSchedule(id).subscribe(() => {
              this.dialogRef.close(true);
            })
          );
        }
      }
    });
  }

  getProjects() {
    this.subscription.add(
      this.projectService.getActiveProjects().subscribe((res: Project[]) => {
        this.projects = res;
      })
    );
  }

  getTechnologies() {
    this.subscription.add(
      this.technologyService.getTechnologies().subscribe((res: Technology[]) => {
        this.technologies = res;
      })
    );
  }

  getUsers() {
    this.subscription.add(
      this.employeeService.getEmployees().subscribe((res: Employee[]) => {
        this.users = res;
      })
    );
  }

  getWorkingDays(toDate, fromDate) {
    if (toDate && fromDate) {
      return moment(toDate).businessDiff(moment(fromDate)) + 1;
    } else {
      return 0;
    }
  }

  compareFn(t1: Technology, t2: Technology) {
    return t1 && t2 ? t1.id === t2.id : t1 === t2;
  }
}
