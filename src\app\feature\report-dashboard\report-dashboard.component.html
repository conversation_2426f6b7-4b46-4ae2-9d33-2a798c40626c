<div class="body-content" fxLayout="column" fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
  <form #filter="ngForm" name="dashboardFilterForm" (ngSubmit)="applyFilter()">
    <div class="sfl-card" fxLayout="row column" fxLayoutAlign="start center" fxFlex="100">

      <mat-card-actions class="filter-input ml-1 mr-1">
        <mat-form-field>
          <mat-select placeholder="Select Project" name="projectName" [(ngModel)]="filterCharts.projectName">
            <mat-option *ngFor="let project of projects" [value]="project.name">{{project?.name}}</mat-option>
          </mat-select>
          <button mat-icon-button matSuffix class="float-center" matTooltip="clear" *ngIf="filterCharts.projectName"
            (click)="resetFilter('projectName')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1">
        <mat-form-field>
          <mat-select placeholder="Select Client" name="clientName" [(ngModel)]="filterCharts.clientName">
            <mat-option *ngFor="let client of clients" [value]="client.clientName">{{client?.clientName}}</mat-option>
          </mat-select>
          <button mat-icon-button matSuffix class="float-center" matTooltip="clear" *ngIf="filterCharts.clientName"
            (click)="resetFilter('clientName')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1">
        <mat-form-field>
          <mat-select placeholder="Select Username" name="uaaUserId" [(ngModel)]="filterCharts.uaaUserId">
            <mat-option *ngFor="let username of users" [value]="username.id">{{username?.fullName}}</mat-option>
          </mat-select>
          <button mat-icon-button matSuffix class="float-center" matTooltip="clear" *ngIf="filterCharts.uaaUserId"
            (click)="resetFilter('uaaUserId')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1">
        <mat-form-field>
          <mat-select placeholder="Select Work Type" name="workType" [(ngModel)]="filterCharts.workType">
            <mat-option *ngFor="let workType of workTypes" [value]="workType.worktypes">{{workType?.worktypes}}
            </mat-option>
          </mat-select>
          <button mat-icon-button matSuffix class="float-center" matTooltip="clear" *ngIf="filterCharts.workType"
            (click)="resetFilter('workType')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1">
        <mat-form-field>
          <mat-select placeholder="Date Filter" name="filterBy" [(ngModel)]="filterCharts.filterBy">
            <mat-option *ngFor="let dateWise of dateWiseFilter" [value]="dateWise.replace(' ', '')">{{dateWise}}
            </mat-option>
          </mat-select>
          <button mat-icon-button matSuffix class="float-center" matTooltip="clear" *ngIf="filterCharts.filterBy"
            (click)="resetFilter('filterBy')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1" *ngIf="filterCharts.filterBy === uptoYear">
        <mat-form-field>
          <input matInput type="text" placeholder="Year" name="upToYear" aria-label="year" minlength="4" maxlength="4" pattern="\d*"
                 [required]="filterCharts.filterBy === uptoYear" [(ngModel)]="filterCharts.upToYear" #uptoYearInput="ngModel" />
          <button mat-icon-button matSuffix matTooltip="clear" *ngIf="filterCharts.upToYear"
                  (click)="resetFilter('upToYear')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
          <mat-error *ngIf="uptoYearInput.touched && !uptoYearInput.valid">
            <small class="mat-text-warn" *ngIf="uptoYearInput?.errors.required">Year is required.</small>
            <small class="mat-text-warn"
                   *ngIf="uptoYearInput.errors.minlength || uptoYearInput.errors.maxlength || uptoYearInput.errors.pattern">
              Invalid Year.</small>
          </mat-error>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1" *ngIf="filterCharts.filterBy === freeSearch">
        <mat-form-field>
          <input matInput placeholder="Start date" name="startDate" aria-label="start-date" (focus)="sDate.open()" (click)="sDate.open()"
            [matDatepicker]="sDate" [max]="filterCharts.endDate" [required]="filterCharts.filterBy === freeSearch"
            [(ngModel)]="filterCharts.startDate">
          <mat-datepicker-toggle matSuffix [for]="sDate"></mat-datepicker-toggle>
          <mat-datepicker #sDate></mat-datepicker>
          <button mat-icon-button matSuffix class="float-center" matTooltip="clear" *ngIf="filterCharts.startDate"
            (click)="resetFilter('startDate')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1" *ngIf="filterCharts.filterBy === freeSearch">
        <mat-form-field>
          <input matInput placeholder="End date" name="endDate" aria-label="end-date" (focus)="eDate.open()" (click)="eDate.open()"
            [matDatepicker]="eDate" [min]="filterCharts.startDate" [required]="filterCharts.filterBy === freeSearch"
            [(ngModel)]="filterCharts.endDate">
          <mat-datepicker-toggle matSuffix [for]="eDate"></mat-datepicker-toggle>
          <mat-datepicker #eDate></mat-datepicker>
          <button mat-icon-button matSuffix class="float-center" matTooltip="clear" *ngIf="filterCharts.endDate"
            (click)="resetFilter('endDate')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-button-group ml-1">
        <button mat-raised-button class="bt-sfl mr-10px" type="submit"
                [disabled]="filter.form.invalid">Filter</button>
        <button class="bt-flat" type="button" (click)="resetAllFilter()">Reset All</button>
      </mat-card-actions>
    </div>
  </form>

  <div class="sfl-card" fxLayout="row column" fxFlex="100">
    <div class="p-25" fxLayoutAlign="center center" fxFlex="100" *ngIf="chartPerProjectTotalHoursShowLoader" >
      <mat-progress-spinner color="warn" mode="indeterminate"></mat-progress-spinner>
    </div>

    <div *ngIf="!chartPerProjectTotalHoursShowLoader && chartPerProjectTotalHoursNoDataFound" class="w-100 mt-40 no-data-found-text">No Project Found.</div>

    <div id="chartPerProjectTotalHoursCanvasDiv" #chartPerProjectTotalHoursCanvasDiv fxFlex.gt-lg="60"
      fxFlex.gt-md="60" fxFlex.gt-sm="100" fxFlex.gt-xs="100"></div>

    <div *ngIf="!chartPerProjectTotalHoursShowLoader && !chartPerProjectTotalHoursNoDataFound"
         fxFlex.gt-lg="40" fxFlex.gt-md="40" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
      <table class="w-auto" mat-table [dataSource]="dataSourcePerProjectTotalHours" matSort matSortActive="hours"
             matSortDirection="desc" matSortDisableClear #perProjectTotalHoursSort="matSort">
        <ng-container matColumnDef="projectName">
          <mat-header-cell *matHeaderCellDef mat-sort-header> Project Name </mat-header-cell>
          <mat-cell *matCellDef="let element">
            {{element?.projectName}}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="hours">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxLayoutAlign="center center"> Total Hours
          </mat-header-cell>
          <mat-cell *matCellDef="let element" class="text-center">
            {{element?.hours}}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="clientName">
          <mat-header-cell *matHeaderCellDef mat-sort-header> Client Name </mat-header-cell>
          <mat-cell *matCellDef="let element">
            <span matTooltipClass="table-mat-tooltip" [matTooltip]="element?.clientName">
              {{element?.clientName}}
            </span>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="displayedColPerProject"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColPerProject;"></mat-row>
      </table>
      <mat-paginator [pageSizeOptions]="[10]" #perProjectTotalHoursPaginator showFirstLastButtons></mat-paginator>
    </div>
  </div>

  <div class="sfl-card" fxLayout="row column" fxFlex="100">
    <div class="p-25" fxLayoutAlign="center center" fxFlex="100" *ngIf="chartPerUserTotalHoursShowLoader" >
      <mat-progress-spinner color="warn" mode="indeterminate"></mat-progress-spinner>
    </div>

    <div *ngIf="!chartPerUserTotalHoursShowLoader && chartPerUserTotalHoursNoDataFound" class="w-100 mt-40 no-data-found-text">No User Found.</div>

    <div id="chartPerUserTotalHoursCanvasDiv" #chartPerUserTotalHoursCanvasDiv fxFlex.gt-lg="70" fxFlex.gt-md="70"
      fxFlex.gt-sm="100" fxFlex.gt-xs="100"></div>

    <div *ngIf="!chartPerUserTotalHoursShowLoader && !chartPerUserTotalHoursNoDataFound"
         fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
      <table class="w-auto" mat-table [dataSource]="dataSourcePerUserTotalHours" matSort matSortActive="hours"
             matSortDirection="desc" matSortDisableClear #perUserTotalHoursSort="matSort">
        <ng-container matColumnDef="userName">
          <mat-header-cell *matHeaderCellDef mat-sort-header> User Name </mat-header-cell>
          <mat-cell *matCellDef="let element">
            {{element?.userName}}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="hours">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxLayoutAlign="center center"> Total Hours
          </mat-header-cell>
          <mat-cell *matCellDef="let element" class="text-center">
            {{element?.hours}}
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="displayedColPerUser"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColPerUser;"></mat-row>
      </table>
      <mat-paginator [pageSizeOptions]="[10]" #perUserTotalHoursPaginator showFirstLastButtons></mat-paginator>
    </div>
  </div>

  <div [hidden]="isMonthWise">
    <div class="sfl-card" fxLayout="row column" fxFlex="100">
      <div class="p-25" fxLayoutAlign="center center" fxFlex="100" *ngIf="!isMonthWise && chartDateWiseTotalHoursShowLoader" >
        <mat-progress-spinner color="warn" mode="indeterminate"></mat-progress-spinner>
      </div>

      <div *ngIf="!chartDateWiseTotalHoursShowLoader && chartDateWiseTotalHoursNoDataFound" class="w-100 mt-40 no-data-found-text">No Daily Data Found.</div>

      <div class="pos-rel" fxFlex.gt-lg="70" fxFlex.gt-md="70" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <div class="chartAreaWrapper" id="chartDateWiseTotalHoursCanvasDiv" #chartDateWiseTotalHoursCanvasDiv></div>
      </div>

      <div *ngIf="!chartDateWiseTotalHoursShowLoader && !chartDateWiseTotalHoursNoDataFound"
           fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <table class="w-auto" mat-table [dataSource]="dataSourceDateWiseTotalHours" matSort matSortActive="hours"
               matSortDirection="desc" matSortDisableClear #dateWiseTotalHoursSort="matSort">
          <ng-container matColumnDef="date">
            <mat-header-cell *matHeaderCellDef mat-sort-header> Date </mat-header-cell>
            <mat-cell *matCellDef="let element">
              {{element?.date | date: dateFormat}}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="hours">
            <mat-header-cell *matHeaderCellDef mat-sort-header fxLayoutAlign="center center"> Total Hours
            </mat-header-cell>
            <mat-cell *matCellDef="let element" class="text-center">
              {{element?.hours}}
            </mat-cell>
          </ng-container>
          <mat-header-row *matHeaderRowDef="displayedColDateWise"></mat-header-row>
          <mat-row *matRowDef="let row; columns: displayedColDateWise;"></mat-row>
        </table>
        <mat-paginator [pageSizeOptions]="[10]" #dateWiseTotalHoursPaginator showFirstLastButtons></mat-paginator>
      </div>
    </div>
  </div>

  <div [hidden]="!isMonthWise">
    <div class="sfl-card" fxLayout="row column" fxFlex="100">
      <div class="p-25" fxLayoutAlign="center center" fxFlex="100" *ngIf="isMonthWise && chartMonthWiseTotalHoursShowLoader" >
        <mat-progress-spinner color="warn" mode="indeterminate"></mat-progress-spinner>
      </div>

      <div *ngIf="!chartMonthWiseTotalHoursShowLoader && chartMonthWiseTotalHoursNoDataFound" class="w-100 mt-40 no-data-found-text">No Monthly Data Found.</div>

      <div id="chartMonthWiseTotalHoursCanvasDiv" #chartMonthWiseTotalHoursCanvasDiv fxFlex.gt-lg="70"
        fxFlex.gt-md="70" fxFlex.gt-sm="100" fxFlex.gt-xs="100"></div>

      <div *ngIf="!chartMonthWiseTotalHoursShowLoader && !chartMonthWiseTotalHoursNoDataFound"
           fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <table class="w-auto" mat-table [dataSource]="dataSourceMonthWiseTotalHours" matSort matSortActive="hours"
               matSortDirection="desc" matSortDisableClear #monthWiseTotalHoursSort="matSort">
          <ng-container matColumnDef="month">
            <mat-header-cell *matHeaderCellDef mat-sort-header> Month </mat-header-cell>
            <mat-cell *matCellDef="let element">
              {{element?.month}}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="hours">
            <mat-header-cell *matHeaderCellDef mat-sort-header fxLayoutAlign="center center"> Total Hours
            </mat-header-cell>
            <mat-cell *matCellDef="let element" class="text-center">
              {{element?.hours}}
            </mat-cell>
          </ng-container>
          <mat-header-row *matHeaderRowDef="displayedColMonthWise"></mat-header-row>
          <mat-row *matRowDef="let row; columns: displayedColMonthWise;"></mat-row>
        </table>
        <mat-paginator [pageSizeOptions]="[10]" #monthWiseTotalHoursPaginator showFirstLastButtons></mat-paginator>
      </div>
    </div>
  </div>

  <div class="sfl-card" fxLayout="row column" fxFlex="100">
    <div class="p-25" fxLayoutAlign="center center" fxFlex="100" *ngIf="chartPerWorkTypeTotalHoursShowLoader" >
      <mat-progress-spinner color="warn" mode="indeterminate"></mat-progress-spinner>
    </div>

    <div *ngIf="!chartPerWorkTypeTotalHoursShowLoader && chartPerWorkTypeTotalHoursNoDataFound" class="w-100 mt-40 no-data-found-text">No Work type Data Found.</div>

    <div id="chartPerWorkTypeTotalHoursCanvasDiv" #chartPerWorkTypeTotalHoursCanvasDiv fxFlex.gt-lg="70"
      fxFlex.gt-md="70" fxFlex.gt-sm="100" fxFlex.gt-xs="100"></div>

    <div *ngIf="!chartPerWorkTypeTotalHoursShowLoader && !chartPerWorkTypeTotalHoursNoDataFound"
         fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
      <table class="w-auto" mat-table [dataSource]="dataSourcePerWorkTypeTotalHours" matSort matSortActive="hours"
             matSortDirection="desc" matSortDisableClear #perWorkTypeTotalHoursSort="matSort">
        <ng-container matColumnDef="workTypeName">
          <mat-header-cell *matHeaderCellDef mat-sort-header> Work Type </mat-header-cell>
          <mat-cell *matCellDef="let element">
            {{element?.workTypeName}}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="hours">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxLayoutAlign="center center"> Total Hours
          </mat-header-cell>
          <mat-cell *matCellDef="let element" class="text-center">
            {{element?.hours}}
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="displayedColPerWorkType"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColPerWorkType;"></mat-row>
      </table>
      <mat-paginator [pageSizeOptions]="[10]" #perWorkTypeTotalHoursPaginator showFirstLastButtons></mat-paginator>
    </div>
  </div>

</div>
