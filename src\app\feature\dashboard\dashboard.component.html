<div class="body-content" fxLayout="column" fxFlex="100">
  <div fxFlex fxLayout="row" fxLayoutAlign="space-between" class="mb-1">
    <mat-card-title class="p-10">My Dashboard</mat-card-title>
    <div>
      <button mat-raised-button class="mr-10px" *ngIf="!ActiveEmpAttandance.checkedIn" (click)="clockIn()">Clock-In</button>
      <button name="clockOut" mat-raised-button *ngIf="ActiveEmpAttandance.checkedIn" (click)="clockOut()" class="bt-sfl mr-10px">Clock-Out</button>
      <!-- <button mat-raised-button class="bt-sfl mr-10px" [routerLink]="[worklogURL]"
        [queryParams]="{ addWorklog: true }">
        Add worklog
      </button> -->
      <button mat-raised-button class="bt-sfl" [routerLink]="[leaveURL]" [queryParams]="{ addLeave: true }">Add leave</button>
    </div>
  </div>

  <hr class="header-divider" />

  <div fxLayout="row column">
    <div fxFlex="25" class="py-1">
      <div class="card-counter danger">
        <mat-icon class="card-counter mat-icon">work_off</mat-icon>
        <span class="count-numbers">{{ leaveCount?.sickLeave || "-" }}/{{ leavesTotalCount.totalSickLeaves }}</span>
        <span class="count-name">Sick Leave</span>
      </div>
    </div>

    <div fxFlex="25" class="py-1">
      <div class="card-counter danger">
        <mat-icon class="card-counter mat-icon">work_off</mat-icon>
        <span class="count-numbers">{{ leaveCount?.casualLeave || "-" }}/{{ leavesTotalCount.totalCasualLeaves }}</span>
        <span class="count-name">Casual Leave</span>
      </div>
    </div>

    <div fxFlex="25" class="py-1">
      <div class="card-counter danger">
        <mat-icon class="card-counter mat-icon">work_off</mat-icon>
        <span class="count-numbers">{{ leaveCount?.unPaidLeave || "-" }}</span>
        <span class="count-name">UnPaid Leave</span>
      </div>
    </div>

    <div fxFlex="25" class="py-1">
      <div class="card-counter danger">
        <mat-icon class="card-counter mat-icon">work_off</mat-icon>
        <span class="count-numbers">{{ leaveCount?.sickLeave + leaveCount?.casualLeave || "-" }}/{{ leavesTotalCount.totalLeaves }}</span>
        <span class="count-name">Total Leave Applied</span>
      </div>
    </div>
  </div>

  <div fxLayout="row column" fxLayoutAlign="space-between">
    <!-- <div class="sfl-card" fxLayout="column" fxFlex="50">
      <div class="mb-1">
        <mat-card-title class="p-10">Notifications</mat-card-title>
      </div>

      <div class="p-25" fxLayoutAlign="center center" fxFlex="100" *ngIf="showNotificationLoader" >
        <mat-progress-spinner color="warn" mode="indeterminate"></mat-progress-spinner>
      </div>

      <div *ngIf="!showNotificationLoader && noNotificationData" class="w-100 mb-1 no-data-found-text">No Notification Found.</div>

      <div *ngIf="!showNotificationLoader && !noNotificationData">
        <table class="w-auto" mat-table matSort [dataSource]="notificationDataSource" #notificationSort="matSort">
          <ng-container matColumnDef="projectName">
            <mat-header-cell *matHeaderCellDef>
              Project Name
            </mat-header-cell>
            <mat-cell *matCellDef="let element">
              {{ element?.projcetName }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="formName">
              <mat-header-cell *matHeaderCellDef>
                Form Name
              </mat-header-cell>
              <mat-cell *matCellDef="let element">
                  {{ element?.formName }}
                </mat-cell>
            </ng-container>
            <ng-container matColumnDef="action">
              <mat-header-cell *matHeaderCellDef fxLayoutAlign="center center">
                Action
              </mat-header-cell>
              <mat-cell *matCellDef="let element" fxLayoutAlign="center center">
                <button mat-button class=""
                    [routerLink]="[formsFillURL, element.createdFormId, element.generatedFormId]">
                  Fill form
                </button>
              </mat-cell>
            </ng-container>
          <mat-header-row *matHeaderRowDef="displayedNotificationColumns"></mat-header-row>
          <mat-row *matRowDef="let row; columns: displayedNotificationColumns"></mat-row>
        </table>
        <mat-paginator [pageSizeOptions]="pageSize" showFirstLastButtons #notificationPaginator></mat-paginator>
      </div>
    </div> -->

    <div class="sfl-card" fxLayout="column" fxFlex="50">
      <div class="mb-1 w-100">
        <mat-card-title class="p-10">Current Projects</mat-card-title>
      </div>

      <div class="p-25" fxLayoutAlign="center center" fxFlex="100" *ngIf="showProjectsLoader">
        <mat-progress-spinner color="warn" mode="indeterminate"></mat-progress-spinner>
      </div>

      <div *ngIf="!showProjectsLoader && noProjectData" class="w-100 mb-1 no-data-found-text">No Project Found.</div>

      <div *ngIf="!showProjectsLoader && !noProjectData">
        <table
          class="w-auto"
          mat-table
          matSort
          #myCurrentProjectSort="matSort"
          [dataSource]="myCurrentProjectDataSource"
          [matSortActive]="pageableMyCurrentProject.sort"
          [matSortDirection]="pageableMyCurrentProject.direction"
          (matSortChange)="getMyCurrentProjectSorting($event)"
          matSortDisableClear
        >
          <ng-container matColumnDef="projectName">
            <mat-header-cell *matHeaderCellDef mat-sort-header> Project Name </mat-header-cell>
            <mat-cell *matCellDef="let element" class="link cursor-pointer" [routerLink]="[projectDetailsURL, element?.projectId]">
              {{ element?.projectName }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="total_hours">
            <mat-header-cell *matHeaderCellDef mat-sort-header> Hours </mat-header-cell>
            <mat-cell *matCellDef="let element">
              {{ element?.totalHours }}
            </mat-cell>
          </ng-container>
          <mat-header-row *matHeaderRowDef="displayedCurrentProjectColumns"></mat-header-row>
          <mat-row *matRowDef="let row; columns: displayedCurrentProjectColumns"></mat-row>
        </table>

        <mat-paginator
          [length]="myCurrentProjectPageable.totalElements"
          [pageSizeOptions]="pageSize"
          [pageIndex]="pageableMyCurrentProject.page"
          [pageSize]="pageableMyCurrentProject.size"
          (page)="getMyCurrentProjectPagination($event)"
          showFirstLastButtons
          #myCurrentProjectPaginator
        ></mat-paginator>
      </div>
    </div>
  </div>

  <div fxLayout="row column" fxLayoutAlign="space-between">
    <div class="sfl-card" fxFlex="50" fxLayout="column">
      <div class="mb-1">
        <mat-card-title class="p-10">Holidays</mat-card-title>
      </div>

      <div class="p-25" fxLayoutAlign="center center" fxFlex="100" *ngIf="showHolidayLoader">
        <mat-progress-spinner color="warn" mode="indeterminate"></mat-progress-spinner>
      </div>

      <div *ngIf="!showHolidayLoader && noHolidayData" class="w-100 mb-1 no-data-found-text">No Holiday Found.</div>

      <div *ngIf="!showHolidayLoader && !noHolidayData">
        <table class="w-auto" mat-table matSort #holidaySort="matSort" [dataSource]="holidayDataSource" matSortActive="startDate" matSortDirection="asc" matSortDisableClear>
          <ng-container matColumnDef="holidayName">
            <mat-header-cell *matHeaderCellDef mat-sort-header> Holiday Name </mat-header-cell>
            <mat-cell *matCellDef="let element">
              {{ element?.holidayName }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="startDate">
            <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="25"> Start Date </mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="25">
              {{ element?.startDate | date : monthDateYear }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="endDate">
            <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="25"> End Date </mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="25">
              {{ element?.endDate | date : monthDateYear }}
            </mat-cell>
          </ng-container>
          <mat-header-row *matHeaderRowDef="displayedHolidayColumns"></mat-header-row>
          <mat-row *matRowDef="let row; columns: displayedHolidayColumns"></mat-row>
        </table>
        <mat-paginator [pageSizeOptions]="pageSize" showFirstLastButtons #holidayPaginator></mat-paginator>
      </div>
    </div>

    <div class="sfl-card" fxFlex="50" fxLayout="column">
      <!-- <div class="mb-1 w-100">
        <mat-card-title class="p-10">Missing Worklogs</mat-card-title>
      </div> -->

      <!-- <div class="p-25" fxLayoutAlign="center center" fxFlex="100" *ngIf="showMissingWorklogTable">
        <mat-progress-spinner color="warn" mode="indeterminate"></mat-progress-spinner>
      </div> -->

      <!-- <div *ngIf="!showMissingWorklogTable && noMissingWorklogData" class="w-100 mb-1 no-data-found-text">No Missing Worklog Found.</div> -->

      <!-- <div *ngIf="!showMissingWorklogTable && !noMissingWorklogData">
        <table
          class="w-auto"
          mat-table
          matSort
          #missingTimeSheetSort="matSort"
          [dataSource]="missingTimeSheetDataSource"
          [matSortActive]="pageableMissingTimeSheet.sort"
          [matSortDirection]="pageableMissingTimeSheet.direction"
          (matSortChange)="getMissingTimeSheetSorting($event)"
          matSortDisableClear
        >
          <ng-container matColumnDef="date">
            <mat-header-cell *matHeaderCellDef mat-sort-header> Date </mat-header-cell>
            <mat-cell *matCellDef="let element">
              {{ element?.date | date : monthDateYear }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="action">
            <mat-header-cell *matHeaderCellDef fxLayoutAlign="center center"> Action </mat-header-cell>
            <mat-cell *matCellDef="let element" fxLayoutAlign="center center">
              <button
                mat-raised-button
                class="bt-sfl"
                [routerLink]="[worklogURL]"
                [queryParams]="{ addWorklog: true, date: element?.date }"
              >
                Add worklog
              </button>
            </mat-cell>
          </ng-container>
          <mat-header-row *matHeaderRowDef="displayMissingTimeSheetColumns"></mat-header-row>
          <mat-row *matRowDef="let row; columns: displayMissingTimeSheetColumns"></mat-row>
        </table>
        <mat-paginator
          [length]="missingTimeSheetPageable.totalElements"
          [pageSizeOptions]="pageSize"
          [pageIndex]="pageableMissingTimeSheet.page"
          [pageSize]="pageableMissingTimeSheet.size"
          (page)="getMissingWorkSheetPagination($event)"
          #missingTimeSheetPaginator
          showFirstLastButtons
        ></mat-paginator>
      </div> -->
    </div>
  </div>
</div>
