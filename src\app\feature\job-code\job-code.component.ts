import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from "@angular/core";
import { Mat<PERSON><PERSON>og, MatPaginator, MatSlideToggleChange, MatSort, MatTableDataSource } from "@angular/material";
import { Subscription } from "rxjs";
import { finalize } from "rxjs/operators";
import { SnackBarService, Variable } from "src/app/shared";
import { JOB_CODE_DISPLAY_COLUMNS, JobCodeListItem } from "./job-code";
import { JobCodeModalComponent } from "./job-code-modal/job-code-modal.component";
import { JobCodeService } from "./job-code.service";

@Component({
  selector: "app-job-code",
  templateUrl: "./job-code.component.html",
  styleUrls: ["./job-code.component.css"],
})
export class JobCodeComponent implements OnInit, OnDestroy {
  @ViewChild(MatSort, { static: false }) set setSort(sort: MatSort) {
    this.dataSource.sort = sort;
  }
  @ViewChild(MatPaginator, { static: false }) set setPaginator(paginator: MatPaginator) {
    this.dataSource.paginator = paginator;
  }
  showLoader = true;
  noDataFound = true;
  displayedColumns = JOB_CODE_DISPLAY_COLUMNS;
  dataSource = new MatTableDataSource<JobCodeListItem>();
  subscription: Subscription = new Subscription();
  constructor(private matDialog: MatDialog, private jobCodeService: JobCodeService, private snackBarService: SnackBarService) {}

  ngOnInit(): void {
    this.getAll();
  }

  getAll(): void {
    this.showLoader = true;
    this.subscription.add(
      this.jobCodeService
        .getJobCodes()
        .pipe(
          finalize(() => {
            this.noDataFound = this.dataSource.data.length <= 0;
            this.showLoader = false;
          })
        )
        .subscribe((res: JobCodeListItem[]) => {
          this.dataSource = new MatTableDataSource<JobCodeListItem>(res);
        })
    );
  }

  saveJobCode() {
    const dialogRef = this.matDialog.open(JobCodeModalComponent, {
      width: Variable.BOX_WIDTH_VALUE,
      data: null,
    });
    dialogRef.afterClosed().subscribe((res: boolean) => {
      if (res) {
        this.getAll();
      }
    });
  }

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
  }

  openModal(element: JobCodeListItem, isEditMode = false) {
    let isEdit = isEditMode;
    const dialogRef = this.matDialog.open(JobCodeModalComponent, {
      width: Variable.BOX_WIDTH_VALUE,
      data: {
        isEdit: isEdit,
        obj: element,
      },
    });
    dialogRef.afterClosed().subscribe((res: boolean) => {
      if (res) {
        this.getAll();
      }
    });
  }

  updateJobCodeStatus(id: number, obj: MatSlideToggleChange) {
    this.jobCodeService.updateStatus(id, obj.checked).subscribe((res) => {
      this.snackBarService.success(Variable.STATUS_UPDATED);
    });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
