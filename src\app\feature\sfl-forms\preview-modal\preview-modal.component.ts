import {Component, Inject, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material';
import {QUESTION_TYPE, SflFormsModel} from '../sfl-forms.model';
import {SflFormsComponent} from '../sfl-forms.component';


@Component({
  selector: 'sfl-preview-modal',
  templateUrl: './preview-modal.component.html'
})
export class PreviewModalComponent implements OnInit {
  questionType = QUESTION_TYPE;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: SflFormsModel,
    public dialogRef: MatDialogRef<SflFormsComponent>
  ) { }

  ngOnInit() { }

  closeDialog(): void {
    this.dialogRef.close();
  }

}
