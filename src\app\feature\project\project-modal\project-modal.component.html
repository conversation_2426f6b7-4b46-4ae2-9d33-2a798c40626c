<h2 mat-dialog-title>{{ isProjectEdit ? 'Update' : 'Add' }} Project</h2>
<hr class="mb-1" />

<mat-dialog-content>
  <form #projectForm="ngForm">
    <mat-card-content>
      <mat-form-field>
        <input type="text" matInput placeholder="Project Name" name="name" aria-label="name" [(ngModel)]="project.name" #name="ngModel" required />
        <mat-error *ngIf="name.touched && name.invalid">
          <small class="mat-text-warn" *ngIf="name?.errors.required">Project is required.</small>
        </mat-error>
      </mat-form-field>
      <mat-form-field>
        <textarea
          matInput
          placeholder="Description"
          name="description"
          aria-label="description"
          maxlength="255"
          minlength="1"
          [(ngModel)]="project.description"
          (change)="project.description = project.description.trim()"
          #description="ngModel"
          required
        ></textarea>
        <mat-error *ngIf="description.touched && description.invalid">
          <small class="mat-text-warn" *ngIf="description?.errors.required">Description is required.</small>
        </mat-error>
      </mat-form-field>

      <mat-form-field>
        <input
          matInput
          type="text"
          autocomplete="off"
          placeholder="Start Date"
          name="date"
          aria-label="date"
          [matDatepicker]="picker"
          (click)="picker.open()"
          [(ngModel)]="project.startDate"
          #Date="ngModel"
          required
        />
        <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
        <mat-datepicker #picker></mat-datepicker>
        <mat-error *ngIf="Date.touched && Date.invalid">
          <small class="mat-text-warn" *ngIf="Date?.errors.required">Start Date is required.</small>
        </mat-error>
      </mat-form-field>

      <mat-form-field *ngIf="!isNewClient">
        <mat-select placeholder="Select Client" name="clientId" [(ngModel)]="project.clientId" #clientId="ngModel" required>
          <mat-option (click)="isNewClient = !isNewClient"><strong>Add New Client</strong></mat-option>
          <mat-option *ngFor="let client of clients" [value]="client.id">{{ client?.clientName }}</mat-option>
        </mat-select>
        <mat-error *ngIf="clientId.touched && clientId.invalid">
          <small class="mat-text-warn" *ngIf="clientId?.errors.required">Client is required.</small>
        </mat-error>
      </mat-form-field>

      <div *ngIf="isNewClient">
        <mat-form-field>
          <input
            matInput
            type="text"
            placeholder="Client Name"
            name="clientName"
            aria-label="client-name"
            [(ngModel)]="project.clientDTO.clientName"
            #clientName="ngModel"
            required
          />
          <mat-error *ngIf="clientName.touched && clientName.invalid">
            <small class="mat-text-warn" *ngIf="clientName?.errors.required">Client name is required.</small>
          </mat-error>
        </mat-form-field>

        <div fxLayout="row" class="mb-1">
          <div fxFlex>
            <mat-slide-toggle name="isGenerateClientLogin" [(ngModel)]="project.clientDTO.generateClientLogin" color="warn"> Generate Client login </mat-slide-toggle>
          </div>
        </div>

        <div *ngIf="project.clientDTO.generateClientLogin">
          <mat-form-field>
            <input matInput type="text" placeholder="First Name" name="firstName" aria-label="first-name" [(ngModel)]="project.clientDTO.firstName" #firstName="ngModel" required />
            <mat-error *ngIf="firstName.touched && firstName.invalid">
              <small class="mat-text-warn" *ngIf="firstName?.errors.required">First name is required.</small>
            </mat-error>
          </mat-form-field>

          <mat-form-field>
            <input matInput type="text" placeholder="Last Name" name="lastName" aria-label="last-name" [(ngModel)]="project.clientDTO.lastName" #lastName="ngModel" required />
            <mat-error *ngIf="lastName.touched && lastName.invalid">
              <small class="mat-text-warn" *ngIf="lastName?.errors.required">Last name is required.</small>
            </mat-error>
          </mat-form-field>

          <mat-form-field>
            <input
              matInput
              type="email"
              placeholder="Email"
              name="email"
              aria-label="email"
              pattern="^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$"
              [(ngModel)]="project.clientDTO.email"
              #email="ngModel"
              required
            />
            <mat-error *ngIf="email.touched && email.invalid">
              <small class="mat-text-warn" *ngIf="email?.errors.required">Email is required.</small>
              <small class="mat-text-warn" *ngIf="email?.errors.pattern">Invalid Email.</small>
            </mat-error>
          </mat-form-field>

          <mat-form-field>
            <input
              matInput
              type="password"
              placeholder="Password"
              name="password"
              aria-label="password"
              pattern="^[0-9a-zA-Z]{6,}$"
              [(ngModel)]="project.clientDTO.password"
              #password="ngModel"
              required
            />
            <mat-error *ngIf="password.touched && password.invalid">
              <small class="mat-text-warn" *ngIf="password?.errors.required">Password is required.</small>
              <small class="mat-text-warn" *ngIf="password?.errors.pattern">Password must contain only numbers, digits and must be 6 characters long.</small>
            </mat-error>
          </mat-form-field>

          <mat-form-field>
            <input
              matInput
              type="password"
              placeholder="Confirm Password"
              name="c-password"
              aria-label="c-password"
              [(ngModel)]="project.clientDTO.cpassword"
              #cPassword="ngModel"
              required
              pattern="{{ project.clientDTO.password }}"
            />
            <mat-error *ngIf="cPassword.touched && cPassword.invalid">
              <small class="mat-text-warn" *ngIf="cPassword?.errors.required">Confirm password is required.</small>
              <small class="mat-text-warn" *ngIf="cPassword?.errors.pattern">Password and Confirm Password did not matched.</small>
            </mat-error>
          </mat-form-field>

          <mat-form-field>
            <input matInput placeholder="Username" name="username" aria-label="username" [(ngModel)]="project.clientDTO.login" #login="ngModel" required />
            <mat-error *ngIf="login.touched && login.invalid">
              <small class="mat-text-warn" *ngIf="login?.errors.required">Username is required.</small>
            </mat-error>
          </mat-form-field>

          <mat-form-field>
            <input
              matInput
              placeholder="Phone Number"
              name="phoneNumber"
              aria-label="phone-number"
              pattern="^\+?\d{10,13}"
              [(ngModel)]="project.clientDTO.phoneNumber"
              #phoneNumber="ngModel"
              required
            />
            <mat-error *ngIf="phoneNumber.touched && phoneNumber.invalid">
              <small class="mat-text-warn" *ngIf="phoneNumber?.errors.required">Phone Number is required.</small>
              <small class="mat-text-warn" *ngIf="phoneNumber?.errors.pattern">Invalid Phone Number.</small>
            </mat-error>
          </mat-form-field>
        </div>
      </div>
      <mat-form-field>
        <input type="text" matInput placeholder="Project Key" name="projectKey" aria-label="projectKey" [(ngModel)]="project.projectKey" #projectKey="ngModel" required />
        <mat-error *ngIf="projectKey.touched && projectKey.invalid">
          <small class="mat-text-warn" *ngIf="key?.errors.required">Project Key is required.</small>
        </mat-error>
      </mat-form-field>
    </mat-card-content>
  </form>
</mat-dialog-content>
<hr />

<mat-dialog-actions fxLayoutAlign="end">
  <button class="bt-flat" type="submit" (click)="closeDialog()">Cancel</button>
  <button
    mat-raised-button
    class="bt-sfl"
    type="submit"
    (click)="saveProject()"
    [style.cursor]="projectForm.form.invalid ? 'not-allowed' : 'pointer'"
    [disabled]="projectForm.form.invalid"
  >
    Save
  </button>
</mat-dialog-actions>
