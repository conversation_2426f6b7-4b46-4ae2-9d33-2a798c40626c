import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { RouterModule } from '@angular/router';
import { SharedModule } from '../../shared/shared.module';
import { projectRoutes } from './project.route';
import { ProjectComponent, ProjectModalComponent } from './index';
import { ProjectService } from './project.service';
import { ProjectDetailsComponent } from './project-details/project-details.component';
import { UserModalComponent } from './user-modal/user-modal.component';
import { DocumentModalComponent } from './document-modal/document-modal.component';
import { GenerateFormComponent } from 'src/app/feature/sfl-forms/generate-form/generate-form.component';
import { GenerateFormModalComponent } from 'src/app/feature/sfl-forms/generate-form/generate-form-modal/generate-form-modal.component';
import {ClientService} from '../client/client.service';
import {EmployeeManagementService} from '../employee-management/employee-management.service';
import {  WorkHoursReportComponent } from './project-workhours-report-model/project-workhours-report-model.component';
import { WorkHoursComponent } from './project-workhours-model/project-workhours-model.component';


@NgModule({
  imports: [
    RouterModule.forChild(projectRoutes),
    SharedModule
  ],
  declarations: [
    ProjectComponent,
    ProjectModalComponent,
    ProjectDetailsComponent,
    UserModalComponent,
    DocumentModalComponent,
    GenerateFormComponent,
    GenerateFormModalComponent,
    WorkHoursComponent,
    WorkHoursReportComponent
  ],
  entryComponents: [
    UserModalComponent,
    DocumentModalComponent,
    GenerateFormModalComponent,
    WorkHoursComponent,
    WorkHoursReportComponent
  ],
  providers: [
    ProjectService,
    ClientService,
    EmployeeManagementService
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})

export class ProjectModule { }
