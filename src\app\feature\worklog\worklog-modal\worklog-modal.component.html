<h2 mat-dialog-title>Worklog</h2>
<hr class="mb-1" />

<mat-dialog-content>
  <form #worklogUpdateForm="ngForm">
    <mat-card-content>
      <mat-form-field>
        <mat-select placeholder="Project Name" name="project_id" [(ngModel)]="worklog.projectId" #projectId="ngModel" required>
          <mat-option *ngFor="let project of projects" [value]="project.id">{{ project?.name }}</mat-option>
        </mat-select>
        <mat-error *ngIf="projectId.touched && projectId.invalid">
          <small class="mat-text-warn" *ngIf="projectId?.errors.required">projectName is required.</small>
        </mat-error>
      </mat-form-field>
      <mat-form-field>
        <mat-select placeholder="Work Type" name="work_type" [(ngModel)]="worklog.worktypeId" #workTypeId="ngModel" required>
          <mat-option *ngFor="let workType of workTypes" [value]="workType.id">{{ workType?.worktypes }}</mat-option>
        </mat-select>
        <mat-error *ngIf="workTypeId.touched && workTypeId.invalid">
          <small class="mat-text-warn" *ngIf="workTypeId?.errors.required">workType is required.</small>
        </mat-error>
      </mat-form-field>
      <mat-form-field>
        <input
          matInput
          type="text"
          autocomplete="off"
          placeholder="Date"
          name="date"
          aria-label="date"
          [matDatepicker]="picker"
          [max]="currentDate"
          (click)="picker.open()"
          [(ngModel)]="worklog.date"
          #Date="ngModel"
          required
        />
        <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
        <mat-datepicker #picker></mat-datepicker>
        <mat-error *ngIf="Date.touched && Date.invalid">
          <small class="mat-text-warn" *ngIf="Date?.errors.required">Date is required.</small>
        </mat-error>
      </mat-form-field>
      <mat-form-field>
        <mat-select placeholder="Select working hour" name="hour" [(ngModel)]="worklog.hours" #hour="ngModel" required>
          <mat-option *ngFor="let hour of workingHour" [value]="hour">{{ hour }}</mat-option>
        </mat-select>
        <mat-error *ngIf="hour.touched && hour.invalid">
          <small class="mat-text-warn" *ngIf="hour?.errors.required">Working Hour is required.</small>
        </mat-error>
      </mat-form-field>
      <mat-form-field>
        <textarea
          matInput
          placeholder="Description"
          name="description"
          aria-label="description"
          #Description="ngModel"
          maxlength="255"
          minlength="1"
          [(ngModel)]="worklog.note"
          required
          (change)="worklog.note = worklog.note.trim()"
        ></textarea>
        <mat-error *ngIf="Description.touched && Description.invalid">
          <small class="mat-text-warn" *ngIf="Description?.errors.required">Description is required.</small>
        </mat-error>
      </mat-form-field>
    </mat-card-content>
  </form>
</mat-dialog-content>
<hr />

<mat-dialog-actions fxLayoutAlign="end">
  <button class="bt-flat" type="submit" (click)="closeDialog()">Cancel</button>
  <button
    mat-raised-button
    class="bt-sfl"
    type="submit"
    (click)="saveWorklog()"
    [style.cursor]="worklogUpdateForm.form.invalid ? 'not-allowed' : 'pointer'"
    [disabled]="worklogUpdateForm.form.invalid"
  >
    Save
  </button>
</mat-dialog-actions>
