import {Component, Inject, OnDestroy, OnInit} from '@angular/core';
import { BasicProfile, Address, ADDRESS_TYPE } from '../profile.model';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material';
import { EmployeeManagementComponent } from 'src/app/feature/employee-management';
import { SharedService, SnackBarService, Messages, Variable } from '../../../shared';
import { Subscription } from 'rxjs';
import { ProfileService } from '../profile.service';
import { SweetAlertService } from 'src/app/shared/service/sweetalert.service';
import { DatePipe } from '@angular/common';


@Component({
  selector: 'sfl-edit-profile',
  templateUrl: './edit-profile.component.html'
})
export class EditProfileComponent implements OnInit, OnDestroy {
  subscription: Subscription = new Subscription();
  currentDate = new Date();
  ADDRESS_TYPE = ADDRESS_TYPE;
  dob = new Date();
  doj = new Date();

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: BasicProfile,
    public dialogRef: MatDialogRef<EmployeeManagementComponent>,
    private profileService: ProfileService,
    private sharedService: SharedService,
    private sweetAlertService: SweetAlertService,
    private snackBarService: SnackBarService,
    private datePipe: DatePipe
  ) { }

  ngOnInit() {
    if (!this.data.addresses) {
      this.data.addresses = [];
    }
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  saveEditProfile() {
    this.dob = new Date(this.data.birthDate);
    this.data.birthDate = this.datePipe.transform(this.dob, Variable.DATE_FORMAT);

    this.doj = new Date(this.data.joinDate);
    this.data.joinDate = this.datePipe.transform(this.doj, Variable.DATE_FORMAT);

    this.subscription.add(this.profileService.updateProfile(this.data, this.sharedService.getUaaUserId()).subscribe(() => {
      this.profileService.callMethodProfileComponent();
      this.dialogRef.close();
    }));
  }

  addNewAddress() {
    const value = new Address();
    this.data.addresses.push(value);
  }

  addressTypeAvailable(type) {
    let foundIndex = null;
    if (type === ADDRESS_TYPE.CURRENT) {
      foundIndex = this.data.addresses.findIndex((add) => add.type === ADDRESS_TYPE.CURRENT);
    } else if (type === ADDRESS_TYPE.HOME) {
      foundIndex = this.data.addresses.findIndex((add) => add.type === ADDRESS_TYPE.HOME);
    }
    return (foundIndex !== -1);
  }

  deleteAddress(index: number) {
    const confirmation = this.sweetAlertService.deleteAlert('this address?');
    confirmation.then(value => {
      if (value === true) {
        this.subscription.add(
          this.profileService.deleteAddress(this.data.addresses[index].id).subscribe(() => {
            this.data.addresses.splice(index, 1);
            this.snackBarService.success(Messages.PROFILE.address_deleted);
          })
        );
      }
    });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
