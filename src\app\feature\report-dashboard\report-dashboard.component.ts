import {<PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t, Renderer2, ViewChild} from '@angular/core';
import {MatPaginator, MatSort, MatTableDataSource} from '@angular/material';
import {DateUtils, Variable} from '../../shared';
import {Subscription} from 'rxjs';
import {Project} from '../project/project.model';
import {Employee} from '../employee-management/employee-management.model';
import {ReportDashboardService} from './report-dashboard.service';
import {WorkTypes} from '../worklog/worklog.model';
import {ProjectService} from '../project/project.service';
import {EmployeeManagementService} from '../employee-management/employee-management.service';
import {WorklogService} from '../worklog/worklog.service';
import {ClientService} from '../client/client.service';
import {Client} from '../client/client.model';
import {DatePipe} from '@angular/common';
import {finalize} from 'rxjs/operators';
import {Utils} from './utils';
import {
  DASHBOARD_DATE_WISE_DISPLAY_COLUMNS,
  DASHBOARD_PROJECT_DISPLAY_COLUMNS,
  DASHBOARD_USER_DISPLAY_COLUMNS,
  DASHBOARD_WORK_TYPE_DISPLAY_COLUMNS,
  DateWiseTotalHours,
  FilterCharts,
  PerProjectTotalHours,
  PerUserTotalHours,
  PerWorkTypeTotalHours,
  DASHBOARD_MONTH_WISE_DISPLAY_COLUMNS
} from './report-dashboard.model';


@Component({
  selector: 'sfl-dashboard',
  templateUrl: './report-dashboard.component.html'
})
export class ReportDashboardComponent implements OnInit, OnDestroy {
  @ViewChild('perProjectTotalHoursSort', {static: false}) set setPerProjectTotalHoursSort(perProjectTotalHoursSort: MatSort) {
    this.dataSourcePerProjectTotalHours.sort = perProjectTotalHoursSort;
  }
  @ViewChild('perUserTotalHoursSort', {static: false}) set setPerUserTotalHoursSort(perUserTotalHoursSort: MatSort) {
    this.dataSourcePerUserTotalHours.sort = perUserTotalHoursSort;
  }
  @ViewChild('perWorkTypeTotalHoursSort', {static: false}) set setPerWorkTypeTotalHoursSort(perWorkTypeTotalHoursSort: MatSort) {
    this.dataSourcePerWorkTypeTotalHours.sort = perWorkTypeTotalHoursSort;
  }
  @ViewChild('dateWiseTotalHoursSort', {static: false}) set setDateWiseTotalHoursSort(dateWiseTotalHoursSort: MatSort) {
    this.dataSourceDateWiseTotalHours.sort = dateWiseTotalHoursSort;
  }
  @ViewChild('monthWiseTotalHoursSort', {static: false}) set setMonthWiseTotalHoursSort(monthWiseTotalHoursSort: MatSort) {
    this.dataSourceMonthWiseTotalHours.sort = monthWiseTotalHoursSort;
  }

  @ViewChild('perProjectTotalHoursPaginator', {static: false}) set setPerProjectTotalHoursPaginator(perProjectTotalHoursPaginator: MatPaginator) {
    this.dataSourcePerProjectTotalHours.paginator = perProjectTotalHoursPaginator;
  }
  @ViewChild('perUserTotalHoursPaginator', {static: false}) set setPerUserTotalHoursPaginator(perUserTotalHoursPaginator: MatPaginator) {
    this.dataSourcePerUserTotalHours.paginator = perUserTotalHoursPaginator;
  }
  @ViewChild('perWorkTypeTotalHoursPaginator', {static: false}) set setPerWorkTypeTotalHoursPaginator(perWorkTypeTotalHoursPaginator: MatPaginator) {
    this.dataSourcePerWorkTypeTotalHours.paginator = perWorkTypeTotalHoursPaginator;
  }
  @ViewChild('dateWiseTotalHoursPaginator', {static: false}) set setDateWiseTotalHoursPaginator(dateWiseTotalHoursPaginator: MatPaginator) {
    this.dataSourceDateWiseTotalHours.paginator = dateWiseTotalHoursPaginator;
  }
  @ViewChild('monthWiseTotalHoursPaginator', {static: false}) set setMonthWiseTotalHoursPaginator(monthWiseTotalHoursPaginator: MatPaginator) {
    this.dataSourceMonthWiseTotalHours.paginator = monthWiseTotalHoursPaginator;
  }

  @ViewChild('chartPerProjectTotalHoursCanvasDiv', {static: false}) chartPerProjectTotalHoursCanvasDiv: ElementRef;
  @ViewChild('chartPerUserTotalHoursCanvasDiv', {static: false}) chartPerUserTotalHoursCanvasDiv: ElementRef;
  @ViewChild('chartPerWorkTypeTotalHoursCanvasDiv', {static: false}) chartPerWorkTypeTotalHoursCanvasDiv: ElementRef;
  @ViewChild('chartDateWiseTotalHoursCanvasDiv', {static: false}) chartDateWiseTotalHoursCanvasDiv: ElementRef;
  @ViewChild('chartMonthWiseTotalHoursCanvasDiv', {static: false}) chartMonthWiseTotalHoursCanvasDiv: ElementRef;

  dataSourcePerProjectTotalHours = new MatTableDataSource([]);
  dataSourcePerUserTotalHours = new MatTableDataSource([]);
  dataSourcePerWorkTypeTotalHours = new MatTableDataSource([]);
  dataSourceDateWiseTotalHours = new MatTableDataSource([]);
  dataSourceMonthWiseTotalHours = new MatTableDataSource([]);

  chartPerProjectTotalHoursCanvasDraw: any;
  chartPerUserTotalHoursCanvasDraw: any;
  chartPerWorkTypeTotalHoursCanvasDraw: any;
  chartDateWiseTotalHoursCanvasDraw: any;
  chartMonthWiseTotalHoursCanvasDraw: any;

  chartPerProjectTotalHoursNoDataFound = true;
  chartPerUserTotalHoursNoDataFound = true;
  chartPerWorkTypeTotalHoursNoDataFound = true;
  chartDateWiseTotalHoursNoDataFound = true;
  chartMonthWiseTotalHoursNoDataFound = true;

  chartPerProjectTotalHoursShowLoader = true;
  chartPerUserTotalHoursShowLoader = true;
  chartPerWorkTypeTotalHoursShowLoader = true;
  chartDateWiseTotalHoursShowLoader = true;
  chartMonthWiseTotalHoursShowLoader = true;

  displayedColPerProject: string[] = DASHBOARD_PROJECT_DISPLAY_COLUMNS;
  displayedColPerUser: string[] = DASHBOARD_USER_DISPLAY_COLUMNS;
  displayedColPerWorkType: string[] = DASHBOARD_WORK_TYPE_DISPLAY_COLUMNS;
  displayedColDateWise: string[] = DASHBOARD_DATE_WISE_DISPLAY_COLUMNS;
  displayedColMonthWise: string[] = DASHBOARD_MONTH_WISE_DISPLAY_COLUMNS;

  dateWiseFilter: string[] = Variable.DATE_WISE_FILTER_LIST;
  dateFormat = Variable.MM_DD_YYYY;
  freeSearch = Variable.FREE_SEARCH.replace(' ', '');
  uptoYear = Variable.UPTO_YEAR.replace(' ', '');
  subscription: Subscription = new Subscription();
  filterCharts: FilterCharts = new FilterCharts();
  projects: Project[] = [];
  users: Employee[] = [];
  workTypes: WorkTypes[] = [];
  clients: Client[] = [];
  isMonthWise = false;

  constructor(
    private dashboardService: ReportDashboardService,
    private projectService: ProjectService,
    private employeeService: EmployeeManagementService,
    private worklogService: WorklogService,
    private clientService: ClientService,
    private render: Renderer2,
    private datePipe: DatePipe
  ) { }

  ngOnInit() {
    this.applyFilter();
    this.getClients();
    this.getProjects();
    this.getUsers();
    this.getWorkTypes();
  }

  createPerProjectTotalHoursChart() {
    this.chartPerProjectTotalHoursNoDataFound = true;
    this.chartPerProjectTotalHoursShowLoader = true;

    this.subscription.add(this.dashboardService.getPerProjectTotalHours(this.filterCharts)
      .pipe(
        finalize(() => {
          this.chartPerProjectTotalHoursNoDataFound = this.dataSourcePerProjectTotalHours.data.length <= 0;
          this.chartPerProjectTotalHoursShowLoader = false;
        })
      )
      .subscribe((res: PerProjectTotalHours[]) => {
        if (res.length > 0) {
          this.chartPerProjectTotalHoursCanvasDiv.nativeElement.innerHTML = '';
          this.chartPerProjectTotalHoursCanvasDraw = this.render.createElement(Variable.CANVAS);
          this.chartPerProjectTotalHoursCanvasDraw.id = Variable.CHART_PER_PROJECT_TOTAL_HOURS_ID;
          this.chartPerProjectTotalHoursCanvasDraw.height = Variable.CHART_HEIGHT;
          this.chartPerProjectTotalHoursCanvasDiv.nativeElement.appendChild(this.chartPerProjectTotalHoursCanvasDraw);
          this.dataSourcePerProjectTotalHours = new MatTableDataSource(res);
          const data = [];
          const label = [];
          res.forEach((x) => {
            data.push(x.hours);
            label.push(x.projectName);
          });
          this.chartPerProjectTotalHoursCanvasDraw.innerHTML = Utils.getBarChart(
            Variable.CHART_PER_PROJECT_TOTAL_HOURS_ID, label, data, Variable.PER_PROJECT_TOTAL_HOURS, Variable.Project_Name, Variable.NUMBER_OF_HOURS
          );
        } else {
          this.chartPerProjectTotalHoursCanvasDiv.nativeElement.innerHTML = '';
          this.dataSourcePerProjectTotalHours = new MatTableDataSource();
        }
      })
    );
  }

  createPerUserTotalHoursChart() {
    this.chartPerUserTotalHoursNoDataFound = true;
    this.chartPerUserTotalHoursShowLoader = true;

    this.subscription.add(this.dashboardService.getPerUserTotalHours(this.filterCharts)
      .pipe(
        finalize(() => {
          this.chartPerUserTotalHoursNoDataFound = this.dataSourcePerUserTotalHours.data.length <= 0;
          this.chartPerUserTotalHoursShowLoader = false;
        })
      )
      .subscribe((res: PerUserTotalHours[]) => {
        if (res.length > 0) {
          this.chartPerUserTotalHoursCanvasDiv.nativeElement.innerHTML = '';
          this.chartPerUserTotalHoursCanvasDraw = this.render.createElement(Variable.CANVAS);
          this.chartPerUserTotalHoursCanvasDraw.id = Variable.CHART_PER_USER_TOTAL_HOURS_ID;
          this.chartPerUserTotalHoursCanvasDraw.height = Variable.CHART_HEIGHT;
          this.chartPerUserTotalHoursCanvasDiv.nativeElement.appendChild(this.chartPerUserTotalHoursCanvasDraw);
          this.dataSourcePerUserTotalHours = new MatTableDataSource(res);
          const data = [];
          const label = [];
          res.forEach((x) => {
            data.push(x.hours);
            label.push(x.userName);
          });
          this.chartPerUserTotalHoursCanvasDraw.innerHTML = Utils.getBarChart(
            Variable.CHART_PER_USER_TOTAL_HOURS_ID, label, data, Variable.PER_USER_TOTAL_HOURS, Variable.EMPLOYEE_NAME, Variable.NUMBER_OF_HOURS);
        } else {
          this.chartPerUserTotalHoursCanvasDiv.nativeElement.innerHTML = '';
          this.dataSourcePerUserTotalHours = new MatTableDataSource();
        }
      })
    );
  }

  createDateWiseTotalHoursChart() {
    this.chartDateWiseTotalHoursNoDataFound = true;
    this.chartDateWiseTotalHoursShowLoader = true;

    this.subscription.add(this.dashboardService.getDateWiseTotalHours(this.filterCharts)
      .pipe(
        finalize(() => {
          this.chartDateWiseTotalHoursNoDataFound = this.dataSourceDateWiseTotalHours.data.length <= 0;
          this.chartDateWiseTotalHoursShowLoader = false;
        })
      )
      .subscribe((res: DateWiseTotalHours[]) => {
        if (res.length > 0) {
          this.chartDateWiseTotalHoursCanvasDiv.nativeElement.innerHTML = '';
          this.chartDateWiseTotalHoursCanvasDraw = this.render.createElement(Variable.CANVAS);
          this.chartDateWiseTotalHoursCanvasDraw.id = Variable.CHART_DATE_WISE_TOTAL_HOURS_ID;
          this.chartDateWiseTotalHoursCanvasDraw.height = Variable.CHART_HEIGHT;
          this.chartDateWiseTotalHoursCanvasDiv.nativeElement.appendChild(this.chartDateWiseTotalHoursCanvasDraw);
          this.dataSourceDateWiseTotalHours = new MatTableDataSource(res);
          const data = [];
          const label = [];
          res.forEach((x) => {
            data.push(x.hours);
            label.push(this.datePipe.transform(x.date, Variable.DATE_FORMAT));
          });
          let responsive = true;
          if (data.length > 50) {
            responsive = false;
            this.chartDateWiseTotalHoursCanvasDraw.width = (data.length * Variable.RESPONSIVE_CHART_ENTITY_WIDTH).toString();
          }
          this.chartDateWiseTotalHoursCanvasDraw.innerHTML = Utils.getBarChart(
            Variable.CHART_DATE_WISE_TOTAL_HOURS_ID, label, data, Variable.DATE_WISE_TOTAL_HOURS, Variable.DATE, Variable.NUMBER_OF_HOURS, responsive);
        } else {
          this.chartDateWiseTotalHoursCanvasDiv.nativeElement.innerHTML = '';
          this.dataSourceDateWiseTotalHours = new MatTableDataSource();
        }
      })
    );
  }

  createMonthWiseTotalHoursChart() {
    this.chartMonthWiseTotalHoursNoDataFound = true;
    this.chartMonthWiseTotalHoursShowLoader = true;

    this.subscription.add(this.dashboardService.getDateWiseTotalHours(this.filterCharts)
      .pipe(
        finalize(() => {
          this.chartMonthWiseTotalHoursNoDataFound = this.dataSourceMonthWiseTotalHours.data.length <= 0;
          this.chartMonthWiseTotalHoursShowLoader = false;
        })
      )
      .subscribe((res: DateWiseTotalHours[]) => {
        if (res.length > 0) {
          this.chartMonthWiseTotalHoursCanvasDiv.nativeElement.innerHTML = '';
          this.chartMonthWiseTotalHoursCanvasDraw = this.render.createElement(Variable.CANVAS);
          this.chartMonthWiseTotalHoursCanvasDraw.id = Variable.CHART_MONTH_WISE_TOTAL_HOURS_ID;
          this.chartMonthWiseTotalHoursCanvasDraw.height = Variable.CHART_HEIGHT;
          this.chartMonthWiseTotalHoursCanvasDiv.nativeElement.appendChild(this.chartMonthWiseTotalHoursCanvasDraw);
          this.dataSourceMonthWiseTotalHours = new MatTableDataSource(res);
          const data = [];
          const label = [];
          res.forEach((x) => {
            data.push(x.hours);
            label.push(x.month);
          });
          this.chartMonthWiseTotalHoursCanvasDraw.innerHTML = Utils.getBarChart(
            Variable.CHART_MONTH_WISE_TOTAL_HOURS_ID, label, data, Variable.MONTH_WISE_TOTAL_HOURS, Variable.MONTH, Variable.NUMBER_OF_HOURS);
        } else {
          this.chartMonthWiseTotalHoursCanvasDiv.nativeElement.innerHTML = '';
          this.dataSourceMonthWiseTotalHours = new MatTableDataSource();
        }
      })
    );
  }

  createPerWorkTypeTotalHoursChart() {
    this.chartPerWorkTypeTotalHoursNoDataFound = true;
    this.chartPerWorkTypeTotalHoursShowLoader = true;

    this.subscription.add(this.dashboardService.getPerWorkTypeTotalHours(this.filterCharts)
      .pipe(
        finalize(() => {
          this.chartPerWorkTypeTotalHoursNoDataFound = this.dataSourcePerWorkTypeTotalHours.data.length <= 0;
          this.chartPerWorkTypeTotalHoursShowLoader = false;
        })
      )
      .subscribe((res: PerWorkTypeTotalHours[]) => {
        if (res.length > 0) {
          this.chartPerWorkTypeTotalHoursCanvasDiv.nativeElement.innerHTML = '';
          this.chartPerWorkTypeTotalHoursCanvasDraw = this.render.createElement(Variable.CANVAS);
          this.chartPerWorkTypeTotalHoursCanvasDraw.id = Variable.CHART_PER_WORK_TYPE_TOTAL_HOURS_ID;
          this.chartPerWorkTypeTotalHoursCanvasDiv.nativeElement.appendChild(this.chartPerWorkTypeTotalHoursCanvasDraw);
          this.dataSourcePerWorkTypeTotalHours = new MatTableDataSource(res);
          const data = [];
          const label = [];
          res.forEach((x) => {
            data.push(x.hours);
            label.push(x.workTypeName);
          });
          this.chartPerWorkTypeTotalHoursCanvasDraw.innerHTML = Utils.getPieChart(
            Variable.CHART_PER_WORK_TYPE_TOTAL_HOURS_ID, label, data, Variable.PER_WORK_TYPE_TOTAL_HOURS);
        } else {
          this.chartPerWorkTypeTotalHoursCanvasDiv.nativeElement.innerHTML = '';
          this.dataSourcePerWorkTypeTotalHours = new MatTableDataSource();
          this.chartPerWorkTypeTotalHoursNoDataFound = true;
        }
      })
    );
  }

  applyFilter() {
    if (this.filterCharts.filterBy !== this.freeSearch) {
      this.filterCharts.startDate = undefined;
      this.filterCharts.endDate = undefined;
    }
    this.filterCharts.startDate = DateUtils.convertDate(this.filterCharts.startDate);
    this.filterCharts.endDate = DateUtils.convertDate(this.filterCharts.endDate);

    this.createPerProjectTotalHoursChart();
    this.createPerUserTotalHoursChart();
    this.createPerWorkTypeTotalHoursChart();
    if (this.filterCharts.filterBy === Variable.LAST_YEAR.replace(' ', '')) {
      this.isMonthWise = true;
      this.createMonthWiseTotalHoursChart();
    } else {
      this.isMonthWise = false;
      this.createDateWiseTotalHoursChart();
    }
  }

  resetFilter(parameter: string) {
    this.filterCharts[parameter] = undefined;
  }

  resetAllFilter() {
    this.filterCharts = new FilterCharts();
    this.applyFilter();
  }

  getProjects() {
    this.subscription.add(this.projectService.getActiveProjects().subscribe((res: Project[]) => {
      this.projects = res;
    }));
  }

  getUsers() {
    this.subscription.add(this.employeeService.getEmployees().subscribe((res: Employee[]) => {
      this.users = res;
    }));
  }

  getWorkTypes() {
    this.subscription.add(this.worklogService.getWorkTypes().subscribe((res: WorkTypes[]) => {
      this.workTypes = res;
    }));
  }

  getClients() {
    this.subscription.add(this.clientService.getClients().subscribe((res: Client[]) => {
      this.clients = res;
    }));
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
