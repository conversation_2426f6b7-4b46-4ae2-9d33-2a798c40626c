import { Injectable } from '@angular/core';
import {HttpClientService} from '../../shared';
import {AppConfig} from '../../app.config';
import {ScheduleModel, ScheduleFilters} from './schedule.model';


@Injectable({
  providedIn: 'root'
})
export class ScheduleService {

  constructor(private http: HttpClientService) { }

  getSchedules(filters: ScheduleFilters) {
    return this.http.post(AppConfig.GET_SCHEDULES, filters);
  }

  createSchedule(param: ScheduleModel) {
    return this.http.post(AppConfig.SCHEDULE, param);
  }

  editSchedule(param: ScheduleModel) {
    return this.http.put(AppConfig.SCHEDULE, param);
  }

  deleteSchedule(id: number) {
    return this.http.delete(AppConfig.SCHEDULE + id);
  }

}
