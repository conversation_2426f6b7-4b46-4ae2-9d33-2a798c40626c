import {Sort, Pageable} from 'src/app/shared/utils/utils';
import {Variable} from '../../shared';

export class Worklog {
  userId: number;
  projectId: number;
  worktypeId: number;
  worktype: string;
  worktypeName: string;
  date: string | Date;
  uaaUserId: number;
  hours: number;
  note: string;
}

export class WorkTypes {
  id: number;
  worktypes: string;
}

export class FilterWorklog {
  id: number;
  uaaUserId: number;
  projectName: string;
  clientName: string;
  workType: string;
  startDate: any;
  endDate: any;
  filterBy: string;
  upToYear: number;
}

export class FilterAttendance {
  clockIn: any;
  clockOut: any;
  filterBy: string;
  uaaUserId: number;
}

export class MissedWorklog {
  localDate: string;
  userNotFilledWorklogDTOList: Array<UserIdNameDTOList>;
}

export class UserIdNameDTOList {
  serialNumber: number;
  uaaUserId: number;
  name: string;
  email: string;
}

export class FormattedMissedWorklog {
  localDate: string;
  serialNumber: number;
  name: string;
  email: string;
  uaaUserId: number;
}

export class WorklogPageable {
  content: Worklog[];
  pageable: Pageable;
  totalElements: number;
  totalPages: number;
  last: boolean;
  numberOfElements: number;
  first: boolean;
  sort: Sort;
  size: number;
  number: number;
}

export class AttendancePageable {
  content: Worklog[];
  pageable: Pageable;
  totalElements: number;
  totalPages: number;
  last: boolean;
  numberOfElements: number;
  first: boolean;
  sort: Sort;
  size: number;
  number: number;
}

export const WORKLOG_DISPLAY_COLUMNS = [
  Variable.PROJECT_NAME,
  Variable.WORK_TYPE,
  Variable.Date,
  Variable.HOURS,
  Variable.NOTE,
  Variable.ACTION
];

export const DAILY_ATTENDANCE_DISPLAY_COLUMNS = [
  Variable.EMP_NAME,
  Variable.CLOCK_IN,
  Variable.CLOCK_OUT
];

export const EMP_WORKLOG_DISPLAY_COLUMNS = [
  Variable.PROJECT_NAME,
  Variable.WORK_TYPE,
  Variable.USERNAME,
  Variable.Date,
  Variable.HOURS,
  Variable.NOTE
];

export const MISSED_WORKLOG_DISPLAY_COLUMNS = [Variable.LOCAL_DATE, Variable.NAME, Variable.EMAIL];
export const MISSED_WORKLOG_FILE_TYPES = ['pdf', 'excel'];
