import { <PERSON>mpo<PERSON>, OnInit, View<PERSON>hil<PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { MatPaginator, MatTableDataSource, MatSort } from '@angular/material';
import { Subscription } from 'rxjs';
import {PageableQuery, SharedService, Variable} from '../../../shared';
import { WorklogService } from '../worklog.service';
import { Worklog, WorkTypes, FilterWorklog, WorklogPageable, EMP_WORKLOG_DISPLAY_COLUMNS } from '../worklog.model';
import { ActivatedRoute, Router } from '@angular/router';
import { Project } from '../../project/project.model';
import { DateUtils } from '../../../shared';
import { Employee } from '../../employee-management/employee-management.model';
import { AppConfig } from '../../../app.config';
import { ProjectService } from '../../project/project.service';
import { Client } from '../../client/client.model';
import {finalize} from 'rxjs/operators';
import {EmployeeManagementService} from '../../employee-management/employee-management.service';
import {ClientService} from '../../client/client.service';


@Component({
  selector: 'sfl-emp-worklog.component',
  templateUrl: './emp-worklog.component.html'
})
export class EmpWorklogComponent implements OnInit, OnDestroy {
  @ViewChild(MatSort, {static: false}) sort: MatSort;
  @ViewChild(MatPaginator, {static: false}) paginator: MatPaginator;

  showLoader = true;
  noDataFound = true;
  uaaUserId: number;
  displayedColumns = EMP_WORKLOG_DISPLAY_COLUMNS;
  dateFormat = Variable.MM_DD_YYYY;
  dateWiseFilter: string[] = Variable.DATE_WISE_FILTER_LIST;
  projectName: string;
  projects: Project[] = [];
  workTypes: WorkTypes[] = [];
  users: Employee[] = [];
  clients: Client[] = [];
  dataSource = new MatTableDataSource<Worklog>([]);
  subscription: Subscription = new Subscription();
  worklogPageable: WorklogPageable = new WorklogPageable();
  filterWorklog: FilterWorklog = new FilterWorklog();
  pageable: PageableQuery = new PageableQuery();
  freeSearch = Variable.FREE_SEARCH.replace(' ', '');
  uptoYear = Variable.UPTO_YEAR.replace(' ', '');
  missedWorklogURL = AppConfig._WORKLOG_MISSED;

  constructor(
    private sharedService: SharedService,
    private worklogService: WorklogService,
    private projectService: ProjectService,
    private employeeService: EmployeeManagementService,
    private clientService: ClientService,
    private router: Router,
    private route: ActivatedRoute
  ) {
    this.pageable.page = 0;
    this.pageable.size = 10;
    this.pageable.sort = Variable.Date;
    this.pageable.direction = Variable.DESC;
  }

  ngOnInit() {
    this.uaaUserId = this.sharedService.getUserId();

    this.route.queryParams.subscribe((params) => {
      if (params[Variable.PROJECT_NAME.toLowerCase()] != null) {
        this.filterWorklog.projectName = params.projectname;
        this.onLoadData();
      } else {
        this.onLoadData();
        this.subscription.add(
          this.worklogService.invokeEvent.subscribe(() => {
            this.onLoadData();
          })
        );
      }
    });
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

  onLoadData() {
    this.getProjects();
    this.getClients();
    this.getWorkTypes();
    this.getUsers();
    this.applyFilter();
  }

  getSorting(event) {
    this.pageable.sort = event.active;
    this.pageable.direction = event.direction;
    this.pageable.page = 0;
    this.applyFilter(false);
  }

  getPagination(event) {
    this.pageable.size = event.pageSize;
    this.pageable.page = event.pageIndex;
    this.applyFilter(false);
  }

  applyFilter(set_page_zero = true) {
    this.pageable.page = set_page_zero ? 0 : this.pageable.page;
    this.showLoader = true;
    this.noDataFound = true;

    if (this.filterWorklog.filterBy !== this.freeSearch) {
      this.filterWorklog.startDate = undefined;
      this.filterWorklog.endDate = undefined;
    }
    this.filterWorklog.startDate = DateUtils.convertDate(this.filterWorklog.startDate);
    this.filterWorklog.endDate = DateUtils.convertDate(this.filterWorklog.endDate);

    const currentSort = this.pageable.sort;
    this.pageable.sort = this.pageable.sort + ',' + this.pageable.direction;
    this.subscription.add(this.worklogService.getFilterWorklog(this.filterWorklog, this.pageable)
      .pipe(
        finalize(() => {
          this.noDataFound = this.dataSource.data.length <= 0;
          this.showLoader = false;
        })
      )
      .subscribe((res: WorklogPageable) => {
        this.worklogPageable = res;
        this.dataSource = new MatTableDataSource<Worklog>(this.worklogPageable.content);
        this.pageable.size = this.worklogPageable.pageable.pageSize;
        this.pageable.page = this.worklogPageable.pageable.pageNumber;
      })
    );
    this.pageable.sort = currentSort;

    this.filterWorklog.startDate = DateUtils.convertStrToDate(this.filterWorklog.startDate);
    this.filterWorklog.endDate = DateUtils.convertStrToDate(this.filterWorklog.endDate);
  }

  resetFilter(parameter: string) {
    this.filterWorklog[parameter] = undefined;
  }

  resetAllFilter() {
    this.filterWorklog = new FilterWorklog();
    this.applyFilter();
  }

  getProjects() {
    this.subscription.add(this.projectService.getActiveProjects().subscribe((res: Project[]) => {
      this.projects = res;
    }));
  }

  getWorkTypes() {
    this.subscription.add(this.worklogService.getWorkTypes().subscribe((res: WorkTypes[]) => {
      this.workTypes = res;
    }));
  }

  getUsers() {
    this.subscription.add(this.employeeService.getEmployees().subscribe((res: Employee[]) => {
      this.users = res;
    }));
  }

  getClients() {
    this.subscription.add(this.clientService.getClients().subscribe((res: Client[]) => {
      this.clients = res;
    }));
  }

  getWorklogExcel() {
    this.filterWorklog.startDate = DateUtils.convertDate(this.filterWorklog.startDate);
    this.filterWorklog.endDate = DateUtils.convertDate(this.filterWorklog.endDate);

    this.subscription.add(
      this.worklogService.getWorklogFile(this.filterWorklog, this.sharedService.getUserId()).subscribe((data: any) => {
        window.open(data.fileUrl, Variable._BLANK);
      })
    );
    this.filterWorklog.startDate = DateUtils.convertStrToDate(this.filterWorklog.startDate);
    this.filterWorklog.endDate = DateUtils.convertStrToDate(this.filterWorklog.endDate);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
