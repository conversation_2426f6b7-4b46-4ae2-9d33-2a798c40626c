import {Pageable, Sort, Variable} from '../../shared';

export class GivenKudosPageable {
  content: GivenKudos[];
  pageable: Pageable;
  totalElements: number;
  totalPages: number;
  last: boolean;
  numberOfElements: number;
  first: boolean;
  sort: Sort;
  size: number;
  number: number;
}

export class GivenKudos {
  id: number;
  awardedDate: Date;
  purpose: string;
  awardedByFirstName: string;
  awardedByLastName: string;
  awardedToFirstName: string;
  awardedToLastName: string;
  employeeProfileId: number;
  awardsId: number;
  awardedById: number;
  awardsDTO: Kudos;
}

export class Kudos {
  id: number;
  name: string;
  imageUrl: string;
  description: string;
  file: File;
}

export class FilterKudos {
  employeeProfileId: number;
  awardedById: number;
  fromDate: any;
  toDate: any;
  filterBy: string;
  upToYear: number;
  name: string;
}

export const KUDOS_DISPLAY_COLUMNS = [
  Variable.IMAGE,
  Variable.AWARD.concat('.').concat(Variable.NAME),
  Variable.AWARD.concat('.').concat(Variable.DESCRIPTION),
  Variable.AWARDED_DATE,
  Variable.AWARDED_BY,
  'employeeProfileId',
  Variable.PURPOSE
];

export const KUDOS_LIST_DISPLAY_COLUMNS = [
  Variable.IMAGE,
  Variable.NAME,
  Variable.DESCRIPTION,
  Variable.ACTION
];

export const KUDOS_GIVEN_THREE_TIMES_ERROR_CODE = 1006;
export const KUDOS_ALREADY_GIVEN_ERROR_CODE = 1007;
