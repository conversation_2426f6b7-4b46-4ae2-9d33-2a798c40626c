import { Component, OnInit, ViewChild } from '@angular/core';
import { MatDialog, MatDialogConfig, MatPaginator, MatSort, MatTableDataSource } from '@angular/material';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { Variable } from 'src/app/shared';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Technology } from '../technology/technology.model';
import { EmployeeTechnologiesModalComponent } from './employee-technologies-modal/employee-technologies-modal.component';
import { EmployeeTechnologies, ScheduleModal } from './employee-technologies.model';
import { EmployeeTechnologyService } from './employee-technologies.service';

@Component({
  selector: 'app-employee-technologies',
  templateUrl: './employee-technologies.component.html'
})
export class EmployeeTechnologiesComponent implements OnInit {
  @ViewChild(MatSort, { static: false }) set setSort(sort: MatSort) {
    this.dataSource.sort = sort;
  }
  @ViewChild(MatPaginator, { static: false }) set setPaginator(paginator: MatPaginator) {
    this.dataSource.paginator = paginator;
  }

  showLoader = true;
  addText = Variable.ADD;
  noDataFound = true;
  displayedColumns = DisplayColumns.Columns.employeeTechnologiesList;
  dataSource = new MatTableDataSource<EmployeeTechnologies>();
  empTechnologies = new EmployeeTechnologies();
  subscription: Subscription = new Subscription();
  addEmpTechnology = new EmployeeTechnologies();
  editEmpTechnology = new EmployeeTechnologies();

  constructor(public dialog: MatDialog, private readonly employeeTechnologyService: EmployeeTechnologyService) {}

  ngOnInit() {
    this.getEmployeeTechnologies();
  }

  getEmployeeTechnologies() {
    this.subscription.add(
      this.employeeTechnologyService
        .getEmployeeTechnology()
        .pipe(
          finalize(() => {
            this.noDataFound = this.dataSource.data.length <= 0;
            this.showLoader = false;
          })
        )
        .subscribe(res => {
          this.dataSource = new MatTableDataSource<EmployeeTechnologies>(res.content);
        })
    );
  }

  getNameForTechnology(data: Technology[]) {
    if (data && data.length) {
      return Array.prototype.map.call(data, field => field.name).join(', ');
    }
  }

  openEmployeeTechnologyDialog(action: string, addSchedule: boolean) {
    const scheduleModal = new ScheduleModal();
    scheduleModal.action = action;
    if (action === Variable.ADD) {
      if (addSchedule) {
        scheduleModal.schedule = this.addEmpTechnology;
      } else {
        scheduleModal.schedule = new EmployeeTechnologies();
      }
    } else {
      scheduleModal.schedule = this.editEmpTechnology;
    }

    const matDialogConfig = new MatDialogConfig();
    matDialogConfig.data = scheduleModal;
    matDialogConfig.width = Variable.BOX_WIDTH_VALUE;
    const dialogRef = this.dialog.open(EmployeeTechnologiesModalComponent, matDialogConfig);
    dialogRef.afterClosed().subscribe(isNotCancelled => {
      if (isNotCancelled) {
        this.getEmployeeTechnologies();
      }
    });
  }

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
  }

  openSaveModal(data) {
    const matDialogConfig = new MatDialogConfig();
    matDialogConfig.data = data;
    matDialogConfig.width = Variable.BOX_WIDTH_VALUE;
    const dialogRef = this.dialog.open(EmployeeTechnologiesModalComponent, matDialogConfig);

    dialogRef.afterClosed().subscribe(isNotCancelled => {
      if (isNotCancelled) {
        this.getEmployeeTechnologies();
        this.dataSource._updateChangeSubscription();
      }
    });
  }
}
