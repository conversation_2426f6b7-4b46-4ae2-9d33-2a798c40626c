import { Component, OnInit, <PERSON>Child, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { MatDialog, MatSort, MatPaginator, MatTableDataSource } from '@angular/material';
import {PageableQuery, SharedService} from '../../shared';
import { Variable } from '../../shared';
import { TechnologyService } from './technology.service';
import { Subscription } from 'rxjs';
import {Technology, TECHNOLOGY_DISPLAY_COLUMNS, TechnologyPageable} from './technology.model';
import { TechnologyModalComponent } from './technology-modal/technology-modal.component';
import { SweetAlertService } from '../../shared/service/sweetalert.service';
import {finalize} from 'rxjs/operators';


@Component({
  selector: 'sfl-technology',
  templateUrl: './technology.component.html'
})
export class TechnologyComponent implements OnInit, OnDestroy {
  @ViewChild(MatSort, {static: false}) sort: MatSort;
  @ViewChild(MatPaginator, {static: false}) paginator: MatPaginator;

  showLoader = true;
  noDataFound = true;
  subscription: Subscription = new Subscription();
  dataSource = new MatTableDataSource<Technology>([]);
  displayedColumns = TECHNOLOGY_DISPLAY_COLUMNS;
  technologyPageable: TechnologyPageable = new TechnologyPageable();
  pageable: PageableQuery = new PageableQuery();

  constructor(
    private matDialog: MatDialog,
    private sharedService: SharedService,
    private technologyService: TechnologyService,
    private alertService: SweetAlertService
  ) {
    this.pageable.page = 0;
    this.pageable.size = 10;
    this.pageable.sort = Variable.NAME;
    this.pageable.direction = Variable.ASC;
  }

  ngOnInit() {
    this.getTechnologies();
    this.subscription.add(this.technologyService.invokeEvent.subscribe(() => {
      this.getTechnologies();
    }));
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

  getSorting(event) {
    this.pageable.sort = event.active;
    this.pageable.direction = event.direction;
    this.pageable.page = 0;
    this.getTechnologies();
  }

  getPagination(event) {
    this.pageable.size = event.pageSize;
    this.pageable.page = event.pageIndex;
    this.getTechnologies();
  }

  getTechnologies() {
    this.showLoader = true;
    this.noDataFound = true;
    const currentSort = this.pageable.sort;
    this.pageable.sort = this.pageable.sort + ',' + this.pageable.direction;
    this.subscription.add(this.technologyService.getTechnologiesPageable(this.pageable)
      .pipe(
        finalize(() => {
          this.noDataFound = this.dataSource.data.length <= 0;
          this.showLoader = false;
        })
      )
      .subscribe((res: TechnologyPageable) => {
      this.technologyPageable = res;
      this.dataSource = new MatTableDataSource<Technology>(this.technologyPageable.content);
      this.pageable.size = this.technologyPageable.pageable.pageSize;
      this.pageable.page = this.technologyPageable.pageable.pageNumber;
    }));
    this.pageable.sort = currentSort;
  }

  openSaveTechnology(technology: Technology) {
    this.matDialog.open(TechnologyModalComponent, {
      width: Variable.BOX_WIDTH_VALUE,
      data: technology
    });
  }

  openDeleteTechnology(technology: Technology) {
    const confirmation = this.alertService.deleteAlert(technology.name);
    confirmation.then(value => {
      if (value === true) {
        this.technologyService.deleteTechnology(technology).subscribe(() => {
          this.getTechnologies();
        });
      }
    });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
