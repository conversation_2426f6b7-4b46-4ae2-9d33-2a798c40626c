import { Injectable } from '@angular/core';
import { AppConfig } from '../../app.config';
import {HttpClientService, createRequestOption, PageableQuery} from '../../shared';
import {Client, FilterClient} from './client.model';
import { Subject } from 'rxjs';


@Injectable()
export class ClientService {
  invokeEvent: Subject<any> = new Subject();

  constructor(
    private http: HttpClientService
  ) { }

  callMethodOfWorklogComponent() {
    this.invokeEvent.next();
  }

  getClients() {
    return this.http.get(AppConfig.ALL_CLIENT);
  }

  createClient(client: Client) {
    return this.http.post(AppConfig.CREATE_CLIENT, client);
  }

  updateClient(client: Client) {
    return this.http.put(AppConfig.UPDATE_CLIENT, client);
  }

  getFilterClients(parameterValue: FilterClient, pageableObject: PageableQuery) {
    return this.http.post(AppConfig.FILTER_CLIENT, parameterValue, {
      params: createRequestOption(pageableObject)
    });
  }

}
