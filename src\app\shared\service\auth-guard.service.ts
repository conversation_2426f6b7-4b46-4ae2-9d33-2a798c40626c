import { Injectable } from '@angular/core';
import {ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot} from '@angular/router';
import { AppConfig } from 'src/app/app.config';
import { SharedService } from './shared.service';
import {Observable} from 'rxjs';
import {SnackBarService} from './snack-bar.service';
import {Messages} from '..';


@Injectable({
  providedIn: 'root'
})
export class AuthGuardService implements CanActivate {

  constructor(
    private sharedService: SharedService,
    private router: Router,
    private snackbar: SnackBarService
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    if (this.sharedService.isLoggedIn()) {
      return true;
    }
    this.snackbar.error(Messages.UNAUTHORIZED);
    this.router.navigate([AppConfig.LOGIN]).then();
    return false;
  }

}
