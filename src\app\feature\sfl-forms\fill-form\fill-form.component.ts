import {Component, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {SharedService, Variable} from '../../../shared';
import {SflFormsService} from '../sfl-forms.service';
import {ActivatedRoute} from '@angular/router';
import {Subscription} from 'rxjs';
import {AnswersDTOS, FormResponse, QUESTION_TYPE, SflFormsModel, SubQuestionsAnswerDTOS} from '../sfl-forms.model';
import {Location} from '@angular/common';


@Component({
  selector: 'sfl-fill-form',
  templateUrl: './fill-form.component.html'
})
export class FillFormComponent implements OnInit, OnDestroy {
  createdFormId: string;
  questionType = QUESTION_TYPE;
  sflForm: SflFormsModel = new SflFormsModel();
  subscription: Subscription = new Subscription();
  formResponse: FormResponse = new FormResponse();

  constructor(
    private sharedService: SharedService,
    private sflFormsService: SflFormsService,
    private activatedRoute: ActivatedRoute,
    public location: Location
  ) { }

  ngOnInit() {
    this.subscription.add(this.activatedRoute.paramMap.subscribe((params) => {
      this.createdFormId = params.get(Variable.CREATED_FORM_ID);
      this.formResponse.generatedFormId = params.get(Variable.GENERATED_FORM_ID);
      this.getCreatedForm();
    }));
  }

  getCreatedForm() {
    this.subscription.add(
      this.sflFormsService.getCreatedFormsById(this.createdFormId).subscribe((res: SflFormsModel) => {
        this.sflForm = res;
        this.sflForm.questionsDTOS.forEach(question => {
          const answersDTO = new AnswersDTOS();
          answersDTO.questionId = question.id;
          if (question.type === this.questionType.MULTI_RADIO) {
            question.subQuestionsDTOS.forEach(subQuestion => {
              const subQuestionAnswer = new SubQuestionsAnswerDTOS();
              subQuestionAnswer.subQuestionId = subQuestion.id;
              answersDTO.subQuestionsAnswerDTOS.push(subQuestionAnswer);
            });
          }
          this.formResponse.answersDTOS.push(answersDTO);
        });
      })
    );
  }

  submitFormResponse() {
    this.formResponse.filledBy = this.sharedService.getUaaUserId();
    this.subscription.add(
      this.sflFormsService.addFormResponse(this.formResponse).subscribe(() => {
        this.location.back();
      })
    );
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
