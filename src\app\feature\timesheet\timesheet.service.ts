import { Injectable } from '@angular/core';
import { AppConfig } from 'src/app/app.config';
import { HttpClientService } from 'src/app/shared';
import { TimeSheetFilter, WorklogParams } from './timesheet.model';

@Injectable({
  providedIn: 'root'
})
export class TimesheetService {
  constructor(private http: HttpClientService) {}

  saveWorklog(workLog: WorklogParams) {
    return this.http.post(AppConfig.TIME_TRACKER, workLog);
  }

  getAllWorkLogs(filter: TimeSheetFilter) {
    return this.http.post(`${AppConfig.TIME_TRACKER}/filter`, filter);
  }

  updateWorklog(workLog: WorklogParams) {
    return this.http.put(AppConfig.TIME_TRACKER, workLog);
  }

  deleteWorklog(param: string, id: number) {
    return this.http.delete(`${AppConfig.TIME_TRACKER}?${param}=${id}`);
  }

  getAllTechnologyAndUsers() {
    return this.http.get(AppConfig.PORTAL + 'uaauser-technology');
  }
}
