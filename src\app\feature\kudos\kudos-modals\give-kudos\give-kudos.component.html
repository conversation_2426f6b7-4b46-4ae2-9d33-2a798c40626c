<h2 mat-dialog-title>Give <PERSON><PERSON></h2>
<i *ngIf="isPeerToPeer" class="material-icons info-btn float-right" matTooltip="Instructions" matTooltipPosition="above" (click)="openInstructions()">info</i>

<hr class="mb-1">

<mat-dialog-content>
  <form #GiveAwardFrom="ngForm">
    <mat-card-content>
      <mat-form-field>
        <mat-select placeholder="Select Employee" name="employeeName"
                    [(ngModel)]="givenKudos.employeeProfileId" #employeeName="ngModel" required>
          <mat-option *ngFor="let employeeName of employees" [value]="employeeName.id">
            {{ employeeName?.fullName }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="employeeName.touched && employeeName.invalid">
          <small class="mat-text-warn" *ngIf="employeeName?.errors.required">Employee name is required.</small>
        </mat-error>
      </mat-form-field>

      <mat-form-field>
        <mat-select placeholder="Select Award" name="award" [(ngModel)]="givenKudos.awardsId" #award="ngModel" required (selectionChange)="checkKudosType()">
          <mat-option *ngFor="let award of kudos" [value]="award.id">{{ award?.name }}</mat-option>
        </mat-select>
        <mat-error *ngIf="award.touched && award.invalid">
          <small class="mat-text-warn" *ngIf="award?.errors.required">Award is required.</small>
        </mat-error>
      </mat-form-field>

      <mat-form-field>
        <textarea
          matInput
          placeholder="Enter detailed reason with specific instance."
          name="description"
          aria-label="description"
          [minlength]="kudosMinLength.toString()"
          [(ngModel)]="givenKudos.purpose"
          (change)="givenKudos.purpose = givenKudos.purpose.trim()"
          #description="ngModel"
          required
        ></textarea>
        <mat-error *ngIf="description.touched && description.invalid">
          <small class="mat-text-warn" *ngIf="description?.errors.minlength">Minimum {{kudosMinLength}} character Reason is required.</small>
          <small class="mat-text-warn" *ngIf="description?.errors.required">Reason is required.</small>
        </mat-error>
      </mat-form-field>
      <span class="float-right char"><em>Character count {{ givenKudos.purpose?.length + kudosCharCount }}</em></span>
    </mat-card-content>
  </form>
</mat-dialog-content>
<hr />

<mat-dialog-actions fxLayoutAlign="end">
  <button class="bt-flat" type="submit" (click)="closeDialog()">Cancel</button>
  <button
    mat-raised-button
    class="bt-sfl"
    type="submit"
    [style.cursor]="GiveAwardFrom.form.invalid ? 'not-allowed' : 'pointer'"
    (click)="giveKudos()"
    [disabled]="GiveAwardFrom.form.invalid"
  >
    Save
  </button>
</mat-dialog-actions>
