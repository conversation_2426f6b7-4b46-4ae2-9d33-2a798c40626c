import {NgModule} from '@angular/core';
import {SharedModule} from '../../shared/shared.module';
import {RouterModule} from '@angular/router';
import {MatPaginatorModule} from '@angular/material';
import {SflFormsService} from './sfl-forms.service';
import {SflFormsComponent} from './sfl-forms.component';
import {CreateFormComponent} from './create-form/create-form.component';
import {sflFormsRoutes} from './sfl-forms-route';
import {MatRadioModule} from '@angular/material';
import { GeneratorModalComponent } from './generator-modal/generator-modal.component';
import { FillFormComponent } from './fill-form/fill-form.component';
import { FormResponseComponent } from './form-response/form-response.component';
import { FormResponseModalComponent } from './form-response/form-response-modal/form-response-modal.component';
import {PreviewModalComponent} from './preview-modal/preview-modal.component';
import {ProjectService} from '../project/project.service';

@NgModule({
  imports: [
    RouterModule.forChild(sflFormsRoutes),
    SharedModule,
    MatPaginatorModule,
    MatRadioModule
  ],
  exports: [
    MatPaginatorModule
  ],
  declarations: [
    SflFormsComponent,
    CreateFormComponent,
    GeneratorModalComponent,
    FillFormComponent,
    FormResponseComponent,
    FormResponseModalComponent,
    PreviewModalComponent
  ],
  providers: [
    SflFormsService,
    ProjectService
  ]
})

export class SflFormsModule { }
