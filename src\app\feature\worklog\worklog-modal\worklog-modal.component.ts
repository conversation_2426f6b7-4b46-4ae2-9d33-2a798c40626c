import { DatePipe } from "@angular/common";
import { Component, Inject, <PERSON><PERSON><PERSON>roy, OnInit } from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material";
import { ActivatedRoute, Router } from "@angular/router";
import * as moment from "moment-business-days";
import { Subscription } from "rxjs";
import { AppConfig } from "../../../app.config";
import { SharedService, Variable } from "../../../shared";
import { Project } from "../../project/project.model";
import { ProjectService } from "../../project/project.service";
import { WorklogComponent } from "../worklog.component";
import { WorkTypes, Worklog } from "../worklog.model";
import { WorklogService } from "../worklog.service";

@Component({
  selector: "sfl-update-worklog",
  templateUrl: "./worklog-modal.component.html",
})
export class WorklogModalComponent implements OnInit, OnDestroy {
  projects: Project[] = [];
  workTypes: WorkTypes[];
  workingHour = Variable.WORKING_HOURS;
  subscription: Subscription = new Subscription();
  currentDate = new Date();
  date = new Date();
  worklog: Worklog = new Worklog();

  constructor(
    private sharedService: SharedService,
    @Inject(MAT_DIALOG_DATA) public data: Worklog,
    public dialogRef: MatDialogRef<WorklogComponent>,
    private router: Router,
    private datePipe: DatePipe,
    private worklogService: WorklogService,
    private projectService: ProjectService,
    private activatedRoute: ActivatedRoute
  ) {}

  ngOnInit() {
    this.activatedRoute.queryParams.subscribe((params) => {
      if (params && params.addWorklog) {
        setTimeout(() => {
          if (params.date) {
            this.worklog.date = params.date;
          }
        }, 0);
      }
    });
    if (this.data) {
      this.worklog = this.data;
    } else {
      this.worklog.date = new Date();
    }
    this.getProjects();
    this.getWorkTypes();
  }

  getProjects() {
    this.subscription.add(
      this.projectService.getActiveProjects().subscribe((res: Project[]) => {
        this.projects = res;
      })
    );
  }

  getWorkTypes() {
    this.subscription.add(
      this.worklogService.getWorkTypes().subscribe((res: WorkTypes[]) => {
        this.workTypes = res;
      })
    );
  }

  saveWorklog() {
    this.date = new Date(this.worklog.date);
    this.worklog.date = this.datePipe.transform(this.date, Variable.DATE_FORMAT);
    this.worklog.date = moment(this.worklog.date + " " + moment().format("HH:mm:ss"))
      .utcOffset(Variable.IST_OFFSET)
      .format();
    if (this.data) {
      this.updateWorklog();
    } else {
      this.addWorklog();
    }
  }

  addWorklog() {
    this.worklog.uaaUserId = this.sharedService.getUserId();
    this.subscription.add(
      this.worklogService.createWorklog(this.worklog).subscribe(() => {
        this.worklogService.callMethodOfWorklogComponent();
        this.closeDialog();
      })
    );
  }

  updateWorklog() {
    this.subscription.add(
      this.worklogService.updateWorklog(this.worklog).subscribe(() => {
        this.worklogService.callMethodOfWorklogComponent();
        this.closeDialog();
      })
    );
  }

  closeDialog(): void {
    this.dialogRef.close();
    this.router.navigate([AppConfig._WORKLOG]).then();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
