import {Variable} from '../../shared';

export class PerProjectTotalHours {
  projectId: number;
  projectName: string;
  hours: number;
  active: boolean;
  description: string;
  clientId: number;
  clientName: string;
}

export class PerUserTotalHours {
  uaaUser_Id: number;
  userName: string;
  hours: number;
}

export class PerWorkTypeTotalHours {
  workTypeId: number;
  workTypeName: string;
  hours: number;
}

export class DateWiseTotalHours {
  hours: number;
  date: string;
  month: string;
}

export class FilterCharts {
  projectName: string;
  clientName: string;
  uaaUserId: string;
  workType: string;
  filterBy: string;
  upToYear: number;
  startDate: any;
  endDate: any;
}

export const DASHBOARD_PROJECT_DISPLAY_COLUMNS = [Variable.PROJECT_NAME, Variable.HOURS, Variable.CLIENT_NAME];
export const DASHBOARD_USER_DISPLAY_COLUMNS = [Variable.USERNAME, Variable.HOURS];
export const DASHBOARD_WORK_TYPE_DISPLAY_COLUMNS = [Variable.WORK_TYPE_NAME, Variable.HOURS];
export const DASHBOARD_DATE_WISE_DISPLAY_COLUMNS = [Variable.Date, Variable.HOURS];
export const DASHBOARD_MONTH_WISE_DISPLAY_COLUMNS = [Variable.Month, Variable.HOURS];
