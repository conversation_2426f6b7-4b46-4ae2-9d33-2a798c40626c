# Refresh Token Implementation Testing Guide

## Manual Testing Steps

### 1. Login Flow Testing
1. Open the application and navigate to login page
2. Enter valid credentials and login
3. Check browser localStorage for the following keys:
   - `SFL-demo-token` (access token)
   - `SFL-demo-refreshToken` (refresh token)
   - `SFL-demo-expiresIn` (expiration time in seconds)
   - `SFL-demo-tokenTimestamp` (timestamp when token was stored)

### 2. Token Refresh Testing
1. **Automatic Refresh (5 minutes before expiry):**
   - Modify the `expires_in` value in localStorage to a small value (e.g., 360 seconds = 6 minutes)
   - Wait for 1 minute and check if the token gets refreshed automatically
   - Verify new tokens are stored in localStorage

2. **Manual Token Expiry:**
   - Set `expires_in` to 1 second in localStorage
   - Make any API call (navigate to dashboard, etc.)
   - Verify that token refresh is triggered automatically

### 3. Application Lifecycle Testing
1. **Tab Inactive/Active:**
   - Switch to another tab for a few minutes
   - Come back to the application tab
   - Verify that token refresh is triggered if needed

2. **Browser Close/Reopen:**
   - Close the browser completely
   - Reopen and navigate to the application
   - Verify that existing valid tokens are used and refresh timer is restarted

### 4. Error Scenarios Testing
1. **Invalid Refresh Token:**
   - Manually modify the refresh token in localStorage to an invalid value
   - Trigger a token refresh (by making an API call or waiting for auto-refresh)
   - Verify that user is redirected to login page

2. **Network Error During Refresh:**
   - Disconnect internet during token refresh
   - Verify appropriate error handling

### 5. Logout Testing
1. Click logout button
2. Verify that all token-related data is cleared from localStorage
3. Verify that refresh timers are stopped

## Expected Behavior

### Login Response Format
The backend should return:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expires_in": 86399,
  "token_type": "Bearer"
}
```

### Refresh Token API
- **Endpoint:** `POST /auth/refresh-token`
- **Headers:** `Authorization: Bearer {access_token}`
- **Body:**
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### Refresh Response Format
Same as login response with new tokens.

## Debugging Tips

1. **Check Console Logs:**
   - Look for token refresh success/failure messages
   - Check for any JavaScript errors

2. **Network Tab:**
   - Monitor API calls to see when refresh token API is called
   - Verify request headers and body format

3. **Application Tab (DevTools):**
   - Monitor localStorage changes
   - Check if tokens are being stored/updated correctly

4. **Timer Verification:**
   - Add console logs in TokenManagementService to track timer creation/destruction

## Common Issues and Solutions

1. **Tokens not being stored:**
   - Check if login response contains all required fields
   - Verify SharedService.setTokenData() is being called

2. **Refresh not triggering:**
   - Check if timer is being set correctly
   - Verify expires_in value is reasonable

3. **401 errors not handled:**
   - Check if HTTP interceptor is properly configured
   - Verify TokenManagementService is injected correctly

4. **Circular dependency errors:**
   - Ensure proper service injection order
   - Check for circular imports between services
