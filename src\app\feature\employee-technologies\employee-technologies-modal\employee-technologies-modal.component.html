<h2 mat-dialog-title>{{ isEmployeeTechnologyEdit ? 'Update' : 'Add' }} Employee Technologies</h2>
<hr class="mb-1" />

<mat-dialog-content>
  <form #projectForm="ngForm">
    <mat-card-content>
      <mat-form-field>
        <mat-select placeholder="Select Employee" name="uaaUserId" [(ngModel)]="data.uaaUserId" #user="ngModel" required>
          <mat-option *ngFor="let user of users" [value]="user.id">{{ user?.fullName }}</mat-option>
        </mat-select>
        <mat-error *ngIf="user.touched && user.invalid">
          <small class="mat-text-warn" *ngIf="user?.errors.required">user is required.</small>
        </mat-error>
      </mat-form-field>

      <mat-form-field>
        <mat-select placeholder="Select Technology" name="technology" [(ngModel)]="data.technologyId" #technology="ngModel" multiple>
          <mat-option *ngFor="let technology of technologies" [value]="technology?.id">{{ technology?.name }}</mat-option>
        </mat-select>
      </mat-form-field>
    </mat-card-content>
  </form>
</mat-dialog-content>
<hr />

<mat-dialog-actions fxLayoutAlign="end">
  <button class="bt-flat" type="submit" (click)="closeDialog()">Cancel</button>
  <button mat-raised-button class="bt-sfl" type="submit" (click)="saveSchedule()" [disabled]="projectForm.form.invalid">Save</button>
</mat-dialog-actions>
