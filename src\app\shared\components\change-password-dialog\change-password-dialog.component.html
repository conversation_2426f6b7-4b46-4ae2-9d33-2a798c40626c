<h2 mat-dialog-title>Change Password</h2>
<hr class="mb-1" />
<mat-dialog-content>
  <form #passwordForm="ngForm">
    <mat-card-content>
      <mat-form-field>
        <mat-label>New Password</mat-label>
        <input matInput [type]="showNewPassword ? 'text' : 'password'" id="password" name="password" [(ngModel)]="password" #passwordModel="ngModel" required minlength="8" />
        <mat-icon matSuffix (click)="togglePasswordVisibility('newPassword')" class="cp">
          {{ showNewPassword ? 'visibility_off' : 'visibility' }}
        </mat-icon>
        <mat-error *ngIf="passwordModel.invalid && passwordModel.touched">
          <small *ngIf="passwordModel.errors?.required">New Password is required.</small>
          <small *ngIf="passwordModel.errors?.minlength">New Password must be at least 8 characters long.</small>
        </mat-error>
      </mat-form-field>

      <mat-form-field>
        <mat-label>Confirm Password</mat-label>
        <input
          matInput
          type="password"
          id="confirmPassword"
          name="confirmPassword"
          [(ngModel)]="confirmPassword"
          #confirmPasswordModel="ngModel"
          required
          [type]="showConfirmPassword ? 'text' : 'password'"
          (input)="validatePasswords()"
        />
        <mat-icon matSuffix (click)="togglePasswordVisibility('confirmPassword')">
          {{ showConfirmPassword ? 'visibility_off' : 'visibility' }}
        </mat-icon>
        <mat-error *ngIf="confirmPasswordModel.invalid && confirmPasswordModel.touched">
          <small *ngIf="confirmPasswordModel.errors?.required">Confirm Password is required.</small>
        </mat-error>
      </mat-form-field>
      <mat-error *ngIf="!passwordsMatch">
        <div>Passwords do not match.</div>
      </mat-error>
    </mat-card-content>
  </form>
</mat-dialog-content>
<hr />
<mat-dialog-actions fxLayoutAlign="end">
  <button class="bt-flat" type="button" (click)="closeDialog()">Cancel</button>
  <button mat-raised-button class="bt-sfl" type="submit" [disabled]="passwordForm.invalid" (click)="savePasswordDetails()">Save</button>
</mat-dialog-actions>
