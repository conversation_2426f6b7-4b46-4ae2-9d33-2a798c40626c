import { DatePipe } from "@angular/common";
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from "@angular/core";
import { RouterModule } from "@angular/router";
import { SharedModule } from "../../shared/shared.module";
import { ClientService } from "../client/client.service";
import { EmployeeManagementService } from "../employee-management/employee-management.service";
import { ProjectService } from "../project/project.service";
import { EmpWorklogComponent, WorklogComponent } from "./index";
import { MissedWorklogComponent } from "./missed-worklog/missed-worklog.component";
import { WorklogModalComponent } from "./worklog-modal/worklog-modal.component";
import { worklogRoutes } from "./worklog.route";
import { WorklogService } from "./worklog.service";

@NgModule({
  imports: [RouterModule.forChild(worklogRoutes), SharedModule],
  declarations: [WorklogComponent, EmpWorklogComponent, MissedWorklogComponent, WorklogModalComponent],
  entryComponents: [],
  providers: [WorklogService, ProjectService, EmployeeManagementService, ClientService, DatePipe],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class WorklogModule {}
