<div class="body-content" fxLayout="column" fxFlex="100">
  <div fxFlex fxLayout="row" fxLayoutAlign="space-between" class="mb-1">
    <mat-card-title class="p-10">Form Response - {{formName}} </mat-card-title>
  </div>

  <hr class="header-divider">

  <div class="sfl-card">
    <div class="p-25" fxLayoutAlign="center center" fxFlex="100" *ngIf="showLoader">
      <mat-progress-spinner color="warn" mode="indeterminate"></mat-progress-spinner>
    </div>

    <div *ngIf="!showLoader && noDataFound" class="w-100 mt-40 no-data-found-text">No Form Response Found.</div>

    <div class="w-100" *ngIf="!showLoader && !noDataFound">
      <table class="w-auto" mat-table matSort matSortDisableClear matSortActive="createdDate" matSortDirection="desc" [dataSource]="dataSource">

        <ng-container matColumnDef="filledBy">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-sm="30"> Name &nbsp; <i>(filled by)</i> </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-sm="30">
            {{ element?.filledBy }}
          </mat-cell>
        </ng-container>

        <ng-container matColumnDef="createdDate">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-sm="30"> Submitted on </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-sm="30">
            {{ element?.createdDate | date: 'MM/dd/yyyy' }}
          </mat-cell>
        </ng-container>

        <ng-container matColumnDef="behalfOf">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-sm="30"> Filled on behalf of </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-sm="30">
            {{ element?.behalfOf || 'None' }}
          </mat-cell>
        </ng-container>

        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxLayoutAlign="center center" fxFlex.gt-sm="30"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxLayoutAlign="center center" fxFlex.gt-sm="30">
            <button mat-icon-button matTooltip="View details" (click)="responseDetails(element)">
              <mat-icon>visibility</mat-icon>
            </button>
          </mat-cell>
        </ng-container>

        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns"></mat-row>
      </table>
      <mat-paginator [pageSize]="10" [pageSizeOptions]="[10, 20, 25]" showFirstLastButtons></mat-paginator>
    </div>
  </div>

</div>
