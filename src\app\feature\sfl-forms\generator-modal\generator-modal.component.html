<h2 mat-dialog-title>
  {{ data?.formName }}
</h2>
<hr class="mb-1">

<mat-dialog-content class="mb-1">
  <mat-card-content fxLayout="column">
    <form #generatorForm="ngForm">
      <mat-form-field fxFlex="100">
        <mat-chip-list #generatorList>
          <mat-chip *ngFor="let generator of data.generators" (removed)="removeGenerator(generator)">
            {{generator | replaceString: '-' | titlecase}}
            <mat-icon matChipRemove>cancel</mat-icon>
          </mat-chip>
          <input name="generators" placeholder="Who can generate this form" aria-label="generator" [matAutocomplete]="auto" [matChipInputFor]="generatorList">
        </mat-chip-list>
        <mat-autocomplete #auto="matAutocomplete" (optionSelected)="selectedGenerator($event)">
          <mat-option *ngFor="let generator of formGenerator" [value]="generator">
            {{generator | replaceString: '-' | titlecase}}
          </mat-option>
        </mat-autocomplete>
      </mat-form-field>
    </form>

  </mat-card-content>
</mat-dialog-content>
<hr>

<mat-dialog-actions fxLayoutAlign="end">
  <button class="bt-flat" (click)="closeDialog()">Close</button>
  <button mat-raised-button class="bt-sfl" type="submit" (click)="updateFormGenerator()"
          [style.cursor]="generatorForm.form.invalid ? 'not-allowed' : 'pointer'" [disabled]="generatorForm.form.invalid">
    Save
  </button>
</mat-dialog-actions>
