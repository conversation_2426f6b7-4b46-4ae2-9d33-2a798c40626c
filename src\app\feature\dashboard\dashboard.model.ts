import { Pageable, Sort } from 'src/app/shared/utils/utils';
import {Variable} from '../../shared';

export class MissingTimeSheet {
  date: Date;
}

export class MissingTimeSheetPageable {
  content: MissingTimeSheet[];
  pageable: Pageable;
  totalElements: number;
  totalPages: number;
  last: boolean;
  numberOfElements: number;
  first: boolean;
  sort: Sort;
  size: number;
  number: number;
}

export class MyCurrentProject {
  totalHours: number;
  projectName: string;
  projectId: number;
}

export class MyCurrentProjectPageable {
  content: MyCurrentProject[];
  pageable: Pageable;
  totalElements: number;
  totalPages: number;
  last: boolean;
  numberOfElements: number;
  first: boolean;
  sort: Sort;
  size: number;
  number: number;
}

export class Holiday {
  id: number;
  holidayName: string;
  startDate: Date;
  endDate: Date;
}

export class LeaveCount {
  id: number;
  sickLeave: number;
  casualLeave: number;
  unPaidLeave: number;
  leaveCycleYear: number;
  uaaUserId: number;
}

export class LeavesTotalCount {
  id: number;
  totalSickLeaves: number;
  totalCasualLeaves: number;
  totalLeaveCountYear: number;
  totalLeaves: number;
  uaaUserId: number;
}

export class ClockInOutDTO {
  uaaUserId: number;
  clockIn: any;
  clockOut: any;
  id: number;
  userName: string;
}

export class AttendanceDTO {
  uaaUserId: number;
  clockIn: any;
  clockOut: any;
}

export class ActiveUserDTO {
  checkedIn: boolean;
  clockedInId: number;
}

export class FormNotification {
  createdFormId: string;
  formName: string;
  generatedFormId: string;
  projcetName: string;
  action: string;
}

export const DASHBOARD_MISSING_TIME_SHEET_COLUMNS = [Variable.Date, Variable.ACTION];
export const DASHBOARD_CURRENT_PROJECT_COLUMNS = [Variable.PROJECT_NAME, 'total_hours'];
export const DASHBOARD_HOLIDAY_DISPLAY_COLUMNS = ['holidayName', Variable.START_DATE, Variable.END_DATE];
export const DASHBOARD_NOTIFICATIONS_COLUMNS = ['projectName', Variable.FORM_NAME, Variable.ACTION];
