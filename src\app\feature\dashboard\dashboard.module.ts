import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { RouterModule } from '@angular/router';
import { SharedModule } from '../../shared/shared.module';
import { myDashboardRoutes } from './dashboard.route';
import { DashboardComponent } from './dashboard.component';
import { DashboardService } from './dashboard.service';
import { DatePipe } from '@angular/common';


@NgModule({
  imports: [
    RouterModule.forChild(myDashboardRoutes),
    SharedModule
  ],
  declarations: [
    DashboardComponent
  ],
  providers: [
    DashboardService,
    DatePipe
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})

export class DashboardModule { }
