import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { LoginService } from './login.service';
import { LoginModel, EmployeeProfileParam } from './login.model';
import { AppConfig } from '../../app.config';
import {ErrorMessage, Messages, ROLES, SharedService, SnackBarService, Variable} from '../../shared';


@Component({
  selector: 'sfl-login',
  templateUrl: './login.component.html'
})
export class LoginComponent implements OnInit, OnDestroy {
  username: string;
  password: string;
  isError = false;
  message: string;
  authority: string[] = [];
  loginParams: LoginModel = new LoginModel();
  subscription: Subscription = new Subscription();
  employeeProfileParams: EmployeeProfileParam = new EmployeeProfileParam();

  constructor(
    private router: Router,
    private sharedService: SharedService,
    private loginService: LoginService,
    private snackBarService: SnackBarService
  ) {}

  ngOnInit() {
    if (this.sharedService.checkLogin()) {
      this.authority = this.sharedService.getRole();
      this.routeHandler();
    }
  }

  login() {
    this.isError = false;
    this.loginParams.username = this.username;
    this.loginParams.password = this.password;
    this.loginParams.rememberMe = false;

    this.subscription.add(
      this.loginService.login(this.loginParams).subscribe((response: any) => {
          this.sharedService.setUserToken(response.access_token);
          if (this.sharedService.getUserToken() !== null) {
            this.getAccountDetails();
          } else {
            this.router.navigate([AppConfig.LOGIN]).then();
          }
        }, (error: any) => {
          this.errorHandler(error);
        }
      )
    );
  }

  getAccountDetails() {
    this.subscription.add(
      this.loginService.getAccount().subscribe((res: any) => {
        this.authority = res.authorities;
        this.storeUserDetails(res);
        this.setUaaUser(res);
      })
    );
  }

  setUaaUser(data) {
    this.loginService.setUaaUser(data.id).subscribe((resp: any) => {
      this.sharedService.setUserId(resp.id);
      this.getEmployeeProfileDetails();
    });
  }

  storeUserDetails(data) {
    this.sharedService.storeRole(data.authorities);
    this.sharedService.setUserName(data.login);
    this.sharedService.setUaaUserId(data.id);
    this.employeeProfileParams.email = data.email;
    this.employeeProfileParams.userName = data.login;
  }

  getEmployeeProfileDetails() {
    this.loginService.setEmployeeProfile(
      this.sharedService.getUaaUserId(),
      this.employeeProfileParams
    ).subscribe(() => {
      this.routeHandler();
      }, () => {
        this.sharedService.logout();
      }
    );
  }

  routeHandler() {
    if (this.authority.includes(ROLES.SUPER_ADMIN) ||
      this.authority.includes(ROLES.ADMIN)) {
      this.router.navigate([AppConfig._REPORT_DASHBOARD]).then();
    } else if (this.authority.includes(ROLES.LEAD) ||
      this.authority.includes(ROLES.HR) ||
      this.authority.includes(ROLES.USER)) {
      this.router.navigate([AppConfig._DASHBOARD]).then();
    } else {
      this.snackBarService.error(Messages.UNAUTHORIZED);
      this.sharedService.logout();
    }
  }

  errorHandler(error) {
    if (error.error === ErrorMessage.Unauthorized) {
      this.message = ErrorMessage.Failed_Sign_In + '\n' + ErrorMessage.ActivateAccount;
    } else if (error.error === Variable.INVALID_GRANT) {
      this.message = ErrorMessage.Failed_Sign_In + '\n' + ErrorMessage.InvalidCredentials;
    } else {
      this.message = ErrorMessage.InternalServer;
    }
    this.isError = true;
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
