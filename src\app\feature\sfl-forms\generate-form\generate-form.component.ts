import {Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild, Input} from '@angular/core';
import {DateUtils, PageableQuery, SharedService, Variable} from '../../../shared';
import {MatTableDataSource} from '@angular/material/table';
import {MatSort} from '@angular/material/sort';
import {MatPaginator} from '@angular/material/paginator';
import {Subject, Subscription} from 'rxjs';
import {Project} from '../../project/project.model';
import {MatDialog} from '@angular/material/dialog';
import {SweetAlertService} from '../../../shared/service/sweetalert.service';
import {ActivatedRoute} from '@angular/router';
import {SflFormsService} from '../sfl-forms.service';
import {ProjectService} from '../../project/project.service';
import {GenerateFormModalComponent} from './generate-form-modal/generate-form-modal.component';
import {AppConfig} from '../../../app.config';
import {finalize} from 'rxjs/operators';
import {
  FilterGeneratedForm,
  FilterGeneratedFormPageable,
  GENERATED_FORMS_COLUMNS,
  GenerateForm,
  SflFormsModel
} from '../sfl-forms.model';


@Component({
  selector: 'sfl-generate-form',
  templateUrl: './generate-form.component.html'
})
export class GenerateFormComponent implements OnInit, OnDestroy {
  @ViewChild(MatSort, {static: false}) sort: MatSort;
  @ViewChild(MatPaginator, {static: false}) paginator: MatPaginator;

  showLoader = true;
  noDataFound = true;
  uaaUserId: number;
  displayedColumns = GENERATED_FORMS_COLUMNS;
  dateFormat = Variable.MM_DD_YYYY;
  dataSource = new MatTableDataSource<GenerateForm>([]);
  subscription: Subscription = new Subscription();
  projects: Project[] = [];
  createdForms: SflFormsModel[] = [];
  pageable: PageableQuery = new PageableQuery();
  filterGeneratedFormPageable: FilterGeneratedFormPageable = new FilterGeneratedFormPageable();
  filterGeneratedForm: FilterGeneratedForm = new FilterGeneratedForm();
  dateWiseFilter: string[] = Variable.DATE_WISE_FILTER_LIST;
  formsResponseURL = AppConfig._FORMS_RESPONSE;
  freeSearch = Variable.FREE_SEARCH.replace(' ', '');
  uptoYear = Variable.UPTO_YEAR.replace(' ', '');
  isProjectResReceived = false;
  isFormResReceived = false;
  applyFilterSubject = new Subject<any>();

  @Input() projectId: number;
  constructor(
    private matDialog: MatDialog,
    private sharedService: SharedService,
    private sflFormsService: SflFormsService,
    private projectService: ProjectService,
    private alertService: SweetAlertService,
    private activatedRoute: ActivatedRoute
  ) {
    this.subscription.add(this.activatedRoute.queryParams.subscribe(params => {
      if (params && params.generateFormModal) {
        this.generateFormModal(null);
      }
    }));
    this.pageable.page = 0;
    this.pageable.size = 10;
    this.pageable.sort = Variable.END_DATE;
    this.pageable.direction = Variable.DESC;
  }

  ngOnInit() {
    this.uaaUserId = this.sharedService.getUserId();
    this.getProjects();
    this.getCreatedForms();
    this.subscription.add(this.applyFilterSubject.subscribe((res: any) => {
      if (res.isFormResReceived && res.isProjectResReceived) {
        this.applyFilter();
      }
    }));
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
    this.filterGeneratedForm.projectId = this.projectId;
    this.applyFilter();
  }

  getSorting(event) {
    this.pageable.sort = event.active;
    this.pageable.direction = event.direction;
    this.applyFilter();
  }

  getPagination(event) {
    this.pageable.size = event.pageSize;
    this.pageable.page = event.pageIndex;
    this.applyFilter(false);
  }

  getProjects() {
    this.subscription.add(this.projectService.getActiveProjects().subscribe((res: Project[]) => {
      this.projects = res;
      this.isProjectResReceived = true;
      this.applyFilterSubject.next({
        isProjectResReceived: this.isProjectResReceived,
        isFormResReceived: this.isFormResReceived
      });
    }));
  }

  getCreatedForms() {
    this.subscription.add(
      this.sflFormsService.getCreatedForms().subscribe((res: SflFormsModel[]) => {
        this.createdForms = res;
        this.isFormResReceived = true;
        this.applyFilterSubject.next({
          isProjectResReceived: this.isProjectResReceived,
          isFormResReceived: this.isFormResReceived
        });
      })
    );
  }

  applyFilter(set_page_zero = true) {
    this.pageable.page = set_page_zero ? 0 : this.pageable.page;
    this.showLoader = true;
    this.noDataFound = true;

    this.filterGeneratedForm.startDate = DateUtils.convertDate(this.filterGeneratedForm.startDate);
    this.filterGeneratedForm.endDate = DateUtils.convertDate(this.filterGeneratedForm.endDate);

    const currentSort = this.pageable.sort;
    this.pageable.sort = this.pageable.sort + ',' + this.pageable.direction;
    this.filterGeneratedForm.createdBy = this.uaaUserId;
    this.subscription.add(this.sflFormsService.getFilteredGeneratedForms(this.filterGeneratedForm, this.pageable)
      .pipe(
        finalize(() => {
          this.noDataFound = this.dataSource.data.length <= 0;
          this.showLoader = false;
        })
      )
      .subscribe((res: FilterGeneratedFormPageable) => {
        this.filterGeneratedFormPageable = res;
        this.mapGeneratedForm();
        this.dataSource = new MatTableDataSource<GenerateForm>(this.filterGeneratedFormPageable.content);
        this.pageable.size = this.filterGeneratedFormPageable.pageable.pageSize;
        this.pageable.page = this.filterGeneratedFormPageable.pageable.pageNumber;
      })
    );
    this.pageable.sort = currentSort;

    this.filterGeneratedForm.startDate = DateUtils.convertStrToDate(this.filterGeneratedForm.startDate);
    this.filterGeneratedForm.endDate = DateUtils.convertStrToDate(this.filterGeneratedForm.endDate);
  }

  resetFilter(parameter: string) {
    this.filterGeneratedForm[parameter] = undefined;
  }

  resetAllFilter() {
    this.filterGeneratedForm = new FilterGeneratedForm();
    this.filterGeneratedForm.projectId = this.projectId;
    this.applyFilter();
  }

  generateFormModal(generateForm: GenerateForm) {
    this.matDialog.open(GenerateFormModalComponent, {
      width: Variable.BOX_WIDTH_VALUE,
      data: {
        generateFormModel: generateForm
      }
    });
  }

  deleteForm(generateForm: GenerateForm) {
    this.alertService.deleteAlert(Variable.THIS_FORM).then(value => {
      if (value === true) {
        this.sflFormsService.deleteGeneratedForm(generateForm.id).subscribe(() => {
          this.applyFilter(false);
        });
      }
    });
  }

  mapGeneratedForm() {
    this.filterGeneratedFormPageable.content = this.filterGeneratedFormPageable.content.map((e) => {
      const project = this.projects.find(element => element.id === e.projectId);
      if (project && project.name) {
        e.projectName = project.name;
      } else {
        e.projectName = Variable.DISABLED;
      }
      const formsModel = this.createdForms.find(element => element.id === e.createdFormId);
      if (formsModel && formsModel.formName) {
        e.formName = formsModel.formName;
      } else {
        e.formName = Variable.DISABLED;
      }
      return e;
    });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
