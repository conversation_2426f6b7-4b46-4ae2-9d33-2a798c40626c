import { Component, Inject, On<PERSON><PERSON>roy, OnInit } from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material";
import _ from "lodash";
import { Subscription } from "rxjs";
import { EmployeeManagementComponent } from "src/app/feature/employee-management";
import { Employee } from "../../employee-management/employee-management.model";
import { Designation, EditLevelDialogData, Role } from "../profile.model";
import { ProfileService } from "../profile.service";

@Component({
  selector: "sfl-level-profile",
  templateUrl: "./edit-level.component.html",
})
export class EditLevelComponent implements OnInit, OnDestroy {
  subscription: Subscription = new Subscription();
  designations: Designation[] = [];
  roles: Role[] = [];
  employeeDetails: Employee;

  constructor(@Inject(MAT_DIALOG_DATA) public data: EditLevelDialogData, public dialogRef: MatDialogRef<EmployeeManagementComponent>, private profileService: ProfileService) {}

  ngOnInit() {
    this.employeeDetails = _.cloneDeep(this.data.employee);
    this.subscription.add(
      this.profileService.getDesignations().subscribe((res: Designation[]) => {
        this.designations = res;
      })
    );

    this.subscription.add(
      this.profileService.getRoles().subscribe((res: Role[]) => {
        this.roles = res;
      })
    );
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  saveEmployeeDetails() {
    this.data.employee = this.employeeDetails;
    this.data.isEdit = true;
    this.dialogRef.close(this.data);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
