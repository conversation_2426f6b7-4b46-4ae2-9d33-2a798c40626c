import { BidiModule } from '@angular/cdk/bidi';
import { CommonModule } from '@angular/common';
import { HTTP_INTERCEPTORS, HttpClient, HttpClientModule } from '@angular/common/http';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import {
  MAT_DIALOG_DEFAULT_OPTIONS,
  MatAutocompleteModule,
  MatBadgeModule,
  MatButtonModule,
  MatButtonToggleModule,
  MatCardModule,
  MatCheckboxModule,
  MatChipsModule,
  MatDatepickerModule,
  MatDialogModule,
  MatExpansionModule,
  MatFormFieldModule,
  MatIconModule,
  MatInputModule,
  MatListModule,
  MatMenuModule,
  MatNativeDateModule,
  MatPaginatorModule,
  MatProgressBarModule,
  MatProgressSpinnerModule,
  MatSelectModule,
  MatSidenavModule,
  MatSlideToggleModule,
  MatSliderModule,
  MatSnackBarModule,
  MatSortModule,
  MatStepperModule,
  MatTableModule,
  MatTabsModule,
  MatToolbarModule,
  MatTooltipModule,
  MatTreeModule,
} from '@angular/material';
import { MatRadioModule } from '@angular/material/radio';
import { HttpClientService, SharedService, SnackBarService, httpClientServiceCreator } from '.';
import { AppInterceptor } from './app-intercepter';
import { ChangePasswordDialogComponent } from './components/change-password-dialog/change-password-dialog.component';
import { ArrayDisplayPipe } from './pipes/array-display.pipe';
import { DateToIstPipe } from './pipes/date-to-ist.pipe';
import { ReplaceStringPipe } from './pipes/replace-string.pipe';
import { ConvertSecondsToHourPipe } from './pipes/time-converter.pipe';
import { DisplayTimePipe } from './pipes/time-display.pipe';
import { AuthGuardService } from './service/auth-guard.service';
import { RoleGuardService } from './service/role-guard.service';

@NgModule({
  imports: [
    MatSortModule,
    MatTableModule,
    HttpClientModule,
    FormsModule,
    ReactiveFormsModule,
    MatInputModule,
    MatSidenavModule,
    MatCardModule,
    MatMenuModule,
    MatCheckboxModule,
    MatIconModule,
    MatButtonModule,
    MatToolbarModule,
    MatTabsModule,
    MatListModule,
    MatTooltipModule,
    MatChipsModule,
    MatButtonToggleModule,
    MatSlideToggleModule,
    MatSelectModule,
    MatProgressBarModule,
    MatExpansionModule,
    MatPaginatorModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatSliderModule,
    FlexLayoutModule,
    BidiModule,
    MatStepperModule,
    MatFormFieldModule,
    MatBadgeModule,
    MatDialogModule,
    MatSnackBarModule,
    MatTreeModule,
    MatAutocompleteModule,
    MatRadioModule,
    CommonModule,
  ],
  declarations: [ReplaceStringPipe, ArrayDisplayPipe, DateToIstPipe, DisplayTimePipe, ConvertSecondsToHourPipe, ChangePasswordDialogComponent],
  providers: [
    SharedService,
    AuthGuardService,
    RoleGuardService,
    SnackBarService,
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AppInterceptor,
      multi: true,
    },
    {
      provide: HttpClientService,
      useFactory: httpClientServiceCreator,
      deps: [HttpClient],
    },
    {
      provide: MAT_DIALOG_DEFAULT_OPTIONS,
      useValue: { hasBackdrop: false },
    },
  ],
  exports: [
    MatTableModule,
    MatSortModule,
    FormsModule,
    CommonModule,
    ReactiveFormsModule,
    MatInputModule,
    MatSidenavModule,
    MatCardModule,
    MatMenuModule,
    MatCheckboxModule,
    MatIconModule,
    MatButtonModule,
    MatToolbarModule,
    MatTooltipModule,
    MatChipsModule,
    MatButtonToggleModule,
    MatTabsModule,
    MatListModule,
    MatSlideToggleModule,
    MatSelectModule,
    MatProgressBarModule,
    MatExpansionModule,
    MatPaginatorModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatSliderModule,
    FlexLayoutModule,
    BidiModule,
    MatStepperModule,
    MatFormFieldModule,
    MatBadgeModule,
    MatDialogModule,
    MatSnackBarModule,
    MatTreeModule,
    MatAutocompleteModule,
    MatProgressSpinnerModule,
    ReplaceStringPipe,
    ArrayDisplayPipe,
    DateToIstPipe,
    DisplayTimePipe,
    ConvertSecondsToHourPipe,
    MatRadioModule,
  ],
  entryComponents: [ChangePasswordDialogComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class SharedModule {}
