import { Injectable } from '@angular/core';
import { HttpClientService } from '../../shared';
import { AppConfig } from '../../app.config';
import {LoginModel} from './login.model';
import {EmployeeProfileParam} from './login.model';


@Injectable()
export class LoginService {

  constructor(private http: HttpClientService) { }

  login(loginParam: LoginModel) {
    return this.http.post(AppConfig.AUTH_LOGIN, loginParam);
  }

  setUaaUser(uaaUserId: number) {
    return this.http.get(AppConfig.SET_UAA_USER + uaaUserId);
  }

  getAccount() {
    return this.http.get(AppConfig.ACCOUNT_URL);
  }

  setEmployeeProfile(uaaUserId: number, employeeProfileParam: EmployeeProfileParam) {
   return this.http.put(AppConfig.SET_EMPLOYEE_PROFILE + uaaUserId, employeeProfileParam);
  }

}
