import { Injectable } from '@angular/core';
import { AppConfig } from '../../app.config';
import { HttpClientService } from '../../shared';
import { EmployeeProfileParam, LoginModel } from './login.model';


@Injectable()
export class LoginService {

  constructor(private http: HttpClientService) { }

  login(loginParam: LoginModel) {
    return this.http.post(AppConfig.AUTH_LOGIN, loginParam);
  }

  setUaaUser(uaaUserId: number) {
    return this.http.get(AppConfig.SET_UAA_USER + uaaUserId);
  }

  getAccount() {
    return this.http.get(AppConfig.ACCOUNT_URL);
  }

  setEmployeeProfile(uaaUserId: number, employeeProfileParam: EmployeeProfileParam) {
   return this.http.put(AppConfig.SET_EMPLOYEE_PROFILE + uaaUserId, employeeProfileParam);
  }

  refreshToken(refreshTokenRequest: any) {
    return this.http.post(AppConfig.AUTH_REFRESH_TOKEN, refreshTokenRequest);
  }

}
