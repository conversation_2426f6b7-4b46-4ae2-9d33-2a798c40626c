import { Pipe, PipeTransform } from "@angular/core";

@Pipe({
  name: "convertSecondsToHour",
  pure: true,
})
export class ConvertSecondsToHourPipe implements PipeTransform {
  transform(value: number): string {
    if (value) {
      const hours = Math.floor(value / 3600);
      const minutes = Math.floor((value % 3600) / 60);
      return `${hours}:${String(minutes).padStart(2, "0")}`;
    }
    return "";
  }
}
