{"name": "angular", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "prod-build": "ng build --prod --aot --build-optimizer", "bundle-report": "ng build --prod --aot --build-optimizer --stats-json && webpack-bundle-analyzer dist/stats-es2015.json"}, "private": true, "dependencies": {"@angular/animations": "^9.1.13", "@angular/cdk": "^8.2.3", "@angular/common": "^9.1.13", "@angular/compiler": "^9.1.13", "@angular/core": "^9.1.13", "@angular/flex-layout": "^9.0.0-beta.31", "@angular/forms": "^9.1.13", "@angular/material": "^8.2.3", "@angular/platform-browser": "^9.1.13", "@angular/platform-browser-dynamic": "^9.1.13", "@angular/router": "^9.1.13", "@types/lodash": "4.14.147", "chart.js": "^2.9.2", "core-js": "^3.4.1", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "hammerjs": "^2.0.8", "intl": "^1.2.5", "jquery": "^3.4.1", "moment": "^2.24.0", "moment-business-days": "^1.1.3", "ngx-webstorage": "^4.0.1", "rxjs": "6.5.3", "rxjs-compat": "^6.5.3", "sweetalert2": "^9.3.6", "tslib": "^1.10.0", "xlsx": "^0.18.5", "zone.js": "~0.10.2"}, "devDependencies": {"@angular-devkit/build-angular": "^0.901.15", "@angular/cli": "^9.1.13", "@angular/compiler-cli": "^9.1.13", "@angular/language-service": "^9.1.13", "@types/jasmine": "~3.4.6", "@types/jasminewd2": "~2.0.8", "@types/node": "^12.11.1", "codelyzer": "^5.1.2", "jasmine-core": "~3.5.0", "jasmine-spec-reporter": "~4.2.1", "karma": "~4.4.1", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~2.1.0", "karma-jasmine": "~2.0.1", "karma-jasmine-html-reporter": "^1.4.2", "protractor": "~5.4.2", "ts-node": "~8.5.0", "tslint": "~5.9.1", "typescript": "^3.8.3", "webpack-bundle-analyzer": "^3.6.0"}}