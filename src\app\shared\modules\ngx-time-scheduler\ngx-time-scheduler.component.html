<div class="time-sch-wrapper" [style.height]="maxHeight">
  <div class="time-sch-header-wrapper mb-1">
    <div class="time-sch-period-container" *ngIf="showActionButtons">
      <button class="btn" *ngFor="let period of periods" [ngClass]="period.classes" (click)="changePeriod(period)" [title]="period.tooltip ? period.tooltip : ''">{{ period.name }}</button>
    </div>

    <div class="time-sch-time-container" *ngIf="showActionButtons">
      <button class="btn" *ngIf="showGoto" (click)="showGotoModal = !showGotoModal">{{ text.GotoButton }}</button>
      <div class="goto-modal" *ngIf="showGotoModal">
        <label>
          <input type="date" (change)="gotoDate($event.target?.value)" />
        </label>
      </div>

      <button class="btn" *ngIf="showToday" (click)="gotoToday()">{{ text.TodayButton }}</button>
      <button class="btn" (click)="previousPeriod()">{{ text.PrevButton }}</button>
      <button class="btn" (click)="nextPeriod()">{{ text.NextButton }}</button>
    </div>

    <h3 class="text-center m-0" *ngIf="showHeaderTitle">
      {{ text?.HeaderTitle ? text.HeaderTitle : start.locale(locale).format(headerFormat) + " - " + end.locale(locale).format(headerFormat) }}
    </h3>
  </div>

  <div class="time-sch-table-wrapper">
    <table class="time-sch-table">
      <tr *ngFor="let timeFrameHeader of header; let i = index; trackBy: trackByFn">
        <td class="time-sch-section text-center" *ngIf="i === 0" [rowSpan]="periods.length">
          {{ text.SectionTitle }}
        </td>

        <td class="text-center" *ngFor="let dateTime of timeFrameHeader.headerDetails; trackBy: trackByFn" [colSpan]="dateTime.colspan" [title]="dateTime.tooltip ? dateTime.tooltip : ''">
          {{ dateTime.name }}
        </td>
      </tr>
    </table>

    <div class="time-sch-content-wrap">
      <table class="time-sch-table">
        <tr *ngFor="let sectionItem of sectionItems; trackBy: trackByFn" [style.height]="sectionItem.minRowHeight + 'px'">
          <td
            class="time-sch-section text-center"
            #sectionTd
            (click)="events.SectionClickEvent ? events.SectionClickEvent(sectionItem.section) : false"
            (contextmenu)="events.SectionContextMenuEvent ? events.SectionContextMenuEvent(sectionItem.section, $event) : false"
            [style.cursor]="events.SectionClickEvent ? 'pointer' : ''"
            [title]="sectionItem.section.tooltip ? sectionItem.section.tooltip : ''"
          >
            {{ getName(sectionItem.section.name, 1) }} <br />
            <small>
              {{ getName(sectionItem.section.name, 2) }}
            </small>
            <br />
            {{ getName(sectionItem.section.name, 3) }}
          </td>

          <td *ngFor="let td of header[header.length - 1].headerDetails; trackBy: trackByFn"></td>
        </tr>
      </table>

      <div class="time-sch-section-wrapper" [style.left]="SectionLeftMeasure" cdkDropListGroup>
        <div
          class="time-sch-section-container"
          cdkDropList
          cdkDropListSortingDisabled
          [cdkDropListData]="sectionItem.section"
          (cdkDropListDropped)="drop($event)"
          *ngFor="let sectionItem of sectionItems; trackBy: trackByFn"
          [style.height]="sectionItem.minRowHeight + 'px'"
        >
          <div
            class="time-sch-item"
            cdkDrag
            cdkDragLockAxis="y"
            cdkDragBoundary=".time-sch-section-wrapper"
            [cdkDragData]="itemMeta.item"
            [cdkDragDisabled]="!allowDragging"
            *ngFor="let itemMeta of sectionItem.itemMetas"
            [ngClass]="itemMeta.item.classes"
            (click)="events.ItemClicked ? events.ItemClicked(itemMeta.item) : false"
            (contextmenu)="events.ItemContextMenu ? events.ItemContextMenu(itemMeta.item, $event) : false"
            [style.height]="minRowHeight + 'px'"
            [style.top]="itemMeta.cssTop + 'px'"
            [style.left]="itemMeta.cssLeft + '%'"
            [style.width]="itemMeta.cssWidth + '%'"
          >
            <div
              class="item-drag-placeholder"
              *cdkDragPlaceholder
              [style.height]="minRowHeight - 6 + 'px'"
              [style.left]="itemMeta.cssLeft + '%'"
              [style.width]="'calc(' + itemMeta.cssWidth + '% - 6px)'"
            ></div>
            <div class="time-sch-item-start" *ngIf="itemMeta.isStart"></div>
            <div class="time-sch-item-content" [title]="itemMeta.item.tooltip ? itemMeta.item.tooltip : ''">
              {{ itemMeta.item.name }}
            </div>
            <div class="time-sch-item-end" *ngIf="itemMeta.isEnd"></div>
          </div>
        </div>

        <div class="time-sch-current-time" *ngIf="showCurrentTime" [title]="currentTimeTitle" [style.visibility]="currentTimeVisibility" [style.left]="currentTimeIndicatorPosition"></div>
      </div>
    </div>
  </div>
</div>
