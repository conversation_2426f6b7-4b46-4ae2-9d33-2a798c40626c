<div fxLayout="column" fxFlex="100">
  <form #filter="ngForm" (ngSubmit)="applyFilter()">
    <div fxLayout="row column" fxLayoutAlign="start center" fxFlex="100" class="sfl-card">
      <mat-card-actions class="filter-input ml-1 mr-1">
        <mat-form-field>
          <mat-select placeholder="Select Form" name="createdFormId" [(ngModel)]="filterGeneratedForm.createdFormId">
            <mat-option *ngFor="let createdForm of createdForms" [value]="createdForm.id">
              {{createdForm?.formName}} &nbsp; <i>Version &nbsp;{{createdForm?.version}}</i>
            </mat-option>
          </mat-select>
          <button mat-icon-button matSuffix matTooltip="clear"
                  *ngIf="filterGeneratedForm.createdFormId" (click)="resetFilter('createdFormId')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1">
        <mat-form-field>
          <mat-select placeholder="Date Filter" name="filterBy" [(ngModel)]="filterGeneratedForm.filterBy">
            <mat-option *ngFor="let dateWise of dateWiseFilter" [value]="dateWise.replace(' ', '')">{{dateWise}}
            </mat-option>
          </mat-select>
          <button mat-icon-button matSuffix matTooltip="clear" *ngIf="filterGeneratedForm.filterBy"
                  (click)="resetFilter('filterBy')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1" *ngIf="filterGeneratedForm.filterBy === uptoYear">
        <mat-form-field>
          <input matInput type="text" placeholder="Year" name="upToYear" aria-label="year" minlength="4" maxlength="4" pattern="\d*"
                 [required]="filterGeneratedForm.filterBy === uptoYear" [(ngModel)]="filterGeneratedForm.upToYear" #uptoYearInput="ngModel" />
          <button mat-icon-button matSuffix matTooltip="clear" *ngIf="filterGeneratedForm.upToYear"
                  (click)="resetFilter('upToYear')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
          <mat-error *ngIf="uptoYearInput.touched && !uptoYearInput.valid">
            <small class="mat-text-warn" *ngIf="uptoYearInput?.errors.required">Year is required.</small>
            <small class="mat-text-warn"
                   *ngIf="uptoYearInput.errors.minlength || uptoYearInput.errors.maxlength || uptoYearInput.errors.pattern">
              Invalid Year.</small>
          </mat-error>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1" *ngIf="filterGeneratedForm.filterBy === freeSearch">
        <mat-form-field>
          <input matInput placeholder="Start date" name="startDate" aria-label="start-date" (focus)="sDate.open()" (click)="sDate.open()"
                 [matDatepicker]="sDate" [max]="filterGeneratedForm.endDate"
                 [required]="filterGeneratedForm.filterBy === freeSearch" [(ngModel)]="filterGeneratedForm.startDate">
          <mat-datepicker-toggle matSuffix [for]="sDate"></mat-datepicker-toggle>
          <mat-datepicker #sDate></mat-datepicker>
          <button mat-icon-button matSuffix matTooltip="clear" *ngIf="filterGeneratedForm.startDate"
                  (click)="resetFilter('startDate')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1" *ngIf="filterGeneratedForm.filterBy === freeSearch">
        <mat-form-field>
          <input matInput placeholder="End date" name="endDate" aria-label="end-date" (focus)="eDate.open()" (click)="eDate.open()"
                 [matDatepicker]="eDate" [min]="filterGeneratedForm.startDate"
                 [required]="filterGeneratedForm.filterBy === freeSearch" [(ngModel)]="filterGeneratedForm.endDate">
          <mat-datepicker-toggle matSuffix [for]="eDate"></mat-datepicker-toggle>
          <mat-datepicker #eDate></mat-datepicker>
          <button mat-icon-button matSuffix matTooltip="clear" *ngIf="filterGeneratedForm.endDate"
                  (click)="resetFilter('endDate')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-button-group ml-1">
        <button mat-raised-button class="bt-sfl mr-10px" type="submit"
                [disabled]="filter.form.invalid">Filter</button>
        <button class="bt-flat" type="button" (click)="resetAllFilter()">Reset All</button>
      </mat-card-actions>
    </div>
  </form>

  <div class="sfl-card">
    <div class="p-25" fxLayoutAlign="center center" fxFlex="100" *ngIf="showLoader">
      <mat-progress-spinner color='warn' mode="indeterminate"></mat-progress-spinner>
    </div>

    <div *ngIf="!showLoader && noDataFound" class="w-100 mt-40 no-data-found-text">No Generated Form Found.</div>

    <div class="w-100" *ngIf="!showLoader && !noDataFound">
      <table class="w-auto" mat-table matSort matSortDisableClear [dataSource]="dataSource"
             [matSortActive]="pageable.sort" [matSortDirection]="pageable.direction" (matSortChange)="getSorting($event)">
        <ng-container matColumnDef="formName">
          <mat-header-cell *matHeaderCellDef fxFlex.gt-sm="15"> Form Name </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-sm="15">
            {{element?.formName}}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="projectName">
          <mat-header-cell *matHeaderCellDef fxFlex.gt-sm="20"> Project Name </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-sm="20">
            {{element?.projectName}}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="sprint">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-sm="5"> # Sprint </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-sm="5">
            {{element?.sprint}} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="startDate">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-sm="15"> Start date </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-sm="15">
            {{element?.startDate | dateToIst | date: dateFormat}} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="endDate">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-sm="15"> End date </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-sm="15">
            <span>
              {{element?.endDate | dateToIst | date: dateFormat}}
            </span>
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="fillableBy">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-sm="15" class="description-cell"> Fillable by </mat-header-cell>
          <mat-cell *matCellDef="let element" class="description-cell" fxFlex.gt-sm="15">
            <span>
              {{element?.fillableBy | arrayDisplay | replaceString: '-' | titlecase}}
            </span>
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxLayoutAlign="center center" fxFlex.gt-sm="15"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxLayoutAlign="center center" fxFlex.gt-sm="15">
            <button mat-icon-button matTooltip="View Responses"
                    [routerLink]="[formsResponseURL, element?.id, element?.formName]">
              <mat-icon>visibility</mat-icon>
            </button>
            <button mat-icon-button matTooltip="Edit" (click)="generateFormModal(element)">
              <mat-icon>edit</mat-icon>
            </button>
            <button mat-icon-button matTooltip="Delete" (click)="deleteForm(element)">
              <mat-icon>delete</mat-icon>
            </button>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
      </table>
      <mat-paginator [length]="filterGeneratedFormPageable.totalElements" [pageSizeOptions]="[10, 20, 25]"
                     [pageIndex]="pageable.page" [pageSize]="pageable.size" (page)="getPagination($event)" showFirstLastButtons>
      </mat-paginator>
    </div>
  </div>

</div>
