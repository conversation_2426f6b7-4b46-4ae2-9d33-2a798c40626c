.time-sheet .mat-header-cell,
.time-sheet .mat-footer-cell,
.time-sheet .mat-cell {
  border: 1px #e3e1ec solid;
  padding: 0;
  text-align: center;
  justify-content: center;
  min-width: 80px;
}

.time-sheet .mat-row,
.time-sheet .mat-footer-row,
.time-sheet .mat-header-row {
  border-bottom-width: 0px;
}

.time-sheet mat-header-cell:first-of-type,
.time-sheet mat-cell:first-of-type {
  padding-left: 0px;
}

.time-sheet .mat-footer-cell,
.time-sheet .mat-footer-row,
.time-sheet .mat-header-cell,
.time-sheet .mat-header-row,
.time-sheet .totalHours {
  font-weight: 800;
}

.time-sheet .grey-bg {
  background-color: #f1f0f5;
}

.time-sheet .pin {
  rotate: -45deg;
  margin-left: 8px;
}
.time-sheet .orange-color {
  color: #f37d3f;
  font-size: 16px;
}
.time-sheet .jobcode-cell {
  min-width: 250px !important;
  overflow-wrap: anywhere;
}

.job-code {
  max-width: 490px;
}

.reset-filter {
  height: 50px;
  border-radius: 4px;
}

.jobcode-display-name {
  text-align: left !important;
}
.table-container {
  margin-top: 12px;
}
.body-content {
  width: calc(100% - 32px);
}
