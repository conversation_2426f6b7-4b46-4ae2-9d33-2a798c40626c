<div class="body-content" fxLayout="column" fxFlex="100">
    <div fxLayout="row" fxLayoutAlign="space-between" class="mb-1">
        <h1 mat-dialog-title>Work Hours Report</h1>
    </div>

    <hr class="header-divider">
    <div class="sfl-card" fxLayout="column">
        <div class="p-25" fxLayoutAlign="center center" fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100"
            fxFlex.gt-xs="100" *ngIf="showLoader">
            <mat-progress-spinner color='warn' mode="indeterminate"></mat-progress-spinner>
        </div>

        <div *ngIf="!showLoader && noDataFound" class="w-100 mt-40 no-data-found-text">No Data Found.</div>

        <div class="w-100" *ngIf="!showLoader && !noDataFound">
            <table mat-table class="w-auto" [dataSource]="dataSource" matSort matSortDirection="desc"
                matSortActive="levelId">

                <ng-container matColumnDef="levelId">
                    <th mat-header-cell *matHeaderCellDef fxFlex="33" class="title-font-size d-flex"> Emp Level </th>
                    <td mat-cell *matCellDef="let element" fxFlex="33" class="d-flex"> Level {{element.levelId}} </td>
                </ng-container>

                <ng-container matColumnDef="estHours">
                    <th mat-header-cell *matHeaderCellDef fxFlex="33" class="title-font-size d-flex"> Est. Work hours
                    </th>
                    <td mat-cell *matCellDef="let element" fxFlex="33" class="d-flex"> {{element.estHours? element.estHours : '0'}} </td>
                </ng-container>

                <ng-container matColumnDef="calHours">
                    <th mat-header-cell *matHeaderCellDef fxFlex="33" class="title-font-size d-flex"> Cal. Work hours
                    </th>
                    <td mat-cell *matCellDef="let element" fxFlex="33" class="d-flex"> {{element.calHours}} </td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
        </div>
        <div mat-dialog-actions>
            <button class="bt-flat" (click)="closeModal()">Cancel</button>
        </div>
    </div>
</div>