import { Routes } from '@angular/router';
import { WorklogComponent } from './index';
import { EmpWorklogComponent } from './emp-worklog/emp-worklog.component';
import { MissedWorklogComponent } from './missed-worklog/missed-worklog.component';
import { WorklogModalComponent } from './worklog-modal/worklog-modal.component';
import {RoleGuardService} from '../../shared/service/role-guard.service';
import {ROLES} from '../../shared';

export const worklogRoutes: Routes = [
  {
    path: '',
    component: WorklogComponent
  },
  {
    path: '',
    component: WorklogModalComponent
  },
  {
    path: 'emp',
    canActivate: [RoleGuardService],
    data: {
      allowedRoles: [ROLES.SUPER_ADMIN, ROLES.ADMIN, ROLES.LEAD, ROLES.HR]
    },
    component: EmpWorklogComponent
  },
  {
    path: 'missed',
    canActivate: [RoleGuardService],
    data: {
      allowedRoles: [ROLES.SUPER_ADMIN, ROLES.ADMIN, ROLES.LEAD, ROLES.HR]
    },
    component: MissedWorklogComponent
  }
];
