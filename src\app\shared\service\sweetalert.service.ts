import { Injectable } from '@angular/core';
import Swal from 'sweetalert2';


@Injectable()
export class SweetAlertService {

 constructor( ) { }

  public async errorAlert(msg) {
   return await Swal.fire({
     title: msg,
     icon: 'error'
   }).then((result) => {
     return !!result.value;
   });
  }

  public async deleteAlert(message: string) {
    return await Swal.fire({
      title: 'Are you sure?',
      text: 'you want to delete ' + message,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'No, keep it'
    }).then((result) => {
      return !!result.value;
    });
  }

}
