<div class="body-content" fxLayout="column" fxFlex="100">
  <div fxFlex fxLayout="row" fxLayoutAlign="space-between" class="mb-1">
    <mat-card-title class="p-10">Leaves</mat-card-title>
    <div>
      <button mat-raised-button class="bt-sfl mr-10px" (click)="getLeaveExcel()" *ngIf="isActive">Export Employee Leave</button>
      <button mat-raised-button class="bt-sfl" (click)="openSaveLeave(null)">Add New Leave</button>
    </div>
  </div>

  <hr class="header-divider">

  <form #filter="ngForm" (ngSubmit)="applyFilter()">
    <div fxLayout="row column" fxLayoutAlign="start center" fxFlex="100" class="sfl-card">
      <mat-card-actions class="filter-input ml-1 mr-1">
        <mat-form-field>
          <mat-select placeholder="Select Username" name="userName" [(ngModel)]="filterLeaves.uaaUserId" [disabled]="!isActive">
            <mat-option *ngFor="let user of users" [value]="user.id">{{ user?.fullName }}</mat-option>
          </mat-select>
          <button
            mat-icon-button
            matSuffix
            class="float-center"
            matTooltip="clear"
            *ngIf="filterLeaves.uaaUserId"
            (click)="resetFilter('uaaUserId')"
            [disabled]="!isActive"
          >
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1">
        <mat-form-field>
          <mat-select placeholder="Select Leave Type" name="leaveType" [(ngModel)]="filterLeaves.leaveType">
            <mat-option *ngFor="let leaveType of leaveTypes" [value]="leaveType">{{ leaveType }}</mat-option>
          </mat-select>
          <button
            mat-icon-button
            matSuffix
            class="float-center"
            matTooltip="clear"
            *ngIf="filterLeaves.leaveType"
            (click)="resetFilter('leaveType')"
          >
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1">
        <mat-form-field>
          <mat-select placeholder="Select Leave Status" name="leaveStatus" [(ngModel)]="filterLeaves.leaveStatus">
            <mat-option value="active">Active</mat-option>
            <mat-option value="inactive">InActive</mat-option>
          </mat-select>
          <button
            mat-icon-button
            matSuffix
            class="float-center"
            matTooltip="clear"
            *ngIf="filterLeaves.leaveStatus"
            (click)="resetFilter('leaveStatus')"
          >
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1">
        <mat-form-field>
          <mat-select placeholder="Date Filter" name="filterBy" [(ngModel)]="filterLeaves.filterBy">
            <mat-option *ngFor="let dateWise of dateWiseFilter" [value]="dateWise.replace(' ', '')">{{ dateWise }}</mat-option>
          </mat-select>
          <button
            mat-icon-button
            matSuffix
            class="float-center"
            matTooltip="clear"
            *ngIf="filterLeaves.filterBy"
            (click)="resetFilter('filterBy')"
          >
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1" *ngIf="filterLeaves.filterBy === uptoYear">
        <mat-form-field>
          <input matInput type="text" placeholder="Year" name="upToYear" minlength="4" maxlength="4" pattern="\d*" aria-label="year"
                 [required]="filterLeaves.filterBy === uptoYear" [(ngModel)]="filterLeaves.upToYear" #uptoYearInput="ngModel" />
          <button mat-icon-button matSuffix matTooltip="clear" *ngIf="filterLeaves.upToYear"
                  (click)="resetFilter('upToYear')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
          <mat-error *ngIf="uptoYearInput.touched && !uptoYearInput.valid">
            <small class="mat-text-warn" *ngIf="uptoYearInput?.errors.required">Year is required.</small>
            <small class="mat-text-warn"
                   *ngIf="uptoYearInput.errors.minlength || uptoYearInput.errors.maxlength || uptoYearInput.errors.pattern">
              Invalid Year.</small>
          </mat-error>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1" *ngIf="filterLeaves.filterBy === freeSearch">
        <mat-form-field>
          <input
            matInput
            placeholder="Start date"
            name="startDate"
            aria-label="start-date"
            (focus)="sDate.open()"
            (click)="sDate.open()"
            [matDatepicker]="sDate"
            [max]="filterLeaves.endDate"
            [required]="filterLeaves.filterBy === freeSearch"
            [(ngModel)]="filterLeaves.startDate"
          />
          <mat-datepicker-toggle matSuffix [for]="sDate"></mat-datepicker-toggle>
          <mat-datepicker #sDate></mat-datepicker>
          <button
            mat-icon-button
            matSuffix
            class="float-center"
            matTooltip="clear"
            *ngIf="filterLeaves.startDate"
            (click)="resetFilter('startDate')"
          >
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1" *ngIf="filterLeaves.filterBy === freeSearch">
        <mat-form-field>
          <input
            matInput
            placeholder="End date"
            name="endDate"
            aria-label="end-date"
            (focus)="eDate.open()"
            (click)="eDate.open()"
            [matDatepicker]="eDate"
            [min]="filterLeaves.startDate"
            [required]="filterLeaves.filterBy === freeSearch"
            [(ngModel)]="filterLeaves.endDate"
          />
          <mat-datepicker-toggle matSuffix [for]="eDate"></mat-datepicker-toggle>
          <mat-datepicker #eDate></mat-datepicker>
          <button
            mat-icon-button
            matSuffix
            class="float-center"
            matTooltip="clear"
            *ngIf="filterLeaves.endDate"
            (click)="resetFilter('endDate')"
          >
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-button-group ml-1">
        <button mat-raised-button class="bt-sfl mr-10px" type="submit"
                [disabled]="filter.form.invalid">Filter</button>
        <button class="bt-flat" type="button" (click)="resetAllFilter()">Reset All</button>
      </mat-card-actions>
    </div>
  </form>

  <div class="sfl-card">
    <div class="p-25" fxLayoutAlign="center center" fxFlex="100" *ngIf="showLoader">
      <mat-progress-spinner color="warn" mode="indeterminate"></mat-progress-spinner>
    </div>

    <div *ngIf="!showLoader && noDataFound" class="w-100 mt-40 no-data-found-text">No Leave Found.</div>

    <div class="w-100" *ngIf="!showLoader && !noDataFound">
      <table
        class="w-auto"
        mat-table
        matSort
        matSortDisableClear
        [dataSource]="dataSource"
        [matSortActive]="pageable.sort"
        [matSortDirection]="pageable.direction"
        (matSortChange)="getSorting($event)"
      >
        <ng-container matColumnDef="userName">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-sm="12"> Name </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-sm="12">
            {{ element?.userName }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="fromDate">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-sm="10"> Start Date </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-sm="10"> {{ element?.fromDate | dateToIst | date: dateFormat }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="toDate">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-sm="10"> End Date </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-sm="10"> {{ element?.toDate | dateToIst | date: dateFormat }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="noOfDay">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-lg="8" fxFlex.gt-sm="5"> Day </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-sm="5"> {{ element?.noOfDay }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="whichHalf">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-lg="8" fxFlex.gt-sm="5"> Half Day </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-sm="5"> {{ element?.whichHalf }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="leaveType">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-sm="20"> Leave Type </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-sm="20"> {{ element?.leaveType }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="leaveReason">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-sm="26"> Leave Reason </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-sm="26">
            <span matTooltipClass="table-mat-tooltip" [matTooltip]="element?.leaveReason">
              {{ element?.leaveReason }}
            </span>
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="status">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-lg="8" fxFlex.gt-sm="8" fxLayoutAlign="center center">
            Leave Status
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-sm="8" fxLayoutAlign="center center">
            <mat-icon *ngIf="element.status === false" class="material-icons warn">clear</mat-icon>
            <mat-icon *ngIf="element.status === true" class="material-icons success">done</mat-icon>
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="action" *ngIf="isActive">
          <mat-header-cell *matHeaderCellDef fxFlex.gt-lg="7" fxFlex.gt-sm="7" fxLayoutAlign="center center">
            Approval
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="7" fxFlex.gt-sm="7" fxLayoutAlign="center center">
            <mat-slide-toggle (change)="updateLeaveStatus(element?.id, $event)" name="status" [(ngModel)]="element.status">
            </mat-slide-toggle>
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="editleave">
          <mat-header-cell *matHeaderCellDef fxFlex.gt-lg="12" fxFlex.gt-sm="12" fxLayoutAlign="center center"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="12" fxFlex.gt-sm="12" fxLayoutAlign="center center">
            <button mat-icon-button (click)="openSaveLeave(element)" [disabled]="canEdit(element)">
              <mat-icon>edit</mat-icon>
            </button>
            <button mat-icon-button (click)="deleteLeave(element)" *ngIf="isActive">
              <mat-icon>delete</mat-icon>
            </button>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns"></mat-row>
      </table>
      <mat-paginator
        [length]="leavePageable.totalElements"
        [pageSizeOptions]="[5, 20, 25]"
        [pageIndex]="pageable.page"
        [pageSize]="pageable.size"
        (page)="getPagination($event)"
        showFirstLastButtons
      ></mat-paginator>
    </div>
  </div>

</div>
