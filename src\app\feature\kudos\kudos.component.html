<div class="body-content" fxLayout="column" fxFlex="100">
  <div fxFlex fxLayout="row" fxLayoutAlign="space-between" class="mb-1">
    <mat-card-title class="p-10"><PERSON><PERSON></mat-card-title>
    <div>
      <button mat-raised-button class="bt-sfl mr-10px" (click)="giveKudos()">Give Kudos</button>
      <button mat-raised-button class="bt-sfl mr-10px" (click)="myKudos()">My Kudos</button>
      <button mat-raised-button class="bt-sfl" *ngIf="isAdmin" [routerLink]="kudoListURL">Kudos List</button>
    </div>
  </div>

  <hr class="header-divider">

  <form #filter="ngForm" (ngSubmit)="applyFilter()">
    <div fxLayout="row column" fxFlex="100" fxLayoutAlign="start center" class="sfl-card">
      <mat-card-actions class="filter-input ml-1 mr-1">
        <mat-form-field>
          <mat-select placeholder="Select Awarded To" name="awardedTo" [(ngModel)]="filterKudos.employeeProfileId">
            <mat-option *ngFor="let employee of employees" [value]="employee.id">{{ employee?.fullName }}</mat-option>
          </mat-select>
          <button mat-icon-button matSuffix matTooltip="clear"
                  *ngIf="filterKudos.employeeProfileId" (click)="resetFilter('employeeProfileId')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1">
        <mat-form-field>
          <mat-select placeholder="Select Awarded By" name="awardedBy" [(ngModel)]="filterKudos.awardedById">
            <mat-option *ngFor="let employee of employees" [value]="employee.id">{{ employee?.fullName }}</mat-option>
          </mat-select>
          <button mat-icon-button matSuffix matTooltip="clear"
                  *ngIf="filterKudos.awardedById" (click)="resetFilter('awardedById')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1">
        <mat-form-field>
          <mat-select placeholder="Select Kudos Type" name="name" [(ngModel)]="filterKudos.name">
            <mat-option *ngFor="let kudo of kudos" [value]="kudo.name">{{ kudo?.name }}</mat-option>
          </mat-select>
          <button mat-icon-button matSuffix matTooltip="clear"
                  *ngIf="filterKudos.name" (click)="resetFilter('name')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1">
        <mat-form-field>
          <mat-select placeholder="Date Filter" name="filterBy" [(ngModel)]="filterKudos.filterBy">
            <mat-option *ngFor="let dateWise of dateWiseFilter" [value]="dateWise.replace(' ', '')">{{ dateWise }}</mat-option>
          </mat-select>
          <button mat-icon-button matSuffix matTooltip="clear" *ngIf="filterKudos.filterBy" (click)="resetFilter('filterBy')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1" *ngIf="filterKudos.filterBy === uptoYear">
        <mat-form-field>
          <input matInput type="text" placeholder="Year" name="upToYear" aria-label="year" minlength="4" maxlength="4" pattern="\d*"
                 [required]="filterKudos.filterBy === uptoYear" [(ngModel)]="filterKudos.upToYear" #uptoYearInput="ngModel" />
          <button mat-icon-button matSuffix matTooltip="clear" *ngIf="filterKudos.upToYear"
                  (click)="resetFilter('upToYear')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
          <mat-error *ngIf="uptoYearInput.touched && !uptoYearInput.valid">
            <small class="mat-text-warn" *ngIf="uptoYearInput?.errors.required">Year is required.</small>
            <small class="mat-text-warn"
                   *ngIf="uptoYearInput.errors.minlength || uptoYearInput.errors.maxlength || uptoYearInput.errors.pattern">
              Invalid Year.</small>
          </mat-error>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1" *ngIf="filterKudos.filterBy === freeSearch">
        <mat-form-field>
          <input matInput placeholder="From date" name="fromDate" aria-label="from-date" (focus)="fDate.open()" (click)="fDate.open()"
                 [matDatepicker]="fDate" [max]="filterKudos.toDate"
                 [required]="filterKudos.filterBy === freeSearch" [(ngModel)]="filterKudos.fromDate" />
          <mat-datepicker-toggle matSuffix [for]="fDate"></mat-datepicker-toggle>
          <mat-datepicker #fDate></mat-datepicker>
          <button mat-icon-button matSuffix matTooltip="clear" *ngIf="filterKudos.fromDate"
                  (click)="resetFilter('fromDate')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1" *ngIf="filterKudos.filterBy === freeSearch">
        <mat-form-field>
          <input matInput placeholder="To date" name="toDate" aria-label="to-date" (focus)="tDate.open()" (click)="tDate.open()"
                 [matDatepicker]="tDate" [min]="filterKudos.fromDate"
                 [required]="filterKudos.filterBy === freeSearch" [(ngModel)]="filterKudos.toDate" />
          <mat-datepicker-toggle matSuffix [for]="tDate"></mat-datepicker-toggle>
          <mat-datepicker #tDate></mat-datepicker>
          <button mat-icon-button matSuffix matTooltip="clear" *ngIf="filterKudos.toDate"
                  (click)="resetFilter('toDate')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-button-group ml-1">
        <button mat-raised-button class="bt-sfl mr-10px" type="submit"
                [disabled]="filter.form.invalid">Filter</button>
        <button class="bt-flat" type="button" (click)="resetAllFilter()">Reset All</button>
      </mat-card-actions>
    </div>
  </form>

  <div class="sfl-card">
    <div class="p-25" fxLayoutAlign="center center" fxFlex="100" *ngIf="showLoader">
      <mat-progress-spinner color="warn" mode="indeterminate"></mat-progress-spinner>
    </div>

    <div *ngIf="!showLoader && noDataFound" class="w-100 mt-40 no-data-found-text">No Kudos Found.</div>

    <div class="w-100" *ngIf="!showLoader && !noDataFound">
      <table class="w-auto" mat-table matSort matSortDisableClear [dataSource]="dataSource" [matSortActive]="pageable.sort"
             [matSortDirection]="pageable.direction" (matSortChange)="getSorting($event)" >

        <ng-container matColumnDef="image">
          <mat-header-cell *matHeaderCellDef fxFlex.gt-sm="8"> Kudos </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-sm="8">
            <img [src]="element?.awardsDTO?.imageUrl" class="kudos-image" alt="kudos-img" />
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="awards.name">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-sm="11"> Kudos Name </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-sm="11">
            {{ element?.awardsDTO?.name }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="awards.description">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-sm="20"> Description </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-sm="20">
            {{ element?.awardsDTO?.description }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="awardedDate">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-sm="10"> Awarded Date </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-sm="10"> {{ element?.awardedDate | dateToIst | date: dateFormat }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="awardedBy">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-sm="10"> Awarded By </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-sm="10">
            {{ element?.awardedByFirstName + ' ' + element?.awardedByLastName }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="employeeProfileId">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-lg="8" fxFlex.gt-sm="10"> Awarded To
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-sm="10">
            {{ element?.awardedToFirstName + ' ' + element?.awardedToLastName }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="purpose">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-sm="40"> Purpose </mat-header-cell>
          <mat-cell class="sfl-kudos-purpose" *matCellDef="let element" fxFlex.gt-sm="40"> {{ element?.purpose }} </mat-cell>
        </ng-container>

        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns"></mat-row>
      </table>
      <mat-paginator
        [length]="kudosPageable.totalElements"
        [pageSizeOptions]="[10, 20, 25]"
        [pageIndex]="pageable.page"
        [pageSize]="pageable.size"
        (page)="getPagination($event)"
        showFirstLastButtons
      >
      </mat-paginator>
    </div>
  </div>

</div>
