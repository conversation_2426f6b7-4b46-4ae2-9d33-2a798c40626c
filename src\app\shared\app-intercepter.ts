import { Injectable } from '@angular/core';
import { HttpEvent, HttpInterceptor, HttpHandler, HttpRequest, HttpResponse, HttpErrorResponse } from '@angular/common/http';
import {Observable, throwError} from 'rxjs';
import { SharedService, SnackBarService } from './';
import 'rxjs/add/observable/throw';
import 'rxjs/add/operator/catch';
import 'rxjs/add/operator/do';
import { ErrorMessage } from './constants/error.constants';
import {catchError, tap} from 'rxjs/operators';

@Injectable()
export class AppInterceptor implements HttpInterceptor {
  constructor(private sharedService: SharedService, private snakbarService: SnackBarService) {}

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    const authToken = this.sharedService.getUserToken();
    let authRequest;
    if (authToken) {
      authRequest = req.clone({ headers: req.headers.set('Authorization', 'Bearer ' + authToken) });
    } else {
      authRequest = req.clone();
    }

    return next.handle(authRequest)
      .pipe(
        tap((event) => {
          if (event instanceof HttpResponse) {
            this.requestSuccess(event);
          }
          return event;
        }, (error) => {
          return throwError(error);
        }),
        catchError((error: HttpErrorResponse) => {
          if (error.error instanceof Error) {

          } else {
            if (error.status === 401 || error.status === 403) {
              this.sharedService.logout();
              this.snakbarService.error(ErrorMessage.SessionExpired);
              return throwError(error);
            }
            if (error.status === 400 && error.error.title === ErrorMessage.INCORRECT_PASSWORD) {
              return throwError(error);
            }
          }
          this.snakbarService.error(ErrorMessage.InternalServer);
          return throwError(error);
        })
      );
  }

  requestSuccess(res: HttpResponse<any>) {
    return res;
  }
}
