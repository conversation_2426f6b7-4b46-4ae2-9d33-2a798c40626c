import { HttpErrorResponse, HttpEvent, HttpHandler, HttpInterceptor, HttpRequest, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import 'rxjs/add/observable/throw';
import 'rxjs/add/operator/catch';
import 'rxjs/add/operator/do';
import { catchError, filter, switchMap, take, tap } from 'rxjs/operators';
import { SharedService, SnackBarService } from './';
import { ErrorMessage } from './constants/error.constants';
import { TokenManagementService } from './service/token-management.service';

@Injectable()
export class AppInterceptor implements HttpInterceptor {
  private isRefreshing = false;
  private refreshTokenSubject: BehaviorSubject<any> = new BehaviorSubject<any>(null);

  constructor(
    private sharedService: SharedService,
    private snakbarService: SnackBarService,
    private tokenManagementService: TokenManagementService
  ) {}

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    const authToken = this.sharedService.getUserToken();
    let authRequest;
    if (authToken) {
      authRequest = req.clone({ headers: req.headers.set('Authorization', 'Bearer ' + authToken) });
    } else {
      authRequest = req.clone();
    }

    return next.handle(authRequest)
      .pipe(
        tap((event) => {
          if (event instanceof HttpResponse) {
            this.requestSuccess(event);
          }
          return event;
        }, (error) => {
          return throwError(error);
        }),
        catchError((error: HttpErrorResponse) => {
          if (error.error instanceof Error) {
            return throwError(error);
          } else {
            if (error.status === 401) {
              return this.handle401Error(authRequest, next);
            }
            if (error.status === 403) {
              this.sharedService.logout();
              this.snakbarService.error(ErrorMessage.SessionExpired);
              return throwError(error);
            }
            if (error.status === 400 && error.error.title === ErrorMessage.INCORRECT_PASSWORD) {
              return throwError(error);
            }
          }
          this.snakbarService.error(ErrorMessage.InternalServer);
          return throwError(error);
        })
      );
  }

  private handle401Error(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    if (!this.isRefreshing) {
      this.isRefreshing = true;
      this.refreshTokenSubject.next(null);

      return this.tokenManagementService.refreshTokens().pipe(
        switchMap((tokenData: any) => {
          this.isRefreshing = false;
          this.refreshTokenSubject.next(tokenData.access_token);

          // Retry the original request with new token
          const newRequest = request.clone({
            headers: request.headers.set('Authorization', 'Bearer ' + tokenData.access_token)
          });

          return next.handle(newRequest);
        }),
        catchError((error) => {
          this.isRefreshing = false;
          this.sharedService.logout();
          this.snakbarService.error(ErrorMessage.SessionExpired);
          return throwError(error);
        })
      );
    } else {
      // If refresh is in progress, wait for it to complete
      return this.refreshTokenSubject.pipe(
        filter(token => token != null),
        take(1),
        switchMap(token => {
          const newRequest = request.clone({
            headers: request.headers.set('Authorization', 'Bearer ' + token)
          });
          return next.handle(newRequest);
        })
      );
    }
  }

  requestSuccess(res: HttpResponse<any>) {
    return res;
  }
}
