import {<PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t, ViewChild} from '@angular/core';
import { MatPaginator, MatTableDataSource, MatSort } from '@angular/material';
import { Subscription } from 'rxjs';
import { WorklogService } from '../worklog.service';
import { DateUtils, SharedService } from '../../../shared';
import { Variable } from '../../../shared';
import {Employee} from '../../employee-management/employee-management.model';
import {finalize} from 'rxjs/operators';
import {
  MissedWorklog,
  FilterWorklog,
  FormattedMissedWorklog,
  MISSED_WORKLOG_DISPLAY_COLUMNS, MISSED_WORKLOG_FILE_TYPES
} from '../worklog.model';
import {EmployeeManagementService} from '../../employee-management/employee-management.service';


@Component({
  selector: 'sfl-missed-worklog',
  templateUrl: './missed-worklog.component.html'
})
export class MissedWorklogComponent implements <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy {
  @ViewChild(MatSort, {static: false}) set setSort(sort: MatSort) {
    this.dataSource.sort = sort;
  }
  @ViewChild(MatPaginator, {static: false}) set setPaginator(paginator: MatPaginator) {
    this.dataSource.paginator = paginator;
  }

  showLoader = true;
  noDataFound = true;
  selectedFile: string;
  files = MISSED_WORKLOG_FILE_TYPES;
  dateFormat = Variable.MM_DD_YYYY;
  dataSource = new MatTableDataSource<FormattedMissedWorklog>([]);
  displayedColumns = MISSED_WORKLOG_DISPLAY_COLUMNS;
  formattedMissedWorklogs: FormattedMissedWorklog[] = [];
  users: Employee[] = [];
  missedWorklogs: MissedWorklog[] = [];
  dateWiseFilter: string[] = Variable.DATE_WISE_FILTER_LIST;
  freeSearch = Variable.FREE_SEARCH.replace(' ', '');
  uptoYear = Variable.UPTO_YEAR.replace(' ', '');
  subscription: Subscription = new Subscription();
  filterMissedWorklog: FilterWorklog = new FilterWorklog();

  constructor(
    private worklogService: WorklogService,
    private employeeService: EmployeeManagementService,
    private sharedService: SharedService
  ) { }

  ngOnInit() {
    this.applyFilter();
    this.getUsers();
  }

  applyFilter() {
    this.showLoader = true;
    this.noDataFound = true;
    if (this.filterMissedWorklog.filterBy !== this.freeSearch && !this.filterMissedWorklog.filterBy) {
      if (!this.filterMissedWorklog.startDate && !this.filterMissedWorklog.endDate) {
        this.filterMissedWorklog.filterBy = this.freeSearch;
        this.filterMissedWorklog.endDate = new Date().setDate(new Date().getDate() - 1);
        this.filterMissedWorklog.startDate = new Date(new Date().setDate(new Date().getDate() - 31));
      }
    }
    this.filterMissedWorklog.startDate = DateUtils.convertDate(this.filterMissedWorklog.startDate);
    this.filterMissedWorklog.endDate = DateUtils.convertDate(this.filterMissedWorklog.endDate);

    this.subscription.add(this.worklogService.getMissedWorklog(this.filterMissedWorklog)
      .pipe(
        finalize(() => {
          this.noDataFound = this.dataSource.data.length <= 0;
          this.showLoader = false;
        })
      )
      .subscribe((res: MissedWorklog[]) => {
        this.missedWorklogs = res;
        this.formattedMissedWorklogs = [];
        this.missedWorklogs.forEach(missedAllWorklog => {
          const lDate = missedAllWorklog.localDate;
          if (missedAllWorklog.userNotFilledWorklogDTOList) {
            missedAllWorklog.userNotFilledWorklogDTOList.forEach(userNotFilledWorklog => {
              const formattedMissedWorklog = new FormattedMissedWorklog();
              formattedMissedWorklog.localDate = lDate;
              formattedMissedWorklog.name = userNotFilledWorklog.name;
              formattedMissedWorklog.email = userNotFilledWorklog.email;
              this.formattedMissedWorklogs.push(formattedMissedWorklog);
            });
          }
        });
        this.dataSource = new MatTableDataSource<FormattedMissedWorklog>(this.formattedMissedWorklogs);
      })
    );

    this.filterMissedWorklog.startDate = DateUtils.convertStrToDate(this.filterMissedWorklog.startDate);
    this.filterMissedWorklog.endDate = DateUtils.convertStrToDate(this.filterMissedWorklog.endDate);
  }

  resetFilter(parameter: string) {
    this.filterMissedWorklog[parameter] = undefined;
  }

  resetAllFilter() {
    this.filterMissedWorklog = new FilterWorklog();
    this.applyFilter();
  }

  getUsers() {
    this.subscription.add(this.employeeService.getEmployees().subscribe((res: Employee[]) => {
      this.users = res;
    }));
  }

  getReport() {
    this.filterMissedWorklog.startDate = DateUtils.convertDate(this.filterMissedWorklog.startDate);
    this.filterMissedWorklog.endDate = DateUtils.convertDate(this.filterMissedWorklog.endDate);

    this.subscription.add(this.worklogService.getMissedWorklogFile(
      this.filterMissedWorklog, this.selectedFile, this.sharedService.getUserId()
    ).subscribe((data: any) => {
      window.open(data.fileUrl, Variable._BLANK);
    }));

    this.filterMissedWorklog.startDate = DateUtils.convertStrToDate(this.filterMissedWorklog.startDate);
    this.filterMissedWorklog.endDate = DateUtils.convertStrToDate(this.filterMissedWorklog.endDate);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
