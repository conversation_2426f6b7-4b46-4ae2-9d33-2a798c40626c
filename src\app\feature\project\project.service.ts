import { Injectable } from '@angular/core';
import { AppConfig } from '../../app.config';
import { HttpClientService, createRequestOption, PageableQuery } from '../../shared';
import { FilterProject, Project, ProjectDetails, ProjectEstHoursDTO } from './project.model';
import { Subject } from 'rxjs';


@Injectable({
  providedIn: 'root'
})
export class ProjectService {
  invokeEvent: Subject<any> = new Subject();

  constructor(private http: HttpClientService) { }

  callMethodOfWorklogComponent() {
    this.invokeEvent.next();
  }

  getProjects() {
    return this.http.get(AppConfig.GET_ALL_PROJECT_LIST);
  }

  getActiveProjects() {
    return this.http.get(AppConfig.ACTIVE_PROJECTS_ALL);
  }

  updateProjectStatus(project: Project) {
    return this.http.put(AppConfig.UPDATE_PROJECT_STATUS, project);
  }

  createProjectWithClient(project: Project) {
    return this.http.post(AppConfig.CREATE_PROJECT_WITH_CLIENT, project);
  }

  updateProject(project: Project) {
    return this.http.put(AppConfig.UPDATE_PROJECT, project);
  }

  getFilterProject(parameterValue: FilterProject, pageableObject: PageableQuery) {
    return this.http.post(AppConfig.FILTER_PROJECTS, parameterValue, {
      params: createRequestOption(pageableObject)
    });
  }

  getProjectById(id) {
    return this.http.get(AppConfig.GET_PROJECT_BY_ID.concat('/').concat(id));
  }

  getProjectDetailsById(id) {
    return this.http.get(AppConfig.GET_PROJECT_DETAILS_BY_ID.concat('/').concat(id));
  }

  updateProjectDetails(object: ProjectDetails) {
    return this.http.put(AppConfig.PROJECT_DETAILS, object);
  }

  getProjectByTerm(projectTerm: string) {
    return this.http.get(AppConfig.PROJECT_SEARCH_BY_NAME + projectTerm);
  }

  getWorkHoursReportByProjectId(id: number) {
    return this.http.get(AppConfig.GET_PROJECT_WORK_HOURS_BY_ID.concat('/') + id);
  }

  deleteProjectDeveloperById(id: number) {
    return this.http.delete(AppConfig.DELETE_PROJECT_DEVELOPER_BY_ID + id);
  }

  getProjectEstHours(id: number) {
    return this.http.get(AppConfig.PROJECT_EST_HOURS_BY_PROJECT_ID + id);
  }

  saveProjectEstHours(projectEstHoursDTO: ProjectEstHoursDTO) {
    return this.http.post(AppConfig.PROJECT_EST_HOURS, projectEstHoursDTO);
  }

  updateProjectEstHours(projectEstHoursDTO: ProjectEstHoursDTO) {
    return this.http.put(AppConfig.PROJECT_EST_HOURS, projectEstHoursDTO);
  }
}
