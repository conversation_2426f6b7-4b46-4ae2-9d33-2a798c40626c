<div class="account-wrapper">
  <div class="account">
    <mat-card class="sfl-card" fxLayout="column">
      <div fxFlex fxLayout="row" fxLayoutAlign="space-between" class="mb-1">
        <mat-card-title class="p-10">Change Password</mat-card-title>
      </div>
      <form class="p-10" #formElement="ngForm">
        <div fxLayout="column" fxLayoutAlign="space-around">
          <div class="pb-1">
            <mat-form-field>
              <input
                matInput
                type="password"
                placeholder="Old Password"
                name="old_password"
                aria-label="old-password"
                [(ngModel)]="changePassword.oldPassword"
                #oldPassword="ngModel"
                required
              />
              <mat-error *ngIf="oldPassword.touched && oldPassword.invalid">
                <small class="mat-text-warn" *ngIf="oldPassword?.errors.required">Old Password is required.</small>
              </mat-error>
            </mat-form-field>
          </div>
        </div>
        <div fxLayout="column" fxLayoutAlign="space-around">
          <div class="pb-1">
            <mat-form-field>
              <input
                matInput
                type="password"
                placeholder="New Password"
                name="password"
                aria-label="new-password"
                [(ngModel)]="changePassword.newPassword"
                #password="ngModel"
                required
              />
              <mat-error *ngIf="password.touched && password.invalid">
                <small class="mat-text-warn" *ngIf="password?.errors.required">Password is required.</small>
              </mat-error>
            </mat-form-field>
          </div>
        </div>
        <div fxLayout="column" fxLayoutAlign="space-around">
          <div class="pb-1">
            <mat-form-field>
              <input
                matInput
                type="password"
                placeholder="Confirm Password"
                name="confirm_password"
                aria-label="confirm-password"
                [(ngModel)]="changePassword.confirmPassword"
                #confirmPassword="ngModel"
                required
              />
              <mat-error *ngIf="confirmPassword.touched && confirmPassword.invalid">
                <small class="mat-text-warn" *ngIf="confirmPassword?.errors.required">Confirm Password is required.</small>
              </mat-error>
              <mat-error *ngIf="changePassword.newPassword !== changePassword.confirmPassword">
                <small class="mat-text-warn"> Password not matched</small>
              </mat-error>
            </mat-form-field>
          </div>
        </div>
        <div fxLayoutAlign="end">
          <button mat-raised-button class="bt-sfl" type="submit" [disabled]="formElement.form.invalid" (click)="saveNewPassword()">
            Save
          </button>
        </div>
      </form>
    </mat-card>
  </div>
</div>
