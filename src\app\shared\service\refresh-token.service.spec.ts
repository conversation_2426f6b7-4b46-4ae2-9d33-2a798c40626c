import { TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { LocalStorageService } from 'ngx-webstorage';
import { of, throwError } from 'rxjs';
import { RefreshTokenService, TokenData } from './refresh-token.service';
import { HttpClientService } from './httpclient.service';
import { SnackBarService } from './snack-bar.service';

describe('RefreshTokenService', () => {
  let service: RefreshTokenService;
  let mockLocalStorage: jasmine.SpyObj<LocalStorageService>;
  let mockHttpClient: jasmine.SpyObj<HttpClientService>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockSnackBar: jasmine.SpyObj<SnackBarService>;

  const mockTokenData: TokenData = {
    access_token: 'test-access-token',
    refresh_token: 'test-refresh-token',
    expires_in: 86400,
    token_type: 'Bearer'
  };

  beforeEach(() => {
    const localStorageSpy = jasmine.createSpyObj('LocalStorageService', ['store', 'retrieve', 'clear']);
    const httpClientSpy = jasmine.createSpyObj('HttpClientService', ['post']);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const snackBarSpy = jasmine.createSpyObj('SnackBarService', ['error']);

    TestBed.configureTestingModule({
      providers: [
        RefreshTokenService,
        { provide: LocalStorageService, useValue: localStorageSpy },
        { provide: HttpClientService, useValue: httpClientSpy },
        { provide: Router, useValue: routerSpy },
        { provide: SnackBarService, useValue: snackBarSpy }
      ]
    });

    service = TestBed.inject(RefreshTokenService);
    mockLocalStorage = TestBed.inject(LocalStorageService) as jasmine.SpyObj<LocalStorageService>;
    mockHttpClient = TestBed.inject(HttpClientService) as jasmine.SpyObj<HttpClientService>;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    mockSnackBar = TestBed.inject(SnackBarService) as jasmine.SpyObj<SnackBarService>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('setTokenData', () => {
    it('should store token data in localStorage', () => {
      const currentTime = Date.now();
      spyOn(Date, 'now').and.returnValue(currentTime);

      service.setTokenData(mockTokenData);

      expect(mockLocalStorage.store).toHaveBeenCalledWith('token', mockTokenData.access_token);
      expect(mockLocalStorage.store).toHaveBeenCalledWith('refreshToken', mockTokenData.refresh_token);
      expect(mockLocalStorage.store).toHaveBeenCalledWith('expiresIn', mockTokenData.expires_in);
      expect(mockLocalStorage.store).toHaveBeenCalledWith('tokenTimestamp', currentTime);
    });
  });

  describe('hasValidTokens', () => {
    it('should return true for valid tokens', () => {
      const currentTime = Date.now();
      const tokenTimestamp = currentTime - 1000; // 1 second ago

      mockLocalStorage.retrieve.and.callFake((key: string) => {
        switch (key) {
          case 'token': return 'test-token';
          case 'refreshToken': return 'test-refresh-token';
          case 'expiresIn': return 86400;
          case 'tokenTimestamp': return tokenTimestamp;
          default: return null;
        }
      });

      spyOn(Date, 'now').and.returnValue(currentTime);

      expect(service.hasValidTokens()).toBe(true);
    });

    it('should return false for expired tokens', () => {
      const currentTime = Date.now();
      const tokenTimestamp = currentTime - 90000000; // Way in the past

      mockLocalStorage.retrieve.and.callFake((key: string) => {
        switch (key) {
          case 'token': return 'test-token';
          case 'refreshToken': return 'test-refresh-token';
          case 'expiresIn': return 86400;
          case 'tokenTimestamp': return tokenTimestamp;
          default: return null;
        }
      });

      spyOn(Date, 'now').and.returnValue(currentTime);

      expect(service.hasValidTokens()).toBe(false);
    });

    it('should return false when tokens are missing', () => {
      mockLocalStorage.retrieve.and.returnValue(null);

      expect(service.hasValidTokens()).toBe(false);
    });
  });

  describe('isTokenNearExpiry', () => {
    it('should return true when token is near expiry', () => {
      const currentTime = Date.now();
      const tokenTimestamp = currentTime - 86100000; // Near expiry

      mockLocalStorage.retrieve.and.callFake((key: string) => {
        switch (key) {
          case 'expiresIn': return 86400;
          case 'tokenTimestamp': return tokenTimestamp;
          default: return null;
        }
      });

      spyOn(Date, 'now').and.returnValue(currentTime);

      expect(service.isTokenNearExpiry()).toBe(true);
    });
  });

  describe('refreshTokens', () => {
    it('should successfully refresh tokens', (done) => {
      const newTokenData: TokenData = {
        access_token: 'new-access-token',
        refresh_token: 'new-refresh-token',
        expires_in: 86400,
        token_type: 'Bearer'
      };

      mockLocalStorage.retrieve.and.callFake((key: string) => {
        switch (key) {
          case 'token': return 'old-access-token';
          case 'refreshToken': return 'old-refresh-token';
          default: return null;
        }
      });

      mockHttpClient.post.and.returnValue(of(newTokenData));

      service.refreshTokens().subscribe(
        (result) => {
          expect(result).toEqual(newTokenData);
          expect(mockHttpClient.post).toHaveBeenCalled();
          done();
        },
        (error) => {
          fail('Should not have failed');
          done();
        }
      );
    });

    it('should handle refresh token failure', (done) => {
      mockLocalStorage.retrieve.and.callFake((key: string) => {
        switch (key) {
          case 'token': return 'old-access-token';
          case 'refreshToken': return 'old-refresh-token';
          default: return null;
        }
      });

      mockHttpClient.post.and.returnValue(throwError('Refresh failed'));
      mockRouter.navigate.and.returnValue(Promise.resolve(true));

      service.refreshTokens().subscribe(
        (result) => {
          fail('Should have failed');
          done();
        },
        (error) => {
          expect(error).toBe('Refresh failed');
          expect(mockLocalStorage.clear).toHaveBeenCalled();
          done();
        }
      );
    });
  });

  describe('clearAllTokens', () => {
    it('should clear all token-related data', () => {
      service.clearAllTokens();

      expect(mockLocalStorage.clear).toHaveBeenCalledWith('token');
      expect(mockLocalStorage.clear).toHaveBeenCalledWith('refreshToken');
      expect(mockLocalStorage.clear).toHaveBeenCalledWith('expiresIn');
      expect(mockLocalStorage.clear).toHaveBeenCalledWith('tokenTimestamp');
    });
  });
});
