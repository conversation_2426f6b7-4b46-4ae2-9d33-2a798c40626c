import { Injectable } from '@angular/core';
import { AppConfig } from '../../app.config';
import { HttpClientService, createRequestOption, PageableQuery } from '../../shared';
import { FilterAttendance, FilterWorklog } from '../worklog/worklog.model';
import { ClockInOutDTO } from './dashboard.model';


@Injectable()
export class DashboardService {

  constructor(private http: HttpClientService) { }

  getMissingTimeSheets(userId: number, pageableObject: PageableQuery) {
    return this.http.get(AppConfig.GET_MISSING_TIME_SHEETS + userId, {
      params: createRequestOption(pageableObject)
    });
  }

  getMyCurrentProjects(userId: number, pageableObject: PageableQuery) {
    return this.http.get(AppConfig.GET_MY_CURRENT_PROJECTS + userId, {
      params: createRequestOption(pageableObject)
    });
  }

  getHolidays() {
    return this.http.get(AppConfig.HOLIDAYS);
  }

  getLeaveCountByUaaUser(userId: number) {
    return this.http.get(AppConfig.LEAVE_COUNTS + userId);
  }

  getTotalLeavesCountByUaaUser(userId: number){
    return this.http.get(AppConfig.LEAVES_TOTAL_COUNTS + userId);
  }

  empClockInOut(empAttendance: ClockInOutDTO) {
    return this.http.post(AppConfig.CLOCK_IN_OUT_EMP, empAttendance);
  }

  getActiveClockInUser(userId: number) {
    return this.http.get(AppConfig.ACTIVE_USER + userId);
  }

  empClockInOutPageable(empAttendance: FilterAttendance, pageableObject) {
    return this.http.post(AppConfig.CLOCK_IN_OUT_PAGEABLE, empAttendance, {
      params: createRequestOption(pageableObject)
    });
  }


  // getNotifications(userID: number) {
  //   return this.http.get(AppConfig.FORM_AVAILABLE + userID);
  // }

}
