import { Component, OnInit, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material';
import { ProfileComponent } from '../../../profile/profile.component';
import {GivenKudos} from '../../kudos.model';
import {Variable} from '../../../../shared';


@Component({
  selector: 'sfl-kudos-detail',
  templateUrl: './given-kudos-detail.component.html'
})
export class GivenKudosDetailComponent implements OnInit {
  dateFormat = Variable.MM_DD_YYYY;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: GivenKudos,
    public dialogRef: MatDialogRef<ProfileComponent>
  ) { }

  ngOnInit() { }

  closeDialog(): void {
    this.dialogRef.close();
  }

}
