<div class="body-content" fxLayout="column" fxFlex="100">
  <div fxFlex fxLayout="row" fxLayoutAlign="space-between" class="mb-1">
    <mat-card-title class="p-10">List Of Kudos</mat-card-title>
    <div>
      <button mat-raised-button class="bt-sfl mr-10px" (click)="saveKudos(null)">Add Kudos</button>
    </div>
  </div>

  <hr class="header-divider">

  <div class="sfl-card">
    <div class="p-25" fxLayoutAlign="center center" fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100" *ngIf="showLoader">
      <mat-progress-spinner color="warn" mode="indeterminate"></mat-progress-spinner>
    </div>

    <div *ngIf="!showLoader && noDataFound" class="w-100 mt-40 no-data-found-text">No Kudos Found.</div>

    <div class="w-100" *ngIf="!showLoader && !noDataFound">
      <table class="w-auto" mat-table matSort matSortDisableClear matSortActive="name" matSortDirection="asc" [dataSource]="dataSource">

        <ng-container matColumnDef="image">
          <mat-header-cell *matHeaderCellDef> Kudos Image </mat-header-cell>
          <mat-cell *matCellDef="let element">
            <img [src]="element?.imageUrl" class="kudos-image" alt="kudos-img" />
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="name">
          <mat-header-cell *matHeaderCellDef mat-sort-header> Kudos Name </mat-header-cell>
          <mat-cell *matCellDef="let element">
            {{ element?.name }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="description">
          <mat-header-cell *matHeaderCellDef mat-sort-header> Description
          </mat-header-cell>
          <mat-cell *matCellDef="let element">
            {{ element?.description }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxLayoutAlign="center center"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxLayoutAlign="center center">
            <button mat-icon-button (click)="saveKudos(element)"><mat-icon>edit</mat-icon></button>
            <button mat-icon-button (click)="deleteKudos(element)"><mat-icon>delete</mat-icon></button>
          </mat-cell>
        </ng-container>

        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns"></mat-row>
      </table>
      <mat-paginator [pageSize]="10" [pageSizeOptions]="[10, 20, 25]" showFirstLastButtons></mat-paginator>
    </div>
  </div>

</div>
