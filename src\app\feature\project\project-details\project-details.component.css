.card-title {
  padding-left: 1em;
}

.card-title .title {
  font-size: 18px;
  font-weight: bold;
  margin-right: 10px;
}

.ui-card {
  background-color: #fff;
  padding: 25px;
  transition: 0.2s linear all;
  border-radius: 2px;
  margin: 5px;
  position: relative;
}

.ui-card.bordered {
  border: 2px dotted #444;
  text-align: center;
  cursor: pointer;
  font-weight: bolder;
}

.ui-card.bordered:hover {
  color: #fff;
}

.ui-card.bordered mat-icon {
  font-weight: bolder;
}

.ui-card.bordered .text {
  display: block;
}

.ui-card.colored {
  background-image: linear-gradient(to right bottom, #ff8146, #ff8850, #ff8f5a, #ff9563, #ff9c6d);
  color: #fff;
  padding: 30px;
}

.ui-card.colored .designation {
  color: #fff;
}

.ui-card:hover {
  background-image: linear-gradient(to right bottom, #ff8146, #ff8850, #ff8f5a, #ff9563, #ff9c6d);
  transition: 0.2s linear all;
  box-shadow: 5px 5px 15px #dadada;
}

.ui-card:hover .name,
.ui-card:hover .designation {
  color: #fff;
}

.ui-card .name {
  font-size: 18px;
  font-weight: bold;
  display: block;
}

.ui-card .designation {
  font-size: 12px;
  color: #666;
  font-style: italic;
}

.ui-card .designation:before {
  content: '—';
  margin-right: 5px;
}

.card-contents {
  padding: 0 10px;
}

.file {
  background-color: #fff;
  box-shadow: 2px 2px 5px #dadada;
  border-radius: 10px;
  margin: 5px;
  position: relative;
}

.file:hover {
  transition: 0.2s linear all;
  box-shadow: 5px 5px 15px #dadada;
}

.file .icon {
  text-align: center;
  padding: 7px 0;
  background-image: linear-gradient(to right bottom, #ff8146, #ff8850, #ff8f5a, #ff9563, #ff9c6d);
  color: #fff;
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
}

.file .delete-file {
  position: absolute;
  top: 0;
  right: 5px;
}

.file .details {
  padding: 10px;
}

.file .details .file-name {
  font-weight: bold;
  color: #444;
  display: block;
}

.edit-member-button {
  position: absolute;
  right: 0;
  top: 0;
}

h3.no-members {
  text-align: center;
  opacity: 0.5;
}
