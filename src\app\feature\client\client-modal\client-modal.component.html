<h2 mat-dialog-title>{{ isEdit ? 'Update' : 'Add New' }} Client</h2>
<hr class="mb-1">

<mat-dialog-content>
  <form #clientForm="ngForm">
    <mat-card-content>

      <mat-form-field>
        <input matInput type="text" placeholder="Client Name" name="clientName" aria-label="client-name"
               [(ngModel)]="client.clientName" #clientName="ngModel" required>
        <mat-error *ngIf="clientName.touched && clientName.invalid">
          <small class="mat-text-warn" *ngIf="clientName?.errors.required">Client name is required.</small>
        </mat-error>
      </mat-form-field>

      <div fxLayout="row" class="mb-1">
        <div fxFlex>
          <mat-slide-toggle name="isGenerateClientLogin" [(ngModel)]="client.generateClientLogin" color="warn" [disabled]="client.generateClientLogin">
            Generate Client login
          </mat-slide-toggle>
        </div>
      </div>

      <div *ngIf="client.generateClientLogin">
        <mat-form-field>
          <input matInput type="text" placeholder="First Name" name="firstName" aria-label="first-name"
                 [(ngModel)]="client.firstName" #firstName="ngModel" required>
          <mat-error *ngIf="firstName.touched && firstName.invalid">
            <small class="mat-text-warn" *ngIf="firstName?.errors.required">First name is required.</small>
          </mat-error>
        </mat-form-field>

        <mat-form-field>
          <input matInput type="text" placeholder="Last Name" name="lastName" aria-label="last-name"
                 [(ngModel)]="client.lastName" #lastName="ngModel" required>
          <mat-error *ngIf="lastName.touched && lastName.invalid">
            <small class="mat-text-warn" *ngIf="lastName?.errors.required">Last name is required.</small>
          </mat-error>
        </mat-form-field>

        <mat-form-field>
          <input matInput placeholder="Username" name="username" aria-label="username"
                 [(ngModel)]="client.login" #login="ngModel" required [disabled]="isEdit" pattern="^[0-9a-zA-Z]{6,}$">
          <mat-error *ngIf="login.touched && login.invalid">
            <small class="mat-text-warn" *ngIf="login?.errors.required">Username is required.</small>
            <small class="mat-text-warn" *ngIf="login?.errors.pattern">Username must be minimum 6 characters long and only contain
              alphabets and numbers.</small>
          </mat-error>
        </mat-form-field>

        <mat-form-field>
          <input matInput type="email" placeholder="Email" name="email" aria-label="email"
                 pattern="^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$" [(ngModel)]="client.email"
                 #email="ngModel" required [disabled]="isEdit">
          <mat-error *ngIf="email.touched && email.invalid">
            <small class="mat-text-warn" *ngIf="email?.errors.required">Email is required.</small>
            <small class="mat-text-warn" *ngIf="email?.errors.pattern">Invalid Email.</small>
          </mat-error>
        </mat-form-field>

        <mat-form-field>
          <input matInput type="password" placeholder="Password" name="password" aria-label="password" pattern="^[0-9a-zA-Z]{6,}$"
                 [(ngModel)]="client.password" #password="ngModel" [required]="!isEdit">
          <mat-error *ngIf="password.touched && password.invalid">
            <small class="mat-text-warn" *ngIf="password?.errors.required">Password is required.</small>
            <small class="mat-text-warn" *ngIf="password?.errors.pattern">Password must be minimum 6 characters long and only
              contain alphabets and numbers.</small>
          </mat-error>
        </mat-form-field>

        <mat-form-field>
          <input matInput type="password" placeholder="Confirm Password" name="cpassword" aria-label="c-password"
                 [(ngModel)]="client.cpassword" #cPassword="ngModel" [required]="!!client.password" pattern="{{ client.password }}">
          <mat-error *ngIf="cPassword.touched && cPassword.invalid">
            <small class="mat-text-warn" *ngIf="cPassword?.errors.required">Confirm password is required.</small>
            <small class="mat-text-warn" *ngIf="cPassword?.errors.pattern">Password and Confirm Password did not
              matched.</small>
          </mat-error>
        </mat-form-field>

        <mat-form-field>
          <input matInput placeholder="Phone Number" name="phoneNumber" aria-label="phone-number" pattern="^\+?\d{10,13}"
                 [(ngModel)]="client.phoneNumber" #phoneNumber="ngModel" required>
          <mat-error *ngIf="phoneNumber.touched && phoneNumber.invalid">
            <small class="mat-text-warn" *ngIf="phoneNumber?.errors.required">Phone Number is required.</small>
            <small class="mat-text-warn" *ngIf="phoneNumber?.errors.pattern">Invalid Phone Number.</small>
          </mat-error>
        </mat-form-field>
      </div>

    </mat-card-content>
  </form>
</mat-dialog-content>
<hr>

<mat-dialog-actions fxLayoutAlign="end">
  <button class="bt-flat" type="submit" (click)="closeDialog()">Cancel</button>
  <button mat-raised-button class="bt-sfl" type="submit" (click)="saveClient()"
    [style.cursor]="clientForm.form.invalid ? 'not-allowed' : 'pointer'" [disabled]="clientForm.form.invalid">
    Save
  </button>
</mat-dialog-actions>
