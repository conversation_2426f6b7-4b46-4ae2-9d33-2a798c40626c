import { Component } from '@angular/core';
import { Subscription } from 'rxjs';
import { ForgotPasswordService } from './forgot-password.service';
import { Router } from '@angular/router';
import { SnackBarService } from 'src/app/shared';
import { Variable } from '../../shared';
import { AppConfig } from '../../app.config';


@Component({
  selector: 'sfl-forgot-password',
  templateUrl: './forgot-password.component.html'
})
export class ForgotPasswordComponent {
  email: string;
  subscription: Subscription = new Subscription();

  constructor(
    public forgotPasswordService: ForgotPasswordService,
    public router: Router,
    private snackBarService: SnackBarService
  ) { }

  forgotPassword() {
    this.subscription.add(this.forgotPasswordService.forgotPassword(this.email).subscribe(() => {
      this.snackBarService.success(Variable.EMAIL_SENT);
      this.router.navigate([AppConfig.LOGIN]).then();
    }, () => {
      this.snackBarService.error(Variable.EMAIL_NOT_SENT);
    }));
  }

}
