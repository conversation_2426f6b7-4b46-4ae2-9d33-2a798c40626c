import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild} from '@angular/core';
import {MatPaginator} from '@angular/material/paginator';
import {MatSort} from '@angular/material/sort';
import {SharedService, Variable} from '../../../shared';
import {MatTableDataSource} from '@angular/material/table';
import {GenerateFormResponse, FORM_RESPONSE_COLUMNS} from '../sfl-forms.model';
import {Subscription} from 'rxjs';
import {SflFormsService} from '../sfl-forms.service';
import {ActivatedRoute} from '@angular/router';
import {MatDialog} from '@angular/material/dialog';
import {FormResponseModalComponent} from './form-response-modal/form-response-modal.component';
import {finalize} from 'rxjs/operators';


@Component({
  selector: 'sfl-form-response',
  templateUrl: './form-response.component.html'
})
export class FormResponseComponent implements <PERSON><PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy {
  @ViewChild(MatPaginator, {static: false})
  set paginator(value: MatPaginator) {
    this.dataSource.paginator = value;
  }
  @ViewChild(MatSort, {static: false})
  set sort(value: MatSort) {
    this.dataSource.sort = value;
  }

  showLoader = true;
  noDataFound = true;
  formID: string;
  formName: string;
  displayedColumns = FORM_RESPONSE_COLUMNS;
  dataSource = new MatTableDataSource<GenerateFormResponse>([]);
  subscription: Subscription = new Subscription();

  constructor(
    private matDialog: MatDialog,
    private sharedService: SharedService,
    private sflFormsService: SflFormsService,
    private activatedRoute: ActivatedRoute
  ) {
    this.subscription.add(this.activatedRoute.paramMap.subscribe((params) => {
      this.formID = params.get(Variable.ID);
      this.formName = params.get(Variable.NAME);
    }));
  }

  ngOnInit() {
    this.getFormResponses();
  }

  getFormResponses() {
    this.showLoader = true;
    this.noDataFound = true;
    this.subscription.add(this.sflFormsService.getFormsResponse(this.formID)
      .pipe(
        finalize(() => {
          this.noDataFound = this.dataSource.data.length <= 0;
          this.showLoader = false;
        })
      )
      .subscribe((res: GenerateFormResponse[]) => {
        this.dataSource = new MatTableDataSource<GenerateFormResponse>(res);
      })
    );
  }

  responseDetails(generateFormResponse: GenerateFormResponse) {
    this.matDialog.open(FormResponseModalComponent, {
      data: generateFormResponse
    });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
