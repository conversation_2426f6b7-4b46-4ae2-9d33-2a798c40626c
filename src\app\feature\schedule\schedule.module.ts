import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from "@angular/core";
import { RouterModule } from "@angular/router";
import { NgxTimeSchedulerModule } from "src/app/shared/modules/ngx-time-scheduler/ngx-time-scheduler.module";
import { SharedModule } from "../../shared/shared.module";
import { ClientService } from "../client/client.service";
import { EmployeeManagementService } from "../employee-management/employee-management.service";
import { ProjectService } from "../project/project.service";
import { TechnologyService } from "../technology/technology.service";
import { ScheduleModalComponent } from "./schedule-modal/schedule-modal.component";
import { ScheduleComponent } from "./schedule.component";
import { scheduleRoutes } from "./schedule.route";
import { ScheduleService } from "./schedule.service";

@NgModule({
  imports: [RouterModule.forChild(scheduleRoutes), SharedModule, NgxTimeSchedulerModule],
  declarations: [ScheduleComponent, ScheduleModalComponent],
  entryComponents: [ScheduleModalComponent],
  providers: [ScheduleService, ProjectService, TechnologyService, EmployeeManagementService, ClientService],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class ScheduleModule {}
