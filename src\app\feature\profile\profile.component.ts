import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from "@angular/core";
import { Mat<PERSON><PERSON>og, MatPaginator, MatSort, MatTableDataSource } from "@angular/material";
import { ActivatedRoute, Router } from "@angular/router";
import { Subscription } from "rxjs";
import { finalize } from "rxjs/operators";
import { AppConfig } from "../../app.config";
import { PageableQuery, SharedService, Variable } from "../../shared";
import { GivenKudos, GivenKudosPageable } from "../kudos/kudos.model";
import { EditProfileComponent } from "./edit-profile/edit-profile.component";
import { BasicProfile, DEFAULT_PROFILE_IMAGE, EmployeeIdParam, KUDOS_USER_ID_DISPLAY_COLUMNS, ProfilePicture } from "./profile.model";
import { ProfileService } from "./profile.service";

@Component({
  selector: "sfl-employee-profile",
  templateUrl: "./profile.component.html",
})
export class ProfileComponent implements OnInit, OnD<PERSON>roy {
  @ViewChild(MatSort, { static: false }) sort: MatSort;
  @ViewChild(MatPaginator, { static: false }) paginator: MatPaginator;

  basicProfile: BasicProfile = new BasicProfile();
  profilePicture: ProfilePicture = new ProfilePicture();
  subscription: Subscription = new Subscription();
  pageable: PageableQuery = new PageableQuery();
  kudosPageable: GivenKudosPageable = new GivenKudosPageable();
  dataSource = new MatTableDataSource<GivenKudos>([]);
  imageUrl = DEFAULT_PROFILE_IMAGE;
  displayedColumns = KUDOS_USER_ID_DISPLAY_COLUMNS;
  dateFormat = Variable.MM_DD_YYYY;
  freeSearch = Variable.FREE_SEARCH.replace(" ", "");
  userId: number;
  isCurrentUser = false;
  imgLoader = false;
  showLoader = true;
  noDataFound = true;

  constructor(private matDialog: MatDialog, private profileService: ProfileService, private sharedService: SharedService, private route: ActivatedRoute, private router: Router) {
    this.pageable.page = 0;
    this.pageable.size = 10;
    this.pageable.sort = Variable.AWARDED_DATE;
    this.pageable.direction = Variable.DESC;
  }

  ngOnInit() {
    this.onLoad();
    this.subscription.add(
      this.profileService.invokeEvent.subscribe(() => {
        this.onLoad();
      })
    );
  }

  getSorting(event) {
    this.pageable.sort = event.active;
    this.pageable.direction = event.direction;
    this.pageable.page = 0;
    this.getKudosByUserId();
  }

  getPagination(event) {
    this.pageable.size = event.pageSize;
    this.pageable.page = event.pageIndex;
    this.getKudosByUserId();
  }

  onLoad() {
    this.route.paramMap.subscribe((params) => {
      this.userId = Number(params.get(Variable.ID));
      if (this.userId === 0) {
        this.userId = this.sharedService.getUaaUserId();
      }

      this.isCurrentUser = this.userId === this.sharedService.getUaaUserId();
      this.getProfileDetail();
      this.getKudosByUserId();
    });
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

  getProfileDetail() {
    this.subscription.add(
      this.profileService.getUserDetails(this.userId).subscribe((res: BasicProfile) => {
        this.basicProfile = res;
        this.getProfileImage();
      })
    );
  }

  openEditProfile() {
    if (this.isCurrentUser) {
      this.basicProfile.birthDate = new Date(this.basicProfile.birthDate);
      this.basicProfile.joinDate = new Date(this.basicProfile.joinDate);
      const dialogRef = this.matDialog.open(EditProfileComponent, {
        width: Variable.BOX_WIDTH_VALUE,
        data: this.basicProfile,
      });

      dialogRef.afterClosed().subscribe(() => {
        this.getProfileDetail();
      });
    }
  }

  getProfileImage() {
    if (this.basicProfile) {
      this.imageUrl = this.basicProfile.imageUrl ? this.basicProfile.imageUrl : DEFAULT_PROFILE_IMAGE;
    }
  }

  setProfilePicture(files: FileList) {
    if (files.length > 0 && this.isCurrentUser) {
      this.imgLoader = true;
      this.profilePicture.id = this.sharedService.getUaaUserId();
      this.profilePicture.file = files.item(0);

      this.subscription.add(
        this.profileService.updateProfilePicture(this.profilePicture).subscribe((res: BasicProfile) => {
          this.basicProfile = res;
          this.imgLoader = false;
          this.getProfileImage();
        })
      );
    }
  }

  getKudosByUserId() {
    const employeeIdParam = new EmployeeIdParam();
    employeeIdParam.employeeProfileId = this.userId;

    const currentSort = this.pageable.sort;
    this.pageable.sort = this.pageable.sort + "," + this.pageable.direction;
    this.subscription.add(
      this.profileService
        .getKudosByUserId(employeeIdParam, this.pageable)
        .pipe(
          finalize(() => {
            this.noDataFound = this.dataSource.data.length <= 0;
            this.showLoader = false;
          })
        )
        .subscribe((res: GivenKudosPageable) => {
          this.kudosPageable = res;
          this.dataSource = new MatTableDataSource<GivenKudos>(res.content);
          this.pageable.size = res.pageable.pageSize;
          this.pageable.page = res.pageable.pageNumber;
          this.showLoader = false;
        })
    );
    this.pageable.sort = currentSort;
  }

  openGiveKudos() {
    if (!this.isCurrentUser) {
      const queryParams = {
        queryParams: {
          userId: this.userId,
          giveKudos: true,
        },
      };
      this.router.navigate([AppConfig._KUDOS], queryParams).then();
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
