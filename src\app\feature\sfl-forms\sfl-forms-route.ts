import { Routes } from '@angular/router';
import {SflFormsComponent} from './sfl-forms.component';
import {CreateFormComponent} from './create-form/create-form.component';
import {GeneratorModalComponent} from './generator-modal/generator-modal.component';
import {FillFormComponent} from './fill-form/fill-form.component';
import {FormResponseComponent} from './form-response/form-response.component';
import {FormResponseModalComponent} from './form-response/form-response-modal/form-response-modal.component';
import {PreviewModalComponent} from './preview-modal/preview-modal.component';


export const sflFormsRoutes: Routes = [
  {
    path: '',
    component: SflFormsComponent
  },
  {
    path: 'create',
    component: CreateFormComponent
  },
  {
    path: 'create/:id',
    component: CreateFormComponent
  },
  {
    path: 'generator',
    component: GeneratorModalComponent
  },
  {
    path: 'fill/:createdFormId/:generatedFormId',
    component: FillFormComponent
  },
  {
    path: 'response/:id/:name',
    component: FormResponseComponent
  },
  {
    path: 'response-modal',
    component: FormResponseModalComponent
  },
  {
    path: 'preview-modal',
    component: PreviewModalComponent
  }
];
