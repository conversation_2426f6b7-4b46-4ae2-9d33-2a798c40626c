<h2 mat-dialog-title>{{ title }}</h2>
<hr class="mb-1" />

<mat-dialog-content>
  <div class="p-25" fxLayoutAlign="center center" fxFlex="100" *ngIf="!dataLoaded">
    <mat-progress-spinner color="warn" mode="indeterminate"></mat-progress-spinner>
  </div>
  <form #jobCodeForm="ngForm">
    <ng-container *ngIf="dataLoaded">
      <mat-card-content>
        <mat-form-field>
          <mat-select tabindex="1" [disabled]="isViewMode" placeholder="Select Project" name="projectId" (ngModelChange)="setJobCode()" [(ngModel)]="jobCodeData.projectId" #project="ngModel" required>
            <mat-option *ngFor="let project of projects" [value]="project?.id">{{ project?.name }}</mat-option>
          </mat-select>
          <mat-error *ngIf="project.touched && project.invalid">
            <small class="mat-text-warn" *ngIf="project?.errors.required">Project is required.</small>
          </mat-error>
        </mat-form-field>

        <mat-form-field>
          <mat-select tabindex="2" placeholder="Select Client" name="clientName" #clientName="ngModel" [disabled]="isViewMode" (ngModelChange)="setJobCode()" [(ngModel)]="jobCodeData.clientId">
            <mat-option *ngFor="let client of clients" [value]="client?.id">{{ client?.clientName }}</mat-option>
          </mat-select>
          <mat-error *ngIf="clientName.touched && clientName.invalid">
            <small class="mat-text-warn" *ngIf="project?.errors.required">Client name is required.</small>
          </mat-error>
        </mat-form-field>

        <mat-form-field>
          <mat-select
            tabindex="3"
            [disabled]="isViewMode"
            placeholder="Select Technology"
            name="technology"
            (ngModelChange)="setJobCode()"
            [(ngModel)]="jobCodeData.technologyId"
            #technology="ngModel"
          >
            <mat-option *ngFor="let technology of technologies" [value]="technology?.id">{{ technology?.name }}</mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field>
          <input tabindex="4" [disabled]="isViewMode" matInput type="text" placeholder="Tags" name="tags" aria-label="tags" [(ngModel)]="jobCodeData.tagName" #tags="ngModel" />
        </mat-form-field>

        <mat-form-field>
          <textarea
            tabindex="5"
            [disabled]="isViewMode"
            matInput
            placeholder="Description"
            name="description"
            aria-label="description"
            maxlength="255"
            minlength="1"
            [(ngModel)]="jobCodeData.description"
            (change)="jobCodeData.description = jobCodeData.description.trim()"
            #description="ngModel"
          ></textarea>
          <mat-error *ngIf="description.touched && description.invalid">
            <small class="mat-text-warn" *ngIf="description?.errors.required">Description is required.</small>
          </mat-error>
        </mat-form-field>
        <mat-radio-group tabindex="6" aria-label="Select an option" class="d-inline-block" [disabled]="isViewMode" name="isBillable" #isBillable="ngModel" [(ngModel)]="jobCodeData.isBillable">
          <mat-radio-button color="warn" [value]="true">Billable</mat-radio-button>
          <mat-radio-button class="mr-10px" color="warn" [value]="false">Non Billable</mat-radio-button>
        </mat-radio-group>
        <mat-form-field style="margin-top: 15px">
          <input tabindex="7" matInput type="text" placeholder="Job Code" name="jobCode" aria-label="job-code" [(ngModel)]="jobCodeData.name" #jobCode="ngModel" [disabled]="isViewMode" />
        </mat-form-field>
      </mat-card-content>
    </ng-container>
  </form>
</mat-dialog-content>
<hr />
<mat-dialog-actions fxLayoutAlign="end">
  <button tabindex="9" class="bt-flat" type="submit" (click)="closeDialog()">Cancel</button>
  <button
    tabindex="8"
    mat-raised-button
    class="bt-sfl"
    type="submit"
    (click)="saveJobCode()"
    [style.cursor]="jobCodeForm.form.invalid ? 'not-allowed' : 'pointer'"
    [disabled]="!dataLoaded || jobCodeForm.form.invalid"
  >
    {{ isViewMode ? "Edit" : "Save" }} <mat-progress-spinner *ngIf="isSaving" [diameter]="20" [strokeWidth]="3" mode="indeterminate" style="display: inline-block"></mat-progress-spinner>
  </button>
</mat-dialog-actions>
