import {Component, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild} from '@angular/core';
import {MatDialog, MatPaginator, MatSort, MatTableDataSource} from '@angular/material';
import {ROLES, Variable} from 'src/app/shared/constants/Variable.constants';
import {GivenKudos, FilterKudos, GivenKudosPageable, Kudos, KUDOS_DISPLAY_COLUMNS} from './kudos.model';
import { Subscription } from 'rxjs';
import { Employee } from 'src/app/feature/employee-management/employee-management.model';
import { KudosService } from './kudos.service';
import {DateUtils, PageableQuery, SharedService} from 'src/app/shared';
import {GiveKudosComponent} from './kudos-modals/give-kudos/give-kudos.component';
import {ActivatedRoute, Router} from '@angular/router';
import {AppConfig} from '../../app.config';
import {finalize} from 'rxjs/operators';
import {EmployeeManagementService} from '../employee-management/employee-management.service';


@Component({
  selector: 'sfl-kudos-list',
  templateUrl: './kudos.component.html',
})
export class KudosComponent implements OnInit, OnDestroy {
  @ViewChild(MatSort, {static: false}) sort: MatSort;
  @ViewChild(MatPaginator, {static: false}) paginator: MatPaginator;

  showLoader = true;
  noDataFound = true;
  isAdmin = false;
  searchedUserId: number;
  employees: Employee[] = [];
  kudos: Kudos[] = [];
  kudoListURL = AppConfig._KUDOS_LIST;
  displayedColumns = KUDOS_DISPLAY_COLUMNS;
  dateFormat = Variable.MM_DD_YYYY;
  dateWiseFilter: string[] = Variable.DATE_WISE_FILTER_LIST;
  pageable: PageableQuery = new PageableQuery();
  filterKudos: FilterKudos = new FilterKudos();
  dataSource = new MatTableDataSource<GivenKudos>([]);
  subscription: Subscription = new Subscription();
  kudosPageable: GivenKudosPageable = new GivenKudosPageable();
  freeSearch = Variable.FREE_SEARCH.replace(' ', '');
  uptoYear = Variable.UPTO_YEAR.replace(' ', '');

  constructor(
    private matDialog: MatDialog,
    private kudosListService: KudosService,
    private employeeService: EmployeeManagementService,
    private sharedService: SharedService,
    private route: ActivatedRoute,
    private router: Router,
    private kudosService: KudosService
  ) {
    this.pageable.page = 0;
    this.pageable.size = 10;
    this.pageable.sort = Variable.AWARDED_DATE;
    this.pageable.direction = Variable.DESC;
  }

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      if (params && params.giveKudos) {
        setTimeout(() => {
          this.searchedUserId = Number(params.userId);
          this.giveKudos();
        }, 0);
      }
    });
    const currentUserRole = this.sharedService.getRole();
    if (currentUserRole.includes(ROLES.SUPER_ADMIN) || currentUserRole.includes(ROLES.ADMIN)) {
      this.isAdmin = true;
    }
    this.getUsers();
    this.getKudos();
    this.applyFilter();
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

  getSorting(event) {
    this.pageable.sort = event.active;
    this.pageable.direction = event.direction;
    this.pageable.page = 0;
    this.applyFilter(false);
  }

  getPagination(event) {
    this.pageable.size = event.pageSize;
    this.pageable.page = event.pageIndex;
    this.applyFilter(false);
  }

  applyFilter(set_page_zero = true) {
    this.pageable.page = set_page_zero ? 0 : this.pageable.page;
    this.showLoader = true;
    this.noDataFound = true;

    const currentSort = this.pageable.sort;
    this.pageable.sort = this.pageable.sort + ',' + this.pageable.direction;

    this.filterKudos.fromDate = DateUtils.convertDate(this.filterKudos.fromDate);
    this.filterKudos.toDate = DateUtils.convertDate(this.filterKudos.toDate);
    this.subscription.add(
      this.kudosListService.getFilterKudos(this.filterKudos, this.pageable)
        .pipe(
          finalize(() => {
            this.noDataFound = this.dataSource.data.length <= 0;
            this.showLoader = false;
          })
        )
        .subscribe((res: GivenKudosPageable) => {
          this.kudosPageable = res;
          this.dataSource = new MatTableDataSource<GivenKudos>(res.content);
          this.pageable.size = res.pageable.pageSize;
          this.pageable.page = res.pageable.pageNumber;
      })
    );
    this.pageable.sort = currentSort;

    this.filterKudos.fromDate = DateUtils.convertStrToDate(this.filterKudos.fromDate);
    this.filterKudos.toDate = DateUtils.convertStrToDate(this.filterKudos.toDate);
  }

  getUsers() {
    this.subscription.add(
      this.employeeService.getEmployees().subscribe((res: Employee[]) => {
        this.employees = res;
      })
    );
  }

  getKudos() {
    this.subscription.add(this.kudosService.getKudos().subscribe((res: Kudos[]) => {
      this.kudos = res;
    }));
  }

  resetAllFilter() {
    this.filterKudos = new FilterKudos();
    this.applyFilter();
  }

  resetFilter(parameter: string) {
    this.filterKudos[parameter] = undefined;
  }

  giveKudos() {
    const dialogRef = this.matDialog.open(GiveKudosComponent, {
      width: Variable.BOX_WIDTH_VALUE,
      data: {
        userID: this.searchedUserId
      }
    });

    dialogRef.afterClosed().subscribe(() => {
      this.applyFilter();
    });
  }

  myKudos() {
    this.filterKudos.employeeProfileId = this.sharedService.getUaaUserId();
    this.applyFilter();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
