<div class="body-content" fxFlex="100">
  <div fxFlex fxLayout="row" fxLayoutAlign="space-between" class="mb-1">
    <mat-card-title class="p-10">Fill the forms - {{sflForm?.formName}}</mat-card-title>
  </div>

  <hr class="header-divider">

  <div class="sfl-card" fxLayout="column">
    <form #responseForm="ngForm">
      <table class="form-response-table">

        <tr>
          <td class="pb-1">
            <span class="question-required-notice-star"></span>
            <span>Required</span>
          </td>
        </tr>

        <tr *ngFor="let question of sflForm.questionsDTOS; let i=index">
          <td class="py-1">
            <div class="questions-text">
              <span class="question-number">{{i+1}}. </span>
              <span [ngClass]="question.required? 'question-required-notice-star': ''">{{question.text}}</span>
              <div class="question-ml">
                <span class="question-hint">{{question.hint}}</span>
              </div>
            </div>

            <div class="question-ml" *ngIf="question.type === questionType.TEXT">
              <mat-form-field class="form-response-text-field">
                <input matInput placeholder="Enter your answer" aria-label="q-text" name="text-{{question.id}}-{{i}}"
                       [required]="question.required" [(ngModel)]="formResponse.answersDTOS[i].answer" #textInput="ngModel">
              </mat-form-field>
              <mat-error *ngIf="textInput.touched && textInput.invalid">
                <small class="mat-text-warn" *ngIf="textInput?.errors.required">This is required field.</small>
              </mat-error>
            </div>

            <div class="question-ml" *ngIf="question.type === questionType.TEXT_AREA">
              <mat-form-field class="form-response-text-field">
                <textarea matInput placeholder="Enter your answer" name="textArea-{{question.id}}-{{i}}" aria-label="q-textarea"
                          [required]="question.required" [(ngModel)]="formResponse.answersDTOS[i].answer" #textAreaInput="ngModel"></textarea>
              </mat-form-field>
              <mat-error *ngIf="textAreaInput.touched && textAreaInput.invalid">
                <small class="mat-text-warn" *ngIf="textAreaInput?.errors.required">This is required field.</small>
              </mat-error>
            </div>

            <div class="question-ml" *ngIf="question.type === questionType.RADIO">
              <mat-radio-group class="d-inline-block" name="radio-{{question.id}}-{{i}}" [(ngModel)]="formResponse.answersDTOS[i].answer"
                               #radioInput="ngModel" [required]="question.required">
                <mat-radio-button [name]="question.id" class="mr-10px" color="warn" *ngFor="let option of question.optionsDTOS" [value]="option.optionName">
                  {{option.optionName}}
                </mat-radio-button>
              </mat-radio-group>
              <mat-error *ngIf="radioInput.touched && radioInput.invalid">
                <small class="mat-text-warn" *ngIf="radioInput?.errors.required">This is required field.</small>
              </mat-error>
            </div>

            <div class="question-ml" *ngIf="question.type === questionType.MULTI_RADIO">
              <table>
                <thead>
                <tr>
                  <th></th>
                  <th>
                    <span class="d-inline-block px-1 text-center w-60px" *ngFor="let option of question.optionsDTOS">{{option.optionName}}</span>
                  </th>
                </tr>
                </thead>
                <tbody>
                <tr *ngFor="let subQuestion of question.subQuestionsDTOS; let j=index">
                  <td class="questions-text">
                    <span>{{subQuestion.text}}</span>
                  </td>
                  <td>
                    <mat-radio-group class="d-inline-block" name="multiRadio-{{subQuestion.id}}}-{{i}}-{{j}}" #multiRadioInput="ngModel" [required]="question.required"
                                     [(ngModel)]="formResponse.answersDTOS[i].subQuestionsAnswerDTOS[j].answer">
                        <mat-radio-button color="warn" class="px-1 m-0 text-center w-60px" *ngFor="let option of question.optionsDTOS" [value]="option.optionName"></mat-radio-button>
                   </mat-radio-group>
                  </td>
                  <mat-error *ngIf="multiRadioInput.touched && multiRadioInput.invalid">
                    <small class="mat-text-warn" *ngIf="multiRadioInput?.errors.required">This is required field.</small>
                  </mat-error>
                </tr>
                </tbody>
              </table>
            </div>

          </td>
        </tr>
      </table>

    </form>
  </div>

  <div class="p-10" fxLayoutAlign="end">
    <button class="bt-flat" type="submit" (click)="location.back()">Cancel</button>
    <button mat-raised-button class="bt-sfl" type="submit" (click)="submitFormResponse()"
            [style.cursor]="responseForm.form.invalid ? 'not-allowed' : 'pointer'" [disabled]="responseForm.form.invalid">
      Submit
    </button>
  </div>
</div>
