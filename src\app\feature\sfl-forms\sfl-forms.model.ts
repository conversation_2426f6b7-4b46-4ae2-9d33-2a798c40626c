import {Pageable, Sort, Variable} from '../../shared';

export class SflFormsModel {
  id: string;
  status: boolean;
  createdBy: string | number;
  formName: string;
  formType: string;
  description: string;
  generators: string[];
  version: number;
  questionsDTOS: QuestionsDTOS[];

  constructor() {
    this.generators = new Array<string>();
    this.questionsDTOS = new Array<QuestionsDTOS>();
  }
}

export class QuestionsDTOS {
  id: string;
  text: string;
  type: string;
  hint: string;
  required: boolean;
  subQuestionsDTOS: SubQuestionsDTOS[];
  optionsDTOS: OptionsDTOS[];

  constructor() {
    this.subQuestionsDTOS = new Array<SubQuestionsDTOS>();
    this.optionsDTOS = new Array<OptionsDTOS>();
  }
}

export class SubQuestionsDTOS {
  id: string;
  text: string;
}

export class OptionsDTOS {
  id: string;
  optionName: string;
}

export class FilterGeneratedFormPageable {
  content: GenerateForm[];
  pageable: Pageable;
  totalElements: number;
  totalPages: number;
  last: boolean;
  numberOfElements: number;
  first: boolean;
  sort: Sort;
  size: number;
  number: number;
}

export class GenerateForm {
  id?: string;
  sprint?: number;
  startDate?: string | Date;
  endDate?: string | Date;
  fillableBy?: string[];
  createdFormId?: string;
  projectId?: number;
  createdBy?: number;
  status?: boolean;
  formName?: string;
  projectName?: string;
  generateFormModel?: {};
  directory?: {projectId: number};

  constructor() {
    this.fillableBy = new Array<string>();
  }
}

export class FilterGeneratedForm {
  createdFormId: string;
  projectId: number;
  createdBy: number;
  startDate: any;
  endDate: any;
  filterBy: string;
  upToYear: number;
}

export class GenerateFormResponse {
  responseId: string;
  behalfOf: string;
  filledBy: string;
  createdDate: string;
}

export class FormResponse {
  createdFormId: string;
  generatedFormId: string;
  behalfOf: number;
  filledBy: number;
  answersDTOS: AnswersDTOS[];

  constructor() {
    this.answersDTOS = new Array<AnswersDTOS>();
  }
}

export class AnswersDTOS {
  questionId: string;
  answer: string;
  subQuestionsAnswerDTOS: SubQuestionsAnswerDTOS[];

  constructor() {
    this.subQuestionsAnswerDTOS = new Array<SubQuestionsAnswerDTOS>();
  }
}

export class SubQuestionsAnswerDTOS {
  subQuestionId: string;
  answer: string;
}

export const SFL_FORMS_COLUMNS = [
  Variable.FORM_NAME,
  Variable.DESCRIPTION,
  'formType',
  'createdBy',
  'generators',
  'version',
  Variable.ACTION
];

export const GENERATED_FORMS_COLUMNS = [
  Variable.FORM_NAME,
  Variable.PROJECT_NAME,
  'sprint',
  Variable.START_DATE,
  Variable.END_DATE,
  'fillableBy',
  Variable.ACTION
];

export const FORM_RESPONSE_COLUMNS = [
  'filledBy',
  'createdDate',
  'behalfOf',
  Variable.ACTION
];

export const FORM_GENERATOR = [
  Variable.ACCOUNT_MANAGER,
  Variable.DELIVERY_MANAGER,
  Variable.CLIENT,
  Variable.DEVELOPER
];

export const QUESTION_TYPE = {
  TEXT: 'TEXT',
  RADIO: 'RADIO',
  MULTI_RADIO: 'MULTI_RADIO',
  TEXT_AREA: 'TEXT_AREA',
  DROP_DOWN: ['TEXT', 'TEXT_AREA', 'RADIO', 'MULTI_RADIO']
};
