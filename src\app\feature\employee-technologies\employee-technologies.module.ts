import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { employeeTechnologyRoutes } from './employee-technologies.route';
import { EmployeeTechnologiesComponent } from './employee-technologies.component';
import { EmployeeTechnologyService } from './employee-technologies.service';
import { SharedModule } from 'src/app/shared/shared.module';
import { BrowserModule } from '@angular/platform-browser';
import { EmployeeTechnologiesModalComponent } from './employee-technologies-modal/employee-technologies-modal.component';




@NgModule({
  declarations: [
    EmployeeTechnologiesComponent,
    EmployeeTechnologiesModalComponent
  ],
  entryComponents: [
    EmployeeTechnologiesModalComponent
  ],
  imports: [
    SharedModule,
    // BrowserModule,
    CommonModule,
    RouterModule.forChild(employeeTechnologyRoutes),
  ],
  providers: [
    EmployeeTechnologyService
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class EmployeeTechnologiesModule { }
