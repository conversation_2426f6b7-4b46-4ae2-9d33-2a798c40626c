<div class="body-content" fxLayout="column" fxFlex="100">
  <form #reportForm="ngForm">
    <div fxFlex fxLayout="row column" fxLayoutAlign="space-between">
      <mat-card-title class="p-10">Worklog Not Filled</mat-card-title>
      <div fxLayout="row" fxLayoutAlign="end center" fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="50" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="select file type" name="fileType" [(ngModel)]="selectedFile" required>
            <mat-option *ngFor="let file of files" [value]="file">
              {{file}}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <div>
          <button mat-raised-button class="bt-sfl ml-1" [disabled]="reportForm.form.invalid" (click)="getReport()">
            Generate Report
          </button>
        </div>
      </div>
    </div>
  </form>

  <hr class="header-divider">

  <form #filter="ngForm" (ngSubmit)="applyFilter()">
    <div fxLayout="row column" fxLayoutAlign="start center" fxFlex="100" class="sfl-card">

      <mat-card-actions class="filter-input ml-1 mr-1">
        <mat-form-field>
          <mat-select placeholder="Select Username" name="uaaUserId" [(ngModel)]="filterMissedWorklog.uaaUserId">
            <mat-option *ngFor="let username of users" [value]="username.id">{{username?.fullName}}</mat-option>
          </mat-select>
          <button mat-icon-button matSuffix class="float-center" matTooltip="clear"
              *ngIf="filterMissedWorklog.uaaUserId"
              (click)="resetFilter('uaaUserId')">
              <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
      </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1">
        <mat-form-field>
          <mat-select placeholder="Date Filter" name="filterBy" [(ngModel)]="filterMissedWorklog.filterBy">
            <mat-option *ngFor="let dateWise of dateWiseFilter" [value]="dateWise.replace(' ', '')">{{dateWise}}</mat-option>
          </mat-select>
          <button mat-icon-button matSuffix class="float-center" matTooltip="clear" *ngIf="filterMissedWorklog.filterBy"
            (click)="resetFilter('filterBy')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1" *ngIf="filterMissedWorklog.filterBy === uptoYear">
        <mat-form-field>
          <input matInput type="text" placeholder="Year" name="upToYear" aria-label="year" minlength="4" maxlength="4" pattern="\d*"
                 [required]="filterMissedWorklog.filterBy === uptoYear" [(ngModel)]="filterMissedWorklog.upToYear" #uptoYearInput="ngModel" />
          <button mat-icon-button matSuffix matTooltip="clear" *ngIf="filterMissedWorklog.upToYear"
                  (click)="resetFilter('upToYear')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
          <mat-error *ngIf="uptoYearInput.touched && !uptoYearInput.valid">
            <small class="mat-text-warn" *ngIf="uptoYearInput?.errors.required">Year is required.</small>
            <small class="mat-text-warn"
                   *ngIf="uptoYearInput.errors.minlength || uptoYearInput.errors.maxlength || uptoYearInput.errors.pattern">
              Invalid Year.</small>
          </mat-error>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1" *ngIf="filterMissedWorklog.filterBy === freeSearch">
        <mat-form-field>
          <input matInput placeholder="Start date" name="startDate" aria-label="start-date" (focus)="sDate.open()" (click)="sDate.open()"
                 [matDatepicker]="sDate" [max]="filterMissedWorklog.endDate" [required]="filterMissedWorklog.filterBy === freeSearch"
                 [(ngModel)]="filterMissedWorklog.startDate">
          <mat-datepicker-toggle matSuffix [for]="sDate"></mat-datepicker-toggle>
          <mat-datepicker #sDate></mat-datepicker>
          <button mat-icon-button matSuffix class="float-center" matTooltip="clear" *ngIf="filterMissedWorklog.startDate"
            (click)="resetFilter('startDate')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1" *ngIf="filterMissedWorklog.filterBy === freeSearch">
        <mat-form-field>
          <input matInput placeholder="End date" name="endDate" aria-label="end-date" (focus)="eDate.open()" (click)="eDate.open()"
                 [matDatepicker]="eDate" [min]="filterMissedWorklog.startDate" [required]="filterMissedWorklog.filterBy === freeSearch"
                 [(ngModel)]="filterMissedWorklog.endDate">
          <mat-datepicker-toggle matSuffix [for]="eDate"></mat-datepicker-toggle>
          <mat-datepicker #eDate></mat-datepicker>
          <button mat-icon-button matSuffix class="float-center" matTooltip="clear" *ngIf="filterMissedWorklog.endDate"
            (click)="resetFilter('endDate')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-button-group ml-1">
        <button mat-raised-button class="bt-sfl mr-10px" type="submit"
                [disabled]="filter.form.invalid">Filter</button>
        <button class="bt-flat" type="button" (click)="resetAllFilter()">Reset All</button>
      </mat-card-actions>

    </div>
  </form>

  <div class="sfl-card">
    <div class="p-25" fxLayoutAlign="center center" fxFlex="100" *ngIf="showLoader">
      <mat-progress-spinner color="warn" mode="indeterminate"></mat-progress-spinner>
    </div>

    <div *ngIf="!showLoader && noDataFound" class="w-100 mt-40 no-data-found-text">No Missing Worklog Found.</div>

    <div class="w-100" *ngIf="!showLoader && !noDataFound">
      <table class="w-auto" mat-table matSort matSortActive="localDate" matSortDirection="desc" matSortDisableClear [dataSource]="dataSource">

        <ng-container matColumnDef="localDate">
          <mat-header-cell *matHeaderCellDef mat-sort-header> Date </mat-header-cell>
          <mat-cell *matCellDef="let element">
            {{element?.localDate | date: dateFormat}} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="name">
          <mat-header-cell *matHeaderCellDef mat-sort-header> Name </mat-header-cell>
          <mat-cell *matCellDef="let element">
            {{element?.name}}
          </mat-cell>
        </ng-container>

        <ng-container matColumnDef="email">
          <mat-header-cell *matHeaderCellDef mat-sort-header> Email Address </mat-header-cell>
          <mat-cell *matCellDef="let element">
            {{element?.email}} </mat-cell>
        </ng-container>

        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
      </table>
      <mat-paginator [pageSizeOptions]="[10, 20, 25]" showFirstLastButtons></mat-paginator>
    </div>
  </div>

</div>
