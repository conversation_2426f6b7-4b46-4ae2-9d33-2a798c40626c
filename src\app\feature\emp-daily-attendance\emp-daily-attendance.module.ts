import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { RouterModule } from '@angular/router';
import { SharedModule } from '../../shared/shared.module';
import { empDailyAttendanceRoutes } from './emp-daily-attendance.route';
import { EmpDailyAttendanceComponent } from './emp-daily-attendance.component';
import { EmpDailyAttendanceService } from './emp-daily-attendance.service';
import { DashboardService } from '../dashboard/dashboard.service';
import { DatePipe } from '@angular/common';


@NgModule({
  imports: [
    RouterModule.forChild(empDailyAttendanceRoutes),
    SharedModule
  ],
  declarations: [
    EmpDailyAttendanceComponent
  ],
  providers: [
    EmpDailyAttendanceService,
    DashboardService,
    DatePipe
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})

export class EmployeeAttendanceModule { }
