import {<PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t, ViewChild} from '@angular/core';
import {Mat<PERSON><PERSON>og, MatPaginator, MatSort, MatTableDataSource} from '@angular/material';
import {SflFormsModel, SFL_FORMS_COLUMNS} from './sfl-forms.model';
import {SharedService, Variable} from '../../shared';
import {Subscription} from 'rxjs';
import {SflFormsService} from './sfl-forms.service';
import {SweetAlertService} from '../../shared/service/sweetalert.service';
import {PreviewModalComponent} from './preview-modal/preview-modal.component';
import {AppConfig} from '../../app.config';
import { GeneratorModalComponent } from './generator-modal/generator-modal.component';
import {finalize} from 'rxjs/operators';


@Component({
  selector: 'sfl-forms',
  templateUrl: './sfl-forms.component.html'
})
export class SflFormsComponent implements OnI<PERSON>t, On<PERSON><PERSON>roy {
  @ViewChild(MatPaginator, {static: false})
  set paginator(value: MatPaginator) {
    this.dataSource.paginator = value;
  }
  @ViewChild(MatSort, {static: false})
  set sort(value: MatSort) {
    this.dataSource.sort = value;
  }

  showLoader = true;
  noDataFound = true;
  isPreviewModalOpen = false;
  displayedColumns = SFL_FORMS_COLUMNS;
  formCreateURL = AppConfig._FORMS_CREATE;
  dataSource = new MatTableDataSource<SflFormsModel>([]);
  subscription: Subscription = new Subscription();

  constructor(
    private matDialog: MatDialog,
    private sharedService: SharedService,
    private sflFormsService: SflFormsService,
    private alertService: SweetAlertService
  ) { }

  ngOnInit() {
    this.getCreatedForms();
  }

  getCreatedForms() {
    this.showLoader = true;
    this.noDataFound = true;
    this.subscription.add(this.sflFormsService.getCreatedForms()
      .pipe(
        finalize(() => {
          this.noDataFound = this.dataSource.data.length <= 0;
          this.showLoader = false;
        })
      )
      .subscribe((res: SflFormsModel[]) => {
        this.dataSource = new MatTableDataSource<SflFormsModel>(res);
      })
    );
  }

  updateFormGenerator(sflForm: SflFormsModel) {
    const dialogRef = this.matDialog.open(GeneratorModalComponent, {
      width: Variable.BOX_WIDTH_VALUE,
      data: sflForm
    });
    dialogRef.afterClosed().subscribe(() => {
      this.getCreatedForms();
    });
  }

  previewForm(sflForm: SflFormsModel) {
    if (!this.isPreviewModalOpen) {
      this.isPreviewModalOpen = true;
      this.subscription.add(
        this.sflFormsService.getCreatedFormsById(sflForm.id).subscribe((res: SflFormsModel) => {
          this.matDialog.open(PreviewModalComponent, {
            data: res
          });
          this.isPreviewModalOpen = false;
        })
      );
    }
  }

  deleteForm(sflForm: SflFormsModel) {
    this.alertService.deleteAlert(Variable.THIS_FORM).then(value => {
      if (value === true) {
        this.sflFormsService.deleteCreatedForm(sflForm.id).subscribe(() => {
          this.getCreatedForms();
        });
      }
    });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
