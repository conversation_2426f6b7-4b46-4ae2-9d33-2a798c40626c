import { Injectable } from '@angular/core';
import { AppConfig } from '../../app.config';
import {HttpClientService, createRequestOption, PageableQuery} from '../../shared';
import {FilterWorklog, Worklog} from './worklog.model';
import { Subject } from 'rxjs';


@Injectable()
export class WorklogService {
  invokeEvent: Subject<any> = new Subject();

  constructor(private http: HttpClientService) { }

  callMethodOfWorklogComponent() {
    this.invokeEvent.next();
  }

  getWorkTypes() {
    return this.http.get(AppConfig.WORK_TYPES_ALL);
  }

  createWorklog(worklog: Worklog) {
    return this.http.post(AppConfig.WORKLOG, worklog);
  }

  getFilterWorklog(parameterValue: FilterWorklog, pageableObject: PageableQuery) {
    return this.http.post(AppConfig.FILTER_WORKLOG, parameterValue, {
      params: createRequestOption(pageableObject)
    });
  }

  getMissedWorklog(parameterValue: FilterWorklog) {
    return this.http.post(AppConfig.MISSED_WORKLOG, parameterValue);
  }

  getMissedWorklogFile(parameterValue: FilterWorklog, fileType: string, uaaUserID: number) {
    return this.http.post(AppConfig.MISSED_WORKLOG_FILE + fileType + '/' + uaaUserID, parameterValue);
  }

  getWorklogFile(parameterValue: FilterWorklog, uaaUserID: number) {
    return this.http.post(AppConfig.WORKLOG_FILE + uaaUserID, parameterValue);
  }

  updateWorklog(worklog: Worklog) {
    return this.http.put(AppConfig.UPDATE_WORKLOG, worklog);
  }

  deleteWorklog(parameterValue: FilterWorklog) {
    return this.http.delete(AppConfig.WORKLOG + parameterValue.id);
  }

}
