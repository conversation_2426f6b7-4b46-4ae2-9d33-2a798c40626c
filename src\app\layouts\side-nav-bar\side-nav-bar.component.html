<div class="mat-typography" [dir]="options.dir">
  <sfl-top-bar (toggleSidenav)="sideMenu.toggle()"></sfl-top-bar>

  <mat-sidenav-container class="app-inner">
    <mat-sidenav #sideMenu class="menu-drawer" [mode]="isOver() ? 'over' : 'side'" [opened]="!isOver()">
      <mat-nav-list>
        <ng-container *ngFor="let menuItem of sideNavBarService.getAll(); trackBy: trackByFn">
          <mat-accordion displayMode="flat" multi="true" *ngIf="menuItem.type === MENU_TYPE.SUB && menuItem.visibility">
            <mat-expansion-panel expanded="true" class="mat-elevation-z0">
              <mat-expansion-panel-header>
                <mat-panel-title>
                  <mat-icon mat-list-icon>{{ menuItem.icon }}</mat-icon>
                  <h6 mat-line class="pr-2 menu-header-text">{{ menuItem.name }}</h6>
                </mat-panel-title>
              </mat-expansion-panel-header>

              <ng-container matExpansionPanelContent *ngFor="let menuChildItem of menuItem.children; trackBy: trackByFn">
                <mat-list-item *ngIf="menuChildItem.type === MENU_TYPE.LINK && menuChildItem.visibility" [routerLink]="menuChildItem.state" routerLinkActive="active" class="p-0">
                  <mat-icon class="icon-padding" mat-list-icon>lens</mat-icon>
                  <h6 mat-line class="pr-2">{{ menuChildItem.name }}</h6>
                </mat-list-item>
              </ng-container>
            </mat-expansion-panel>
          </mat-accordion>

          <mat-list-item *ngIf="menuItem.type === MENU_TYPE.LINK && menuItem.visibility" [routerLink]="menuItem.state" routerLinkActive="active">
            <mat-icon mat-list-icon>{{ menuItem.icon }}</mat-icon>
            <h6 mat-line class="pr-2 menu-header-text">{{ menuItem.name }}</h6>
          </mat-list-item>

          <mat-list-item *ngIf="menuItem.type === MENU_TYPE.EXT && menuItem.visibility" class="ext-nav-list">
            <a matLine class="d-flex align-item-center" [href]="menuItem.state" target="_blank">
              <mat-icon mat-list-icon>{{ menuItem.icon }}</mat-icon>
              <h6 mat-line class="pr-2 m-0 menu-header-text">{{ menuItem.name }}</h6>
            </a>
          </mat-list-item>
        </ng-container>
      </mat-nav-list>
    </mat-sidenav>

    <div>
      <router-outlet></router-outlet>
    </div>
  </mat-sidenav-container>
</div>
