<div class="body-content" fxLayout="column" fxFlex="100">
  <div fxFlex fxLayout="row" fxLayoutAlign="space-between" class="mb-1">
    <mat-card-title class="p-10">Job Code</mat-card-title>
    <button mat-raised-button class="bt-sfl" (click)="saveJobCode()">Add New Job Code</button>
  </div>

  <hr class="header-divider" />

  <div fxLayout="row" fxFlex="100" class="sfl-card">
    <div class="filter-input ml-1 mr-1">
      <mat-form-field floatLabel="never">
        <input matInput type="text" #filter (keyup)="applyFilter(filter.value)" placeholder="Filter" aria-label="filter" />
      </mat-form-field>
    </div>
  </div>

  <div class="sfl-card">
    <div class="p-25" fxLayoutAlign="center center" fxFlex="100" *ngIf="showLoader">
      <mat-progress-spinner color="warn" mode="indeterminate"></mat-progress-spinner>
    </div>

    <div *ngIf="!showLoader && noDataFound" class="w-100 mt-40 no-data-found-text">No Job Code Found.</div>
    <div class="w-100" *ngIf="!showLoader && !noDataFound">
      <table class="w-auto" mat-table matSort [dataSource]="dataSource" matSortActive="name" matSortDirection="asc" matSortDisableClear>
        <ng-container matColumnDef="name">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="30"> Job Code </mat-header-cell>
          <mat-cell class="link cursor-pointer" (click)="openModal(element, false)" *matCellDef="let element" fxFlex="30">
            {{ element?.name }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="clientName">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="30"> Client </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="30"> {{ element?.clientName }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="projectName">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="30"> Project </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="30"> {{ element?.projectName }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="technologyName">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="30"> Technology </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="30"> {{ element?.technologyName }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="isActive">
          <mat-header-cell *matHeaderCellDef fxFlex="10" mat-sort-header> Job Code Status </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10">
            <mat-slide-toggle (change)="updateJobCodeStatus(element?.id, $event)" name="active" [(ngModel)]="element.isActive"> </mat-slide-toggle>
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxLayoutAlign="start center" fxFlex="5"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxLayoutAlign="start center" fxFlex="5">
            <button mat-icon-button matTooltip="Edit Job Code" (click)="openModal(element, true)">
              <mat-icon>edit level</mat-icon>
            </button>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns"></mat-row>
      </table>
      <mat-paginator [pageSizeOptions]="[10, 20, 25]" showFirstLastButtons></mat-paginator>
    </div>
  </div>
</div>
