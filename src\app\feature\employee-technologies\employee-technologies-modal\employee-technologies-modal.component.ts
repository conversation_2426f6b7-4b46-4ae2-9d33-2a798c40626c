import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { Subscription } from 'rxjs';
import { Variable } from '../../../shared';
import { Employee } from '../../employee-management/employee-management.model';
import { EmployeeManagementService } from '../../employee-management/employee-management.service';
// import { ScheduleModal, ScheduleModel } from '../schedule.model';
import { Project } from '../../project/project.model';
import { Technology } from '../../technology/technology.model';
import { TechnologyService } from '../../technology/technology.service';
import { EmployeeTechnologies } from '../employee-technologies.model';
import { EmployeeTechnologyService } from '../employee-technologies.service';

@Component({
  selector: 'sfl-employee-technologies-modal',
  templateUrl: './employee-technologies-modal.component.html'
})
export class EmployeeTechnologiesModalComponent implements OnInit {
  subscription: Subscription = new Subscription();
  projects: Project[] = [];
  users: Employee[] = [];
  technologies: Technology[] = [];
  empTechnology: EmployeeTechnologies = new EmployeeTechnologies();
  newData: EmployeeTechnologies[] = [];
  editTextLabel = Variable.EDIT;
  copyId: number;
  isEmployeeTechnologyEdit = false;
  technologyIds = [];
  showLoader = true;
  addText = Variable.ADD;
  scheduleModal;
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: EmployeeTechnologies,
    private readonly employeeTechnologyService: EmployeeTechnologyService,
    private readonly technologyService: TechnologyService,
    private readonly employeeService: EmployeeManagementService,
    public readonly dialogRef: MatDialogRef<EmployeeTechnologiesModalComponent>
  ) {}

  ngOnInit() {
    this.getUsers();
    this.getTechnologies();
    this.empTechnology = this.data;
    this.data.uaaUserId = this.data.userId;
    this.technologyIds = [];
    this.data.technologyDTOS.forEach(item => {
      this.technologyIds.push(item.id);
    });
    this.data.technologyId = this.technologyIds;

    if (this.data.userId) {
      this.isEmployeeTechnologyEdit = true;
    } else {
      this.isEmployeeTechnologyEdit = false;
    }
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  saveSchedule() {
    if (this.isEmployeeTechnologyEdit) {
      this.updateEmployeeTechnology();
    } else {
      this.addEmployeeTechnology();
    }
  }

  addEmployeeTechnology() {
    this.subscription.add(
      this.employeeTechnologyService.addEmployeeTechnology(this.data).subscribe(() => {
        this.dialogRef.close(true);
      })
    );
  }

  updateEmployeeTechnology() {
    this.subscription.add(
      this.employeeTechnologyService.updateEmployeeTechnology(this.data).subscribe(() => {
        this.dialogRef.close(true);
      })
    );
  }

  getTechnologies() {
    this.subscription.add(
      this.technologyService.getTechnologies().subscribe((res: Technology[]) => {
        this.technologies = res;
      })
    );
  }

  getUsers() {
    this.subscription.add(
      this.employeeService.getEmployees().subscribe((res: Employee[]) => {
        this.users = res;
      })
    );
  }
}
