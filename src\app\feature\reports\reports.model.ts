export const TIME_SHEET_DISPLAY_COLUMNS = ['jobCodeName', 'totalHours'];
export class WorklogParams {
  id?: number;
  logDate: string;
  ticketLink = ''; //To be removed later by BE
  description = ''; //To be removed later by BE
  jobCodeId: number;
  uaaUserId: number;
  totalWorkLogTimeStr?: string;
  totalWorkLogTime: number;
  logs: Log[] = [];
}

export interface TechnologyWithEmployees {
  technologyId: number;
  technologyName: string;
  users: Employees[];
}

export interface Employees {
  id: number;
  name: string;
}

export class WorklogListItem {
  user: User;
  technologyList: TechnologyList[] = [];
  totalAllJobCodeLogTime: number;
  timeTrack: TimeTrack[] = [];
}

export interface User {
  id: number;
  name: string;
}

export interface TechnologyList {
  id: number;
  uaaUserId: number;
  technologyId: number;
  technologyName: string;
}

export class TimeTrack {
  id: number | null;
  jobCodeId: number;
  jobCodeName: string;
  isPinned: boolean;
  jobCodeDisplayName: string;
  totalWorkLogTime: number;
  workLogs: WorkLog[] = [];
  projectName?: string;
  isBillable: boolean;
}

export interface WorkLog {
  id: number;
  logDate: string;
  timeTrackLogsTotal: number;
  logs: Log[];
}

export class Log {
  id?: number;
  logTime: number;
  logTimeStr?: string;
  ticketLink: string;
  description: string;
  timeTrackId?: number;
}

export class WorkLogModalData {
  mode: ModalMode;
  date: Date;
  worklogData: WorkLog;
  jobCode: IdNameDto;
  userId: number;
  dailyTotal: any;
}

export class TimeSheetFilter {
  fromDate: string;
  toDate: string;
  uaaUserId?: number;
  employeeId?: number;
  uaaUserIds?: number[];
  technologyId?: number;
  projectId?: number;
  projectIds?: number[];
}

export enum ModalMode {
  ADD_MODE = 'isAddMode',
  EDIT_MODE = 'isEditMode',
  VIEW_MODE = 'isViewMode'
}

export enum ReportType {
  PROJECT_REPORT = 'Project Report',
  DEVELOPER_REPORT = 'Developer Report'
}

export interface IdNameDto {
  id: number;
  name: string;
}

export interface TimeSheetResponse {
  usersTimeTrackDTOS: WorklogListItem[];
}

export class PinDto {
  constructor(public userId: number, public jobCodeId: number, public pinStatus: boolean) {}
}

export const FilterName = {
  EMPLOYEE: 'employeeId',
  TECHNOLOGY: 'technologyId',
  PROJECT: 'projectId',
  ALL: 'all'
};

export class ProjectWorklogListItem {
  users: User[] = [];
  technologyList: TechnologyList[] = [];
  totalAllJobCodeLogTime: number;
  timeTrack: TimeTrack[] = [];
  projectName: string;
}

export class ProjectReportWorklogListItem {
  technologyList: TechnologyList[] = [];
  timeTrackList: timeTrackList[] = [];
  projectName: string;
}

export class timeTrackList {
  technologyList: TechnologyList[] = [];
  timeTrack: TimeTrack[] = [];
  user: User
  totalAllJobCodeLogTime: number;

}