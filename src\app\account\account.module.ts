import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { RouterModule } from '@angular/router';
import { SharedModule } from '../shared/shared.module';
import { LoginComponent, ForgotPasswordComponent } from '.';
import { accountRoutes } from './account.route';
import { LoginService } from './login/login.service';
import {ChangePasswordService} from './change-password/change-password.service';
import {ChangePasswordComponent} from './change-password/change-password.component';


@NgModule({
    imports: [
      RouterModule.forChild(accountRoutes),
      SharedModule
    ],
    declarations: [
      LoginComponent,
      ForgotPasswordComponent,
      ChangePasswordComponent
    ],
    entryComponents: [
    ],
    providers: [
      LoginService,
      ChangePasswordService
    ],
    schemas: [CUSTOM_ELEMENTS_SCHEMA]
})

export class AccountModule { }
