<div class="body-content" fxLayout="column" fxFlex="100">
  <div fxLayout="row" fxLayoutAlign="space-between" class="mb-1">
    <mat-card-title class="p-10">Employees</mat-card-title>
  </div>

  <hr class="header-divider" />

  <div fxLayout="row" fxFlex="100" class="sfl-card">
    <div class="filter-input ml-1 mr-1">
      <mat-form-field floatLabel="never">
        <input matInput type="text" #filter [(ngModel)]="filterValue" (keyup)="applyFilter()" placeholder="Filter" aria-label="filter" />
      </mat-form-field>
    </div>
  </div>

  <div class="sfl-card">
    <div class="p-25" fxLayoutAlign="center center" fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100" *ngIf="showLoader">
      <mat-progress-spinner color="warn" mode="indeterminate"></mat-progress-spinner>
    </div>

    <div *ngIf="!showLoader && noDataFound" class="w-100 mt-40 no-data-found-text">No Employee Found.</div>

    <div class="w-100" *ngIf="!showLoader && !noDataFound">
      <table class="w-auto" mat-table matSort [dataSource]="dataSource" matSortActive="fullName" matSortDirection="asc" matSortDisableClear>
        <ng-container matColumnDef="fullName">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Name </mat-header-cell>
          <mat-cell class="link cursor-pointer" fxFlex="20" *matCellDef="let element" [routerLink]="[employeeProfileURL, element?.id]">
            {{ element?.fullName }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="email">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="30"> Email </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="30"> {{ element?.email }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="phoneNo">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="15"> Phone Number </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="15"> {{ element?.phoneNo }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="userLevelDTO.empLevelId">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Employee Designation </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20">{{ element?.designationName || ' ' }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="status">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="5"> Status </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="5">
            <mat-slide-toggle (change)="updateEmployeeStatus(element?.id, $event)" name="status" [(ngModel)]="element.status"> </mat-slide-toggle>
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxLayoutAlign="start center" fxFlex="7"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxLayoutAlign="start center" fxFlex="7">
            <button mat-icon-button matTooltip="Edit Employee" (click)="openEditLevel(element)">
              <mat-icon>edit level</mat-icon>
            </button>
            <button mat-icon-button matTooltip="Change Password" (click)="openChangePassword(element)">
              <mat-icon>lock_open</mat-icon>
            </button>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns"></mat-row>
      </table>
      <mat-paginator [pageSizeOptions]="[10, 20, 25]" showFirstLastButtons></mat-paginator>
    </div>
  </div>
</div>
