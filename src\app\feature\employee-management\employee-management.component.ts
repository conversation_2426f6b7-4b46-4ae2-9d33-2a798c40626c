import { HttpErrorResponse } from '@angular/common/http';
import { Component, OnD<PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { MatDialog, MatPaginator, MatSort, MatTableDataSource } from '@angular/material';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { ChangePassword } from 'src/app/account/change-password/change-password.model';
import { ChangePasswordService } from 'src/app/account/change-password/change-password.service';
import { Messages, SharedService, SnackBarService } from 'src/app/shared';
import { ChangePasswordDialogComponent } from 'src/app/shared/components/change-password-dialog/change-password-dialog.component';
import { ROLES, Variable } from 'src/app/shared/constants/Variable.constants';
import { AppConfig } from '../../app.config';
import { EditLevelComponent } from '../profile/edit-level/edit-level.component';
import { EditEmployeeLevelDialogData } from '../profile/profile.model';
import { ProfileService } from '../profile/profile.service';
import { EMPLOYEE_LIST_DISPLAY_COLUMNS, Employee, EmployeeRequest, SUPERADMIN_EMPLOYEE_LIST_DISPLAY_COLUMNS } from './employee-management.model';
import { EmployeeManagementService } from './employee-management.service';

@Component({
  selector: 'sfl-employee-list',
  templateUrl: './employee-management.component.html',
})
export class EmployeeManagementComponent implements OnInit, OnDestroy {
  @ViewChild(MatSort, { static: false }) set setSort(sort: MatSort) {
    this.dataSource.sort = sort;
  }
  @ViewChild(MatPaginator, { static: false }) set setPaginator(paginator: MatPaginator) {
    this.dataSource.paginator = paginator;
  }

  showLoader = true;
  noDataFound = true;
  filterValue = '';
  displayedColumns = [];
  employeeProfileURL = AppConfig._EMPLOYEE_PROFILE;
  dataSource = new MatTableDataSource<Employee>();
  subscription: Subscription = new Subscription();
  updateEmployee: Employee = new Employee();
  editEmployeeDetail: EmployeeRequest = new EmployeeRequest();
  constructor(
    private matDialog: MatDialog,
    private employeeService: EmployeeManagementService,
    private profileService: ProfileService,
    private snackBarService: SnackBarService,
    private sharedService: SharedService,
    private changePasswordService: ChangePasswordService,
  ) {}

  ngOnInit() {
    this.hasPermission();
    this.getAll();
  }

  private getAll(): void {
    this.showLoader = true;
    this.subscription.add(
      this.employeeService
        .getEmployees()
        .pipe(
          finalize(() => {
            this.noDataFound = this.dataSource.data.length <= 0;
            this.showLoader = false;
            this.applyFilter();
          }),
        )
        .subscribe((res: Employee[]) => {
          this.dataSource = new MatTableDataSource<Employee>(res);
        }),
    );
  }

  applyFilter() {
    this.dataSource.filter = this.filterValue.trim().toLowerCase();
  }

  openEditLevel(element: Employee) {
    let isEdit = false;
    this.employeeService.getEmployeeById(element.id).subscribe((res: EmployeeRequest) => {
      this.editEmployeeDetail = res;
      isEdit = true;
      const dialogRef = this.matDialog.open(EditLevelComponent, {
        width: Variable.BOX_WIDTH_VALUE,
        data: {
          employee: this.editEmployeeDetail,
          isEdit: isEdit,
        },
      });

      dialogRef.afterClosed().subscribe((res: EditEmployeeLevelDialogData) => {
        if (res) {
          const employeeRequestBody = {
            id: res.employee.id,
            login: res.employee.login,
            firstName: res.employee.firstName,
            lastName: res.employee.lastName,
            activated: res.employee.activated,
            email: res.employee.email,
            authorityId: res.employee.authorityId,
            phoneNo: res.employee.phoneNo,
            designationId: res.employee.designationId,
          };
          if (res.isEdit) {
            this.subscription.add(
              this.profileService.updateEmployee(employeeRequestBody).subscribe((res: Employee) => {
                this.getAll();
              }),
            );
          }
        }
      });
    });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  updateEmployeeStatus(id, ob) {
    this.updateEmployee.id = id;
    this.updateEmployee.status = ob.checked;
    this.employeeService.updateEmployeeStatus(this.updateEmployee).subscribe(
      () => {
        this.getAll();
      },
      (error: HttpErrorResponse) => {
        if (error.error && error.error.detail) {
          this.snackBarService.error(error.error.detail);
        }
        this.getAll();
      },
    );
  }

  hasPermission() {
    const roles = this.sharedService.getRole();
    this.displayedColumns = roles.includes(ROLES.SUPER_ADMIN) ? SUPERADMIN_EMPLOYEE_LIST_DISPLAY_COLUMNS : EMPLOYEE_LIST_DISPLAY_COLUMNS;
  }
  openChangePassword(element: Employee) {
    const dialogRef = this.matDialog.open(ChangePasswordDialogComponent, {
      width: Variable.BOX_WIDTH_VALUE,
    });

    dialogRef.afterClosed().subscribe((res: ChangePassword) => {
      if (res) {
        res.userId = element.id;
        res.oldPassword = null;
        this.subscription.add(
          this.changePasswordService.setNewPassword(res).subscribe(
            (res) => {
              this.snackBarService.success(Messages.PASSWORD.success_save);
            },
            (error: HttpErrorResponse) => {
              if (error.error && error.error.detail) {
                this.snackBarService.error(error.error.detail);
              }
            },
          ),
        );
      }
    });
  }
}
