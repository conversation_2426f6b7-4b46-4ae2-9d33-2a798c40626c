import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { RouterModule } from '@angular/router';
import { SharedModule } from '../../shared/shared.module';
import { leaveRoutes } from './leave.route';
import { LeaveComponent } from './leave.component';
import { LeaveService } from './leave.service';
import { LeaveModalComponent } from './leave-modal/leave-modal.component';
import {EmployeeManagementService} from '../employee-management/employee-management.service';

@NgModule({
  imports: [
    RouterModule.forChild(leaveRoutes),
    SharedModule
  ],
  declarations: [
    LeaveComponent,
    LeaveModalComponent
  ],
  providers: [
    LeaveService,
    EmployeeManagementService
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})

export class LeaveModule { }
