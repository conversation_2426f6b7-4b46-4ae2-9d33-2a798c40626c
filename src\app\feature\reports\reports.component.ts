import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { MatTableDataSource } from '@angular/material';
import * as ExcelJS from 'exceljs';
import * as FileSaver from 'file-saver';
import moment from 'moment';
import { Subscription, from } from 'rxjs';
import { finalize, reduce } from 'rxjs/operators';
import { ROLES, SharedService, SnackBarService, Variable } from 'src/app/shared';
import { ConvertSecondsToHourPipe } from 'src/app/shared/pipes/time-converter.pipe';
import { JobCodeListItem } from '../job-code/job-code';
import { Project } from '../project/project.model';
import { ProjectService } from '../project/project.service';

import {
  Employees,
  FilterName,
  IdNameDto,
  ProjectReportWorklogListItem,
  ReportType,
  TechnologyWithEmployees,
  TimeSheetFilter,
  TimeTrack,
  WorklogListItem
} from './reports.model';

import { Employee } from "../employee-management/employee-management.model";
import { EmployeeManagementService } from "../employee-management/employee-management.service";
import { ReportsService } from "./reports.service";
const EXCEL_TYPE =
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8";

@Component({
  selector: "app-reports",
  templateUrl: "./reports.component.html",
  styleUrls: ["./reports.component.css"],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ConvertSecondsToHourPipe],
})
export class ReportsComponent implements OnInit {
  workLogList: WorklogListItem[] = [];
  projectWorkLogList: ProjectReportWorklogListItem[] = [];
  subscription: Subscription = new Subscription();
  FilterName = FilterName;
  isExportingProject = false;
  isExportingDeveloper = false;

  isAllEmployeeSelected = false;
  isAllProjectSelected = false;
  loggedInUser: IdNameDto;
  projects: Project[] = [];
  filterParam: TimeSheetFilter = new TimeSheetFilter();
  jobCodes: JobCodeListItem[];
  filteredJobCode: JobCodeListItem[] = [];
  currentDate: Date = new Date();
  startingDate!: Date;
  endingDate!: Date;
  allDates!: Date[];
  technologies: TechnologyWithEmployees[] = [];
  users: Employees[] = [];
  developers: Employee[] = [];
  dateRangeStr = "";
  monthOrWeek = "1";
  dailyTotal: any;
  calendarDates!: Date[];
  jobCodeList = [];
  developersList = [];
  projectListArray = [];
  ReportType = ReportType;

  constructor(
    private sharedService: SharedService,
    private reportsService: ReportsService,
    private projectService: ProjectService,
    private cdf: ChangeDetectorRef,
    private snackBarService: SnackBarService,
    private convertSecondsToHour: ConvertSecondsToHourPipe,
    private employeeService: EmployeeManagementService
  ) {}

  async ngOnInit(): Promise<void> {
    this.loggedInUser = {
      id: this.sharedService.getUaaUserId(),
      name: this.sharedService.getUserName(),
    };
    this.setRangeDates();
    if (this.hasPermission(ReportType.PROJECT_REPORT)) {
      this.getProjects();
    }
    if (this.hasPermission(ReportType.DEVELOPER_REPORT)) {
      this.getUsers();
    }
  }

  private setSearchFilter(): void {
    this.filterParam.fromDate = moment(this.startingDate).format(
      "YYYY-MM-DDTHH:mm:ssZ"
    );
    this.filterParam.toDate = moment(this.endingDate).format(
      "YYYY-MM-DDTHH:mm:ssZ"
    );
  }

  setRangeDates(): void {
    if (this.monthOrWeek === "1") {
      this.startingDate = new Date(
        this.currentDate.getFullYear(),
        this.currentDate.getMonth(),
        1
      );
      this.endingDate = new Date(
        this.startingDate.getFullYear(),
        this.startingDate.getMonth() + 1,
        0
      );
    } else {
      this.currentDate = new Date(
        this.currentDate.getFullYear(),
        this.currentDate.getMonth(),
        this.currentDate.getDate()
      );
      while (this.currentDate.getDay() !== 1) {
        this.currentDate.setDate(this.currentDate.getDate() - 1);
      }
      this.startingDate = this.currentDate;
      this.endingDate = new Date(
        this.startingDate.getFullYear(),
        this.startingDate.getMonth(),
        this.startingDate.getDate() + 6
      );
    }
    this.calculateDates();
    this.setSearchFilter();
  }

  calculateDates(): void {
    this.allDates = [];
    this.calendarDates = [];
    const traverseDate = new Date(this.startingDate);
    while (traverseDate <= this.endingDate) {
      this.allDates.push(new Date(traverseDate));
      traverseDate.setDate(traverseDate.getDate() + 1);
    }
    this.calendarDates = [
      this.allDates[0],
      this.allDates[this.allDates.length - 1],
    ];
    this.dateRangeStr = `${moment(this.calendarDates[0]).format(
      Variable.DD_MM_YYYY
    )} - ${moment(this.calendarDates[1]).format(Variable.DD_MM_YYYY)}`;
  }

  nextRange(): void {
    const newDate = new Date(this.startingDate);
    this.monthOrWeek === "1"
      ? newDate.setMonth(newDate.getMonth() + 1)
      : newDate.setDate(newDate.getDate() + 7);
    this.currentDate = newDate;
    this.setRangeDates();
  }

  previousRange(): void {
    const newDate = new Date(this.startingDate);
    this.monthOrWeek === "1"
      ? newDate.setMonth(newDate.getMonth() - 1)
      : newDate.setDate(newDate.getDate() - 7);
    this.currentDate = newDate;
    this.setRangeDates();
  }

  onStateChange(value: string): void {
    this.filteredJobCode = value ? this.filterJobCodes(value) : [];
  }

  filterJobCodes(name: string): JobCodeListItem[] {
    return this.jobCodes.filter((jobCode) =>
      jobCode?.displayName?.toLowerCase().includes(name.toLowerCase())
    );
  }

  private getWorkLogs(): void {
    this.projectWorkLogList = [];
    this.jobCodeList = [];
    this.developersList = [];
    this.projectListArray = [];
    this.isExportingProject = true;
    this.cdf.detectChanges();
    this.subscription.add(
      this.reportsService
        .getAllWorkLogs(this.filterParam)
        .pipe(
          finalize(() => {
            this.cdf.detectChanges();
          })
        )
        .subscribe({
          next: (res: ProjectReportWorklogListItem[]) => {
            this.projectWorkLogList = res;
            this.generateProjectExcel();
            this.isExportingProject = false;
          },
          error: (error) => {
            this.snackBarService.error(error.error.message);
          },
        })
    );
  }

  pToDTransformation() {
    const jobCodeGroup = {};

    this.projectWorkLogList.forEach(projectWorkLog => {
        projectWorkLog.timeTrackList.forEach(tList => {
            const { name } = tList.user;

            tList.timeTrack.forEach(timeTrack => {
                const exists = this.jobCodeList.some(jobCodeEntry =>
                    jobCodeEntry.jobCodeId === timeTrack.jobCodeId &&
                    jobCodeEntry.developer === name &&
                    jobCodeEntry.projectName === projectWorkLog.projectName
                );

                if (!exists) {
                    this.jobCodeList.push({
                        jobCodeId: timeTrack.jobCodeId,
                        jobCodeName: timeTrack.jobCodeName,
                        isBillable: timeTrack.isBillable,
                        projectName: projectWorkLog.projectName,
                        totalWorkLogTime: timeTrack.totalWorkLogTime,
                        totalAllJobCodeLogTime: tList.totalAllJobCodeLogTime,
                        developer: name,
                    });
                }
            });

            if (!this.developersList[projectWorkLog.projectName]) {
                this.developersList[projectWorkLog.projectName] = new Set();
            }
            this.developersList[projectWorkLog.projectName].add(name);
        });
    });

    this.jobCodeList.forEach(result => {
        jobCodeGroup[result.projectName] = jobCodeGroup[result.projectName] || {
            jobCodes: {},
            billable: {},
            nonBillable: {},
        };

        jobCodeGroup[result.projectName].jobCodes[result.jobCodeName] = jobCodeGroup[result.projectName].jobCodes[result.jobCodeName] || {
            total: 0,
            developers: {},
            isBillable: false,
        };

        jobCodeGroup[result.projectName].jobCodes[result.jobCodeName].developers[result.developer] = (jobCodeGroup[result.projectName].jobCodes[result.jobCodeName].developers[result.developer] || 0) + result.totalWorkLogTime;
        jobCodeGroup[result.projectName].jobCodes[result.jobCodeName].total += result.totalWorkLogTime;
        jobCodeGroup[result.projectName].jobCodes[result.jobCodeName].isBillable = result.isBillable;

        if (result.isBillable) {
            jobCodeGroup[result.projectName].billable[result.developer] = (jobCodeGroup[result.projectName].billable[result.developer] || 0) + result.totalWorkLogTime;
        } else {
            jobCodeGroup[result.projectName].nonBillable[result.developer] = (jobCodeGroup[result.projectName].nonBillable[result.developer] || 0) + result.totalWorkLogTime;
        }
    });

    this.projectListArray = Object.keys(jobCodeGroup).map(projectName => {
        const { jobCodes, billable, nonBillable } = jobCodeGroup[projectName];
        return {
            projectName,
            jobCodes: Object.keys(jobCodes).map(jobCodeName => ({
                jobCodeName,
                total: jobCodes[jobCodeName].total,
                developers: jobCodes[jobCodeName].developers,
                isBillable: jobCodes[jobCodeName].isBillable,
            })),
            billableDevelopersLogs: billable,
            nonBillableDevelopersLogs: nonBillable,
        };
    });
}


  getProjects(): void {
    this.subscription.add(
      this.projectService.getActiveProjects().subscribe((res: Project[]) => {
        this.projects = res;
        this.cdf.detectChanges();
      })
    );
  }

  getUsers() {
    this.subscription.add(
      this.employeeService.getEmployees().subscribe((res: Employee[]) => {
        this.developers = res;
      })
    );
  }

  getDataSource(data: WorklogListItem) {
    return new MatTableDataSource<TimeTrack>(data.timeTrack);
  }

  resetFilter(str: string) {
    switch (str) {
      case FilterName.TECHNOLOGY:
        this.filterParam.technologyId = null;
        break;
      case FilterName.PROJECT:
        this.filterParam.projectIds = null;
        break;
      case FilterName.EMPLOYEE:
        this.filterParam.uaaUserId = null;
        this.filterParam.employeeId = null;
        this.filterParam.uaaUserIds = null;
        break;
      case FilterName.ALL:
        this.filterParam.uaaUserId = null;
        this.filterParam.employeeId = null;
        this.filterParam.uaaUserIds = null;
        this.filterParam.technologyId = null;
        this.filterParam.projectId = null;
        this.filterParam.projectIds = null;
        this.monthOrWeek = "1";
        this.currentDate = new Date();
        this.setRangeDates();
        break;
      default:
        break;
    }
  }

  setFilterParam(str: string) {
    switch (str) {
      case FilterName.TECHNOLOGY:
        this.filterParam.employeeId = null;
        this.filterParam.uaaUserId = null;
        this.filterParam.uaaUserIds = null;
        this.filterParam.projectId = null;
        break;
      case FilterName.PROJECT:
        setTimeout(() => {
          this.filterParam.employeeId = null;
          this.filterParam.uaaUserIds = null;
          this.filterParam.uaaUserId = null;
          this.filterParam.technologyId = null;
          if (
            this.filterParam.projectIds.length === this.projects.length &&
            this.filterParam.projectIds.includes(0)
          ) {
            this.filterParam.projectIds = this.filterParam.projectIds.filter(
              (value) => value !== 0
            );
            this.isAllProjectSelected = false;
          } else if (
            this.filterParam.projectIds.length === this.projects.length &&
            !this.filterParam.projectIds.includes(0)
          ) {
            this.filterParam.projectIds.push(0);
            this.filterParam.projectIds = [...this.filterParam.projectIds];
            this.isAllProjectSelected = true;
          }
        }, 0);

        break;
      case FilterName.EMPLOYEE:
        setTimeout(() => {
          this.filterParam.technologyId = null;
          this.filterParam.projectId = null;
          this.filterParam.projectIds = null;
          if (
            this.filterParam.uaaUserIds.length === this.developers.length &&
            this.filterParam.uaaUserIds.includes(0)
          ) {
            this.filterParam.uaaUserIds = this.filterParam.uaaUserIds.filter(
              (value) => value !== 0
            );
            this.isAllEmployeeSelected = false;
          } else if (
            this.filterParam.uaaUserIds.length === this.developers.length &&
            !this.filterParam.uaaUserIds.includes(0)
          ) {
            this.filterParam.uaaUserIds.push(0);
            this.filterParam.uaaUserIds = [...this.filterParam.uaaUserIds];
            this.isAllEmployeeSelected = true;
          }
        }, 0);
        break;

      default:
        break;
    }
  }

  hasPermission(type: ReportType): boolean {
    const roles = this.sharedService.getRole();

    switch (type) {
      case ReportType.DEVELOPER_REPORT:
        return roles.includes(ROLES.SUPER_ADMIN);
      case ReportType.PROJECT_REPORT:
        return roles.includes(ROLES.SUPER_ADMIN) || roles.includes(ROLES.ADMIN);
    }
  }

  getReportDisabled(type: string): boolean {
    if (type === FilterName.EMPLOYEE) {
      return !this.filterParam?.uaaUserIds?.length;
    } else if (type === FilterName.PROJECT) {
      return !this.filterParam?.projectIds?.length;
    }
  }

  selectAllProjects() {
    this.isAllProjectSelected = !this.isAllProjectSelected;
    if (this.isAllProjectSelected) {
      this.filterParam.projectIds = [
        0,
        ...this.projects.map((user) => user.id),
      ];
    } else {
      this.filterParam.projectIds = [];
    }
  }

  selectAllEmployees() {
    this.isAllEmployeeSelected = !this.isAllEmployeeSelected;
    if (this.isAllEmployeeSelected) {
      this.filterParam.uaaUserIds = [
        0,
        ...this.developers.map((user) => user.id),
      ];
    } else {
      this.filterParam.uaaUserIds = [];
    }
  }

  calculateTotalTime(timeArray): number {
    let totalTime = 0;
    from(timeArray)
      .pipe(reduce((acc: any, value) => acc + value, 0))
      .subscribe((total) => {
        totalTime = total;
      });
    return totalTime;
  }

  getTotalHoursForUserAndDate(userLogs: WorklogListItem, date: Date): string {
    const formattedDate = moment(date).format("YYYY-MM-DD");
    const totalHours = this.dailyTotal[String(userLogs.user.id)][formattedDate];
    return totalHours ? this.convertSecondsToHour.transform(totalHours) : "";
  }

  async generateProjectExcel(): Promise<void> {
    this.pToDTransformation();
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Sheet1");

    const boldCenterStyle = {
      font: { bold: true, size: 12 },
      alignment: { vertical: "middle", horizontal: "center" },
    };

    const HeaderRow = worksheet.addRow([
      `Total ${this.monthOrWeek === "1" ? "Monthly" : "Weekly"} Hours (${moment(this.startingDate).format("DD-MM-YYYY")} to ${moment(this.endingDate).format("DD-MM-YYYY")})`,
    ]);

    worksheet.addRow([]);

    const addStyledRow = (data, style) => {
      const row = worksheet.addRow(data);
      row.eachCell((cell) => {
        cell.font = style.font;
        cell.alignment = style.alignment;
      });
      return row;
    };

    this.projectListArray.forEach((project) => {
      const columnLength = this.developersList[project.projectName].size;
      addStyledRow(["Project Name", project.projectName], boldCenterStyle);
      const headerRow = addStyledRow(["Developers"], boldCenterStyle);
      worksheet.mergeCells(
        headerRow.getCell(1).address,
        headerRow.getCell(columnLength + 2).address
      );
      addStyledRow(
        ["Jod Code", ...this.developersList[project.projectName], "Total"],
        boldCenterStyle
      );
      const jobGroupArr = project["jobCodes"];

      const processJobCodes = (isBillable, title, logKey) => {
        if (Object.keys(project[logKey]).length) {
          worksheet.addRow([title]);
        }

        jobGroupArr.forEach((job) => {
          if (job.isBillable === isBillable) {
            const usersLog = [job.jobCodeName];
            this.developersList[project.projectName].forEach((dev) => {
              usersLog.push(
                this.secondsToHoursMinutes(job.developers[dev] || 0)
              );
            });
            usersLog.push(this.secondsToHoursMinutes(job.total));
            worksheet.addRow(usersLog);
          }
        });

        if (Object.keys(project[logKey]).length > 0) {
          let totalSum = 0;
          const developersTotal = ["Total"];
          this.developersList[project.projectName].forEach((dev) => {
            const devTotal = project[logKey][dev] || 0;
            developersTotal.push(this.secondsToHoursMinutes(devTotal));
            totalSum += devTotal;
          });
          developersTotal.push(this.secondsToHoursMinutes(totalSum));
          addStyledRow(developersTotal, boldCenterStyle);
          worksheet.addRow([]);
        }
      };

      processJobCodes(true, "Billable", "billableDevelopersLogs");
      processJobCodes(false, "Non Billable", "nonBillableDevelopersLogs");

      for (let i = 1; i <= columnLength; i++) {
        worksheet.getColumn(i).width = 18;
      }

      worksheet.eachRow({ includeEmpty: true }, (row) => {
        row.eachCell({ includeEmpty: true }, (cell) => {
          cell.alignment = { vertical: "middle", horizontal: "center" };
          cell.border = {
            top: { style: "thin" },
            left: { style: "thin" },
            bottom: { style: "thin" },
            right: { style: "thin" },
          };
        });
      });
    });

    const maxColumn = worksheet.getColumn(worksheet.columnCount).letter;
    worksheet.mergeCells(`A1:${maxColumn}1`);
    HeaderRow.font = { bold: true, size: 12  };
    HeaderRow.alignment = { vertical: "middle", horizontal: "left", indent: 1 };
    HeaderRow.height = 25;

    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });
    FileSaver.saveAs(
      blob,
      `Project Report (${moment(this.startingDate).format("DD-MM-YYYY")} to ${moment(this.endingDate).format("DD-MM-YYYY")}).xlsx`
    );
  }

  exportProjectTimeSheet(): void {
    this.getWorkLogs();
  }

  exportDeveloperTimeSheet() {
    this.isExportingDeveloper = true;
    this.subscription.add(
      this.reportsService
        .getDevelopersHours(this.filterParam)
        .subscribe((res: WorklogListItem[]) => {
          res.forEach((item) => {
            item.timeTrack.sort((a, b) =>
              a.projectName.localeCompare(b.projectName)
            );
          });
          this.generateDeveloperExcel(res);
          this.isExportingDeveloper = false;
          this.cdf.detectChanges();
        })
    );
  }

  private secondsToHoursMinutes(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  }

  transformData(data: WorklogListItem[]): any[] {
    const transformed: any[] = [];

    data.forEach((item) => {
      const userProjects = {};

      if (item.user.name && item.timeTrack.length === 0) {
        transformed.push({
          Name: item.user.name,
          Project: "",
          "Job Code": "",
          Hours: "0h 0m",
        });
      }

      item.timeTrack.forEach((track) => {
        const projectJobCodeKey = `${track.projectName}-${track.jobCodeName}`;

        userProjects[projectJobCodeKey] = {
          Name: item.user.name,
          Project: track.projectName ?? "",
          "Job Code": track.jobCodeName ?? "",
          Hours: this.secondsToHoursMinutes(track.totalWorkLogTime) ?? "",
        };
      });

      for (const key in userProjects) {
        if (userProjects.hasOwnProperty(key)) {
          transformed.push(userProjects[key]);
        }
      }

      transformed.push({
        Name: "Total",
        Project: "",
        "Job Code": "",
        Hours: this.secondsToHoursMinutes(item.totalAllJobCodeLogTime),
      });

      transformed.push({
        Name: "",
        Project: "",
        "Job Code": "",
        Hours: "",
      });
    });
    return transformed;
  }

  async generateDeveloperExcel(data: WorklogListItem[]): Promise<void> {
    const transformedData = this.transformData(data);

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Sheet1");

    const totalRow = worksheet.addRow([
      `Total ${this.monthOrWeek === "1" ? "Monthly" : "Weekly"} Hours (${moment(this.startingDate).format("DD-MM-YYYY")} to ${moment(this.endingDate).format("DD-MM-YYYY")})`,
    ]);
    totalRow.font = { bold: true, size: 12 };
    totalRow.alignment = { vertical: "middle", horizontal: "center" };
    totalRow.height = 25;
    worksheet.mergeCells("A1:D1");

    const headerRow = worksheet.addRow([
      "Name",
      "Project",
      "Job Code",
      "Hours",
    ]);
    headerRow.eachCell((cell) => {
      cell.font = { bold: true, size: 12 };
      cell.alignment = { vertical: "middle", horizontal: "center" };
    });

    let prevName = "";
    let prevProject = "";
    let nameStartRowIndex = 0;
    let projectStartRowIndex = 0;

    transformedData.forEach((row, index) => {
      const rowIndex = index + 3; // +3 to account for header row and 1-based index
      const currentRow = worksheet.addRow([
        row.Name,
        row.Project,
        row["Job Code"],
        row.Hours,
      ]);

      currentRow.getCell(4).alignment = {
        vertical: "middle",
        horizontal: "center",
      };

      if (row.Name === "Total") {
        currentRow.font = { bold: true };
        worksheet.mergeCells(`A${rowIndex}:C${rowIndex}`);
        currentRow.alignment = { vertical: "middle", horizontal: "center" };
      }

      if (
        row.Name === "" &&
        row.Project === "" &&
        row["Job Code"] === "" &&
        row.Hours === ""
      ) {
        worksheet.mergeCells(`A${rowIndex}:D${rowIndex}`);
      }

      if (row.Name !== prevName && prevName !== "" && prevName !== "Total") {
        worksheet.mergeCells(`A${nameStartRowIndex}:A${rowIndex - 1}`);
        worksheet.getCell(`A${nameStartRowIndex}`).alignment = {
          vertical: "middle",
          horizontal: "center",
        };
      }

      if (row.Project !== prevProject && prevProject !== "") {
        worksheet.mergeCells(`B${projectStartRowIndex}:B${rowIndex - 1}`);
        worksheet.getCell(`B${projectStartRowIndex}`).alignment = {
          vertical: "middle",
          horizontal: "center",
        };
      }

      if (row.Name !== prevName) {
        prevName = row.Name;
        nameStartRowIndex = rowIndex;
      }

      if (row.Project !== prevProject) {
        prevProject = row.Project;
        projectStartRowIndex = rowIndex;
      }
    });

    if (prevName !== "") {
      worksheet.mergeCells(
        `A${nameStartRowIndex}:A${transformedData.length + 2}`
      );
      worksheet.getCell(`A${nameStartRowIndex}`).alignment = {
        vertical: "middle",
        horizontal: "center",
      };
    }

    if (prevProject !== "") {
      worksheet.mergeCells(
        `B${projectStartRowIndex}:B${transformedData.length + 2}`
      );
      worksheet.getCell(`B${projectStartRowIndex}`).alignment = {
        vertical: "middle",
        horizontal: "center",
      };
    }

    const allRows = worksheet.getRows(1, worksheet.rowCount);
    allRows.forEach((row) => {
      row.eachCell({ includeEmpty: false }, (cell) => {
        cell.border = {
          top: { style: "thin" },
          left: { style: "thin" },
          bottom: { style: "thin" },
          right: { style: "thin" },
        };
      });
    });

    worksheet.columns.forEach((column) => {
      let maxLength = 0;
      column.eachCell({ includeEmpty: true }, (cell) => {
        const columnLength = cell.value ? cell.value.toString().length : 10;
        if (columnLength > maxLength) {
          maxLength = columnLength;
        }
      });
      column.width = maxLength < 10 ? 10 : maxLength;
    });
    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });
    FileSaver.saveAs(
      blob,
      `Developers Report (${moment(this.startingDate).format("DD-MM-YYYY")} to ${moment(this.endingDate).format("DD-MM-YYYY")}).xlsx`
    );
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
