<h2 mat-dialog-title>Edit Profile</h2>
<hr class="mb-1">

<mat-dialog-content>
  <form #editProfileFrom="ngForm">
    <mat-card-content>
      <mat-form-field>
        <input matInput type="text" placeholder="First Name" name="first_name" aria-label="first-name"
               [(ngModel)]="data.firstName" #firstName="ngModel" required>
        <mat-error *ngIf="firstName.touched && firstName.invalid">
          <small class="mat-text-warn" *ngIf="firstName?.errors.required">First Name is required.</small>
        </mat-error>
      </mat-form-field>
      <mat-form-field>
        <input matInput type="text" placeholder="Last Name" name="last_name" aria-label="last-name"
               [(ngModel)]="data.lastName" #lastName="ngModel" required>
        <mat-error *ngIf="lastName.touched && lastName.invalid">
          <small class="mat-text-warn" *ngIf="lastName?.errors.required">Last Name is required.</small>
        </mat-error>
      </mat-form-field>
      <mat-form-field>
        <input matInput type="text" placeholder="Email" name="email" aria-label="email"
               pattern="^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$" [(ngModel)]="data.email" #email="ngModel" required>
        <mat-error *ngIf="email.touched && email.invalid">
          <small class="mat-text-warn" *ngIf="email?.errors.required">Email is required.</small>
          <small class="mat-text-warn" *ngIf="email?.errors.pattern">Invalid Email.</small>
        </mat-error>
      </mat-form-field>
      <mat-form-field>
        <input matInput [matDatepicker]="picker" type="text" autocomplete="off" placeholder="Choose a BirthDate"
          name="date" [(ngModel)]="data.birthDate" #birthDate="ngModel" required (click)="picker.open()"
          [max]="currentDate" aria-label="birth-date">
        <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
        <mat-datepicker #picker></mat-datepicker>
        <mat-error *ngIf="birthDate.touched && birthDate.invalid">
          <small class="mat-text-warn" *ngIf="birthDate?.errors.required">Date is required.</small>
        </mat-error>
      </mat-form-field>
      <mat-form-field>
        <input matInput [matDatepicker]="picker2" type="text" autocomplete="off" placeholder="Choose a JoiningDate"
          name="joinDate" [(ngModel)]="data.joinDate" #joiningDate="ngModel" required (click)="picker2.open()"
          [max]="currentDate" aria-label="joining-date">
        <mat-datepicker-toggle matSuffix [for]="picker2"></mat-datepicker-toggle>
        <mat-datepicker #picker2></mat-datepicker>
        <mat-error *ngIf="joiningDate.touched && joiningDate.invalid">
          <small class="mat-text-warn" *ngIf="joiningDate?.errors.required">Date is required.</small>
        </mat-error>
      </mat-form-field>
      <mat-form-field>
        <input matInput type="number" placeholder="Phone No." name="phone_no" pattern="^\+?\d{10,13}" aria-label="phone-number"
               [(ngModel)]="data.phoneNo" [max]="13" [min]="10" #phoneNo="ngModel" required>
        <mat-error *ngIf="phoneNo.touched && phoneNo.invalid">
          <small class="mat-text-warn" *ngIf="phoneNo?.errors.required">Phone Number is required.</small>
          <small class="mat-text-warn" *ngIf="phoneNo?.errors.pattern">Invalid Phone Number.</small>
          <small class="mat-text-warn" *ngIf="phoneNo?.errors.minlength">Invalid Phone Number.</small>
        </mat-error>
      </mat-form-field>
      <mat-form-field>
        <input matInput type="number" placeholder="SecondaryPhone No." name="secondary_phone_no" aria-label="secondary-phone-no"
               pattern="^\+?\d{10,13}" [(ngModel)]="data.secondaryPhoneNo" [max]="13" [min]="10" #secondaryPhoneNo="ngModel">
        <mat-error *ngIf="secondaryPhoneNo.touched && secondaryPhoneNo.invalid">
          <small class="mat-text-warn" *ngIf="secondaryPhoneNo?.errors.pattern">Invalid Phone Number.</small>
          <small class="mat-text-warn" *ngIf="secondaryPhoneNo?.errors.minlength">Invalid Phone Number.</small>
        </mat-error>
      </mat-form-field>
      <mat-form-field>
        <mat-select placeholder="Marital status" name="maritalStatus" [(ngModel)]="data.maritalStatus">
          <mat-option [value]="true">Married</mat-option>
          <mat-option [value]="false">Unmarried</mat-option>
        </mat-select>
      </mat-form-field>
      <mat-form-field *ngIf="data.maritalStatus">
        <input matInput [matDatepicker]="anniversaryDate" type="text" autocomplete="off" placeholder="Anniversary Date"
          name="anniversaryDate" [(ngModel)]="data.anniversaryDate" (click)="anniversaryDate.open()"
          [max]="currentDate" aria-label="anniversary-date">
        <mat-datepicker-toggle matSuffix [for]="anniversaryDate"></mat-datepicker-toggle>
        <mat-datepicker #anniversaryDate></mat-datepicker>
      </mat-form-field>

      <div *ngFor="let address of data.addresses; let i = index" class="address-block">
        <mat-expansion-panel class="mat-elevation-z0">
          <mat-expansion-panel-header>
            <mat-panel-title>
              {{address.type === ADDRESS_TYPE.HOME ? 'Permanent Address' : 'Current Address'}}
            </mat-panel-title>
            <mat-panel-description>
              Type your Address
              <mat-icon matTooltip="Remove" (click)="deleteAddress(i)">delete</mat-icon>
            </mat-panel-description>
          </mat-expansion-panel-header>
          <mat-form-field>
            <input matInput type="text" placeholder="AddressLine1" name="address_Line_1" aria-label="address-1"
                   [(ngModel)]="address.addressLine1" #addressLine1="ngModel" required>
            <mat-error *ngIf="addressLine1.touched && addressLine1.invalid">
              <small class="mat-text-warn" *ngIf="addressLine1?.errors.required">AddressLine1 is required.</small>
            </mat-error>
          </mat-form-field>
          <mat-form-field>
            <input matInput type="text" placeholder="AddressLine2" name="address_Line_2" aria-label="address-2"
                   [(ngModel)]="address.addressLine2">
          </mat-form-field>
          <mat-form-field>
            <input matInput type="text" placeholder="State" name="state" aria-label="state"
                   [(ngModel)]="address.state" #state="ngModel" required>
            <mat-error *ngIf="state.touched && state.invalid">
              <small class="mat-text-warn" *ngIf="state?.errors.required">State is required.</small>
            </mat-error>
          </mat-form-field>
          <mat-form-field>
            <input matInput type="text" placeholder="City" name="city" aria-label="city"
                   [(ngModel)]="address.city" #city="ngModel" required>
            <mat-error *ngIf="city.touched && city.invalid">
              <small class="mat-text-warn" *ngIf="city?.errors.required">City is required.</small>
            </mat-error>
          </mat-form-field>
          <mat-form-field>
            <input matInput type="text" placeholder="Country" name="country" aria-label="country"
                   [(ngModel)]="address.country" #country="ngModel" required>
            <mat-error *ngIf="country.touched && country.invalid">
              <small class="mat-text-warn" *ngIf="country?.errors.required">Country is required.</small>
            </mat-error>
          </mat-form-field>
          <mat-form-field>
            <mat-select placeholder="Address Type" name="type" [(ngModel)]="address.type" #addressType="ngModel" required>
              <mat-option [value]="ADDRESS_TYPE.HOME" [disabled]="addressTypeAvailable(ADDRESS_TYPE.HOME)">{{ADDRESS_TYPE.HOME | titlecase}}
              </mat-option>
              <mat-option [value]="ADDRESS_TYPE.CURRENT" [disabled]="addressTypeAvailable(ADDRESS_TYPE.CURRENT)">{{ADDRESS_TYPE.CURRENT | titlecase}}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="addressType.touched && addressType.invalid">
              <small class="mat-text-warn" *ngIf="addressType?.errors.required">Address type is required.</small>
            </mat-error>
          </mat-form-field>

        </mat-expansion-panel>
      </div>

      <div class="h-min-10"></div>

      <button mat-raised-button class="bt-sfl" (click)="addNewAddress()" *ngIf="data.addresses.length < 2">Add New
        Address</button>

    </mat-card-content>
  </form>
</mat-dialog-content>

<hr>
<mat-dialog-actions fxLayoutAlign="end">
  <button class="bt-flat" type="button" (click)="closeDialog()">Cancel</button>
  <button mat-raised-button class="bt-sfl" type="submit" [disabled]="editProfileFrom.form.invalid"
    (click)="saveEditProfile()">Save</button>
</mat-dialog-actions>
