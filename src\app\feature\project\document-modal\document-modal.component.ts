import { Component, OnInit } from '@angular/core';
import { ProjectDocument } from '../project.model';
import { MatDialogRef } from '@angular/material';


@Component({
  selector: 'sfl-document-modal',
  templateUrl: './document-modal.component.html'
})
export class DocumentModalComponent implements OnInit {
  document = new ProjectDocument();

  constructor(public dialogRef: MatDialogRef<DocumentModalComponent>) { }

  ngOnInit() { }

  saveDocument() {
    this.dialogRef.close(this.document);
  }

  closeModal(): void {
    this.dialogRef.close(false);
  }

}
