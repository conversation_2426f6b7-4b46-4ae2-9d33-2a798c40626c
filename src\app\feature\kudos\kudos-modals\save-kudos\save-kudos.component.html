<h2 mat-dialog-title><PERSON>dos</h2>
<hr>

<mat-dialog-content>
  <form #updateKudosForm="ngForm">
    <mat-card-content fxLayout="column">
      <div fxLayout="row" fxFlex="100" class="pt-10">
        <div fxLayout="row" class="award-image">
          <img *ngIf="kudos.imageUrl; else otherImage" [src]="kudos.imageUrl" class="circle-image" alt="kudos-img">
          <ng-template #otherImage>
            <div fxLayout="row" class="no-award-image" *ngIf="url.length === 0">
              <span>no image selected</span>
            </div>
            <div *ngIf="url.length !== 0" fxLayout="row" class="award-image">
              <img [src]="url" class="circle-image" alt="kudos-img">
            </div>
          </ng-template>
        </div>
      </div>
      <div fxLayout="row">
        <button mat-raised-button type="button" (click)="fileInput.click()"
          class="bt-sfl mr-10px add-award-button">Upload Kudos Image
          <input class="wd-fit-content" hidden #fileInput type="file" (change)="onSelectFile(fileInput.files)" />
        </button>
      </div>
      <mat-form-field class="pt-10">
        <input type="text" matInput placeholder="Award Name" name="name" aria-label="name" [(ngModel)]="kudos.name"
               (change)="kudos.name = kudos.name.trim()" #name="ngModel" [value]="kudos.name" required>
        <mat-error *ngIf="name.touched && name.invalid">
          <small class="mat-text-warn" *ngIf="name?.errors.required">Award name is required.</small>
        </mat-error>
      </mat-form-field>
      <mat-form-field>
        <textarea matInput placeholder="Description" name="description" aria-label="description" maxlength="255" minlength="1"
                  [value]="kudos.description" [(ngModel)]="kudos.description"
                  (change)="kudos.description = kudos.description.trim()" #description="ngModel" required></textarea>
        <mat-error *ngIf="description.touched && description.invalid">
          <small class="mat-text-warn" *ngIf="description?.errors.required">Description is required.</small>
        </mat-error>
      </mat-form-field>
    </mat-card-content>
  </form>
</mat-dialog-content>
<hr>

<mat-dialog-actions fxLayoutAlign="end">
  <button class="bt-flat" type="submit" (click)="closeDialog()">Cancel</button>
  <button mat-raised-button class="bt-sfl" type="submit" (click)="saveKudos(selectedFile)"
    [disabled]="updateKudosForm.form.invalid">Save</button>
</mat-dialog-actions>
