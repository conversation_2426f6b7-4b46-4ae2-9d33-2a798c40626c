import { Injectable } from '@angular/core';
import { ROLES, SharedService } from 'src/app/shared';
import { environment } from 'src/environments/environment';
import { AppConfig } from '../../app.config';
import { MENU_ITEM_TYPE, Menu } from './side-nav-bar.model';

@Injectable()
export class SideNavBarService {
  MENU_ITEMS: Menu[] = [
    {
      state: '',
      name: 'Administration',
      type: MENU_ITEM_TYPE.SUB,
      icon: 'account_balance',
      visibility: this.checkSuperAdmin() || this.checkAdmin() || this.checkLead(),
      children: [
        {
          state: AppConfig._REPORT_DASHBOARD,
          name: 'Report Dashboard',
          type: MENU_ITEM_TYPE.LINK,
          visibility: this.checkSuperAdmin() || this.checkAdmin()
        },
        {
          state: AppConfig._CLIENT,
          name: 'Client',
          type: MENU_ITEM_TYPE.LINK,
          visibility: true
        }
      ]
    },
    {
      state: '',
      name: 'Personal',
      type: MENU_ITEM_TYPE.SUB,
      icon: 'account_box',
      visibility: !this.checkClient(),
      children: [
        {
          state: AppConfig._DASHBOARD,
          name: 'My Dashboard',
          type: MENU_ITEM_TYPE.LINK,
          visibility: true
        },
        // {
        //   state: AppConfig._WORKLOG,
        //   name: "My Worklog",
        //   type: MENU_ITEM_TYPE.LINK,
        //   visibility: true,
        // },
        {
          state: AppConfig._TIME_SHEET,
          name: 'Time Sheet',
          type: MENU_ITEM_TYPE.LINK,
          visibility: true
        },
        {
          state: AppConfig._REPORTS,
          name: 'Reports',
          type: MENU_ITEM_TYPE.LINK,
          visibility: this.checkSuperAdmin() || this.checkAdmin()
        },
        {
          state: AppConfig._DAILY_ATTENDANCE,
          name: 'Attendance Logs',
          type: MENU_ITEM_TYPE.LINK,
          visibility: true
        }
      ]
    },
    {
      state: '',
      name: 'Management',
      type: MENU_ITEM_TYPE.SUB,
      icon: 'settings_applications',
      visibility: !this.checkClient(),
      children: [
        {
          state: AppConfig._EMPLOYEE_MANAGEMENT,
          name: 'Employee Management',
          type: MENU_ITEM_TYPE.LINK,
          visibility: this.checkSuperAdmin() || this.checkAdmin() || this.checkHR()
        },
        // {
        //   state: AppConfig._EMP_WORKLOG,
        //   name: "Employee Worklog",
        //   type: MENU_ITEM_TYPE.LINK,
        //   visibility: this.checkSuperAdmin() || this.checkAdmin() || this.checkLead() || this.checkHR(),
        // },
        {
          state: AppConfig._LEAVE,
          name: 'Leave',
          type: MENU_ITEM_TYPE.LINK,
          visibility: true
        },
        {
          state: AppConfig._PROJECT,
          name: 'Project',
          type: MENU_ITEM_TYPE.LINK,
          visibility: this.checkSuperAdmin() || this.checkAdmin() || this.checkLead()
        },
        {
          state: AppConfig._SCHEDULE,
          name: 'Schedule',
          type: MENU_ITEM_TYPE.LINK,
          visibility: this.checkSuperAdmin() || this.checkAdmin() || this.checkLead()
        },
        {
          state: AppConfig._EMPLOYEE_TECHNOLOGIES,
          name: 'Employee Technologies',
          type: MENU_ITEM_TYPE.LINK,
          visibility: this.checkSuperAdmin() || this.checkAdmin() || this.checkLead()
        },
        {
          state: AppConfig._JOB_CODE,
          name: 'Job Codes',
          type: MENU_ITEM_TYPE.LINK,
          visibility: this.checkSuperAdmin() || this.checkAdmin() || this.checkLead() || this.checkHR()
        }
      ]
    },
    // {
    //   state: "",
    //   name: "Time Tracker",
    //   type: MENU_ITEM_TYPE.SUB,
    //   icon: "access_time",
    //   visibility: true,
    //   children: [
    //     {
    //       state: AppConfig._TIME_SHEET,
    //       name: "Time Sheet",
    //       type: MENU_ITEM_TYPE.LINK,
    //       visibility: true,
    //     },
    //     {
    //       state: AppConfig._JOB_CODE,
    //       name: "Job Codes",
    //       type: MENU_ITEM_TYPE.LINK,
    //       visibility: this.checkSuperAdmin() || this.checkAdmin() || this.checkLead() || this.checkHR(),
    //     },
    //   ],
    // },
    {
      state: '',
      name: 'Other',
      type: MENU_ITEM_TYPE.SUB,
      icon: 'widgets',
      visibility: !this.checkClient(),
      children: [
        {
          state: AppConfig._FORMS,
          name: 'Forms',
          type: MENU_ITEM_TYPE.LINK,
          visibility: this.checkSuperAdmin() || this.checkAdmin() || this.checkLead() || this.checkHR()
        },
        {
          state: AppConfig._KUDOS,
          name: 'Kudos',
          type: MENU_ITEM_TYPE.LINK,
          visibility: true
        }
      ]
    },
    {
      state: environment.EARTH_WALKER_URL,
      name: 'Road Map',
      type: MENU_ITEM_TYPE.EXT,
      visibility: this.checkSuperAdmin() || this.checkAdmin(),
      icon: 'map'
    }
  ];

  constructor(private sharedService: SharedService) {}
  getAll(): Menu[] {
    return this.MENU_ITEMS;
  }

  checkSuperAdmin() {
    return this.sharedService.getRole().includes(ROLES.SUPER_ADMIN);
  }

  checkAdmin() {
    return this.sharedService.getRole().includes(ROLES.ADMIN);
  }

  checkLead() {
    return this.sharedService.getRole().includes(ROLES.LEAD);
  }

  checkHR() {
    return this.sharedService.getRole().includes(ROLES.HR);
  }

  checkClient() {
    return this.sharedService.getRole().includes(ROLES.CLIENT);
  }
}
