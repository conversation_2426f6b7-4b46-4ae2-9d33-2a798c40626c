import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { RouterModule } from '@angular/router';
import { SharedModule } from '../../shared/shared.module';
import { clientRoutes } from './client.route';
import { ClientComponent, ClientModalComponent } from './index';
import { ClientService } from './client.service';

@NgModule({
  imports: [
    RouterModule.forChild(clientRoutes),
    SharedModule
  ],
  declarations: [
    ClientComponent,
    ClientModalComponent
  ],
  providers: [
    ClientService
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})

export class ClientModule { }
