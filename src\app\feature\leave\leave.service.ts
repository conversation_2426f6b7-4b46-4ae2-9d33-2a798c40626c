import { Injectable } from '@angular/core';
import { AppConfig } from '../../app.config';
import { HttpClientService, createRequestOption, PageableQuery } from '../../shared';
import { Subject } from 'rxjs';
import { FilterLeaves, Leave, CalDates } from './leave.model';

@Injectable()
export class LeaveService {
  invokeEvent: Subject<any> = new Subject();

  constructor(private http: HttpClientService) {}

  callMethodOfWorklogComponent() {
    this.invokeEvent.next();
  }

  createLeave(leave: Leave) {
    return this.http.post(AppConfig.CREATE_LEAVE, leave);
  }

  updateLeaveStatus(leave: Leave) {
    return this.http.put(AppConfig.UPDATE_LEAVE_STATUS, leave);
  }

  getFilterLeaves(parameterValue: FilterLeaves, pageableObject: PageableQuery) {
    return this.http.post(AppConfig.FILTER_LEAVES, parameterValue, {
      params: createRequestOption(pageableObject)
    });
  }

  updateLeave(leave: Leave) {
    return this.http.put(AppConfig.UPDATE_LEAVE, leave);
  }

  calculateEndDateAndNoOfDays(parameterValue: CalDates) {
    return this.http.post(AppConfig.CAL_END_DATE_OR_NO_OF_DAYS, parameterValue);
  }

  getLeaveFile(parameterValue: FilterLeaves, uaaUserID: number) {
    return this.http.post(AppConfig.LEAVE_FILE + uaaUserID, parameterValue);
  }

  deleteLeave(id: number) {
    return this.http.delete(AppConfig.DELETE_LEAVE + '/' + id);
  }

}
