import { ChangeDetectorRef, Component } from '@angular/core';
import { MatDialogRef } from '@angular/material';
@Component({
  selector: 'app-change-password-dialog',
  templateUrl: './change-password-dialog.component.html',
  styleUrls: ['./change-password-dialog.component.css'],
})
export class ChangePasswordDialogComponent {
  newPassword: string;
  confirmPassword: string;
  password: string;
  noMatch = true;
  passwordsMatch = true;
  showNewPassword: boolean = false;
  showConfirmPassword: boolean = false;
  constructor(private dialogRef: MatDialogRef<ChangePasswordDialogComponent>, private readonly cdr: ChangeDetectorRef) {}

  closeDialog() {
    this.dialogRef.close(false);
  }
  savePasswordDetails() {
    if (!this.passwordsMatch) {
      return;
    }
    let passwordObj = {
      newPassword: this.password,
      confirmPassword: this.confirmPassword,
    };
    this.dialogRef.close(passwordObj);
  }
  public togglePasswordVisibility(item: string): void {
    if (item === 'newPassword') {
      this.showNewPassword = !this.showNewPassword;
    } else {
      this.showConfirmPassword = !this.showConfirmPassword;
    }
  }
  validatePasswords() {
    this.passwordsMatch = this.password === this.confirmPassword;
  }
}
