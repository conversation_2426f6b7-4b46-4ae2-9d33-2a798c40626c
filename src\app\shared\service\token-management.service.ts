import { Injectable, OnD<PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { LocalStorageService } from 'ngx-webstorage';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { catchError, switchMap } from 'rxjs/operators';
import { AppConfig } from '../../app.config';
import { HttpClientService } from './httpclient.service';
import { SnackBarService } from './snack-bar.service';

export interface TokenData {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type?: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

@Injectable({
  providedIn: 'root'
})
export class TokenManagementService implements OnDestroy {
  private refreshTimer: any;
  private tokenRefreshSubject = new BehaviorSubject<boolean>(false);
  private isRefreshing = false;
  private readonly REFRESH_BUFFER_TIME = 5 * 60 * 1000; // 5 minutes in milliseconds
  private readonly TOKEN_STORAGE_KEY = 'token';
  private readonly REFRESH_TOKEN_STORAGE_KEY = 'refreshToken';
  private readonly EXPIRES_IN_STORAGE_KEY = 'expiresIn';
  private readonly TOKEN_TIMESTAMP_KEY = 'tokenTimestamp';

  constructor(
    private localStorage: LocalStorageService,
    private http: HttpClientService,
    private router: Router,
    private snackBarService: SnackBarService
  ) {
    this.initializeVisibilityChangeListener();
    this.initializeBeforeUnloadListener();
  }

  /**
   * Store token data and start refresh timer
   */
  setTokenData(tokenData: TokenData): void {
    const currentTime = Date.now();
    
    this.localStorage.store(this.TOKEN_STORAGE_KEY, tokenData.access_token);
    this.localStorage.store(this.REFRESH_TOKEN_STORAGE_KEY, tokenData.refresh_token);
    this.localStorage.store(this.EXPIRES_IN_STORAGE_KEY, tokenData.expires_in);
    this.localStorage.store(this.TOKEN_TIMESTAMP_KEY, currentTime);

    this.startRefreshTimer(tokenData.expires_in);
  }

  /**
   * Get access token
   */
  getAccessToken(): string | null {
    return this.localStorage.retrieve(this.TOKEN_STORAGE_KEY);
  }

  /**
   * Get refresh token
   */
  getRefreshToken(): string | null {
    return this.localStorage.retrieve(this.REFRESH_TOKEN_STORAGE_KEY);
  }

  /**
   * Get expires in value
   */
  getExpiresIn(): number | null {
    return this.localStorage.retrieve(this.EXPIRES_IN_STORAGE_KEY);
  }

  /**
   * Get token timestamp
   */
  getTokenTimestamp(): number | null {
    return this.localStorage.retrieve(this.TOKEN_TIMESTAMP_KEY);
  }

  /**
   * Check if tokens exist and are valid
   */
  hasValidTokens(): boolean {
    const accessToken = this.getAccessToken();
    const refreshToken = this.getRefreshToken();
    const expiresIn = this.getExpiresIn();
    const timestamp = this.getTokenTimestamp();

    if (!accessToken || !refreshToken || !expiresIn || !timestamp) {
      return false;
    }

    const currentTime = Date.now();
    const tokenAge = currentTime - timestamp;
    const expirationTime = expiresIn * 1000; // Convert to milliseconds

    return tokenAge < expirationTime;
  }

  /**
   * Check if token is about to expire (within buffer time)
   */
  isTokenNearExpiry(): boolean {
    const expiresIn = this.getExpiresIn();
    const timestamp = this.getTokenTimestamp();

    if (!expiresIn || !timestamp) {
      return true;
    }

    const currentTime = Date.now();
    const tokenAge = currentTime - timestamp;
    const expirationTime = expiresIn * 1000;
    const timeUntilExpiry = expirationTime - tokenAge;

    return timeUntilExpiry <= this.REFRESH_BUFFER_TIME;
  }

  /**
   * Start the refresh timer
   */
  private startRefreshTimer(expiresIn: number): void {
    this.clearRefreshTimer();

    // Calculate when to refresh (5 minutes before expiry)
    const refreshTime = (expiresIn * 1000) - this.REFRESH_BUFFER_TIME;
    
    if (refreshTime > 0) {
      this.refreshTimer = setTimeout(() => {
        this.refreshTokens();
      }, refreshTime);
    } else {
      // Token expires very soon, refresh immediately
      this.refreshTokens();
    }
  }

  /**
   * Clear the refresh timer
   */
  private clearRefreshTimer(): void {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }
  }

  /**
   * Refresh tokens using the refresh token API
   */
  refreshTokens(): Observable<TokenData> {
    if (this.isRefreshing) {
      return this.tokenRefreshSubject.asObservable().pipe(
        switchMap(() => {
          const newToken = this.getAccessToken();
          if (newToken) {
            return new Observable<TokenData>(observer => {
              observer.next({
                access_token: newToken,
                refresh_token: this.getRefreshToken()!,
                expires_in: this.getExpiresIn()!
              });
              observer.complete();
            });
          } else {
            return throwError('Token refresh failed');
          }
        })
      );
    }

    this.isRefreshing = true;
    const refreshToken = this.getRefreshToken();
    const accessToken = this.getAccessToken();

    if (!refreshToken || !accessToken) {
      this.handleRefreshFailure('No refresh token available');
      return throwError('No refresh token available');
    }

    const refreshRequest: RefreshTokenRequest = {
      refreshToken: refreshToken
    };

    return this.http.post(AppConfig.AUTH_REFRESH_TOKEN, refreshRequest).pipe(
      switchMap((response: TokenData) => {
        this.handleRefreshSuccess(response);
        return new Observable<TokenData>(observer => {
          observer.next(response);
          observer.complete();
        });
      }),
      catchError((error) => {
        this.handleRefreshFailure('Token refresh failed', error);
        return throwError(error);
      })
    );
  }

  /**
   * Handle successful token refresh
   */
  private handleRefreshSuccess(tokenData: TokenData): void {
    this.setTokenData(tokenData);
    this.isRefreshing = false;
    this.tokenRefreshSubject.next(true);
  }

  /**
   * Handle token refresh failure
   */
  private handleRefreshFailure(message: string, error?: any): void {
    console.error('Token refresh failed:', message, error);
    this.isRefreshing = false;
    this.tokenRefreshSubject.next(false);
    this.clearAllTokens();
    this.redirectToLogin();
  }

  /**
   * Clear all tokens and redirect to login
   */
  clearAllTokens(): void {
    this.clearRefreshTimer();
    this.localStorage.clear(this.TOKEN_STORAGE_KEY);
    this.localStorage.clear(this.REFRESH_TOKEN_STORAGE_KEY);
    this.localStorage.clear(this.EXPIRES_IN_STORAGE_KEY);
    this.localStorage.clear(this.TOKEN_TIMESTAMP_KEY);
  }

  /**
   * Redirect to login page
   */
  private redirectToLogin(): void {
    this.router.navigate([AppConfig.LOGIN]).then(() => {
      window.location.reload();
    });
  }

  /**
   * Initialize on application startup
   */
  initializeOnStartup(): void {
    if (this.hasValidTokens()) {
      const expiresIn = this.getExpiresIn()!;
      const timestamp = this.getTokenTimestamp()!;
      const currentTime = Date.now();
      const tokenAge = currentTime - timestamp;
      const remainingTime = (expiresIn * 1000) - tokenAge;

      if (remainingTime > this.REFRESH_BUFFER_TIME) {
        // Start timer for remaining time minus buffer
        const timerDuration = remainingTime - this.REFRESH_BUFFER_TIME;
        this.refreshTimer = setTimeout(() => {
          this.refreshTokens();
        }, timerDuration);
      } else {
        // Token is near expiry or expired, refresh immediately
        this.refreshTokens().subscribe();
      }
    } else {
      this.clearAllTokens();
      this.redirectToLogin();
    }
  }

  /**
   * Handle page visibility change (tab active/inactive)
   */
  private initializeVisibilityChangeListener(): void {
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden && this.hasValidTokens()) {
        // Page became visible, check if we need to refresh
        if (this.isTokenNearExpiry()) {
          this.refreshTokens().subscribe();
        }
      }
    });
  }

  /**
   * Handle page unload (browser close, tab close, etc.)
   */
  private initializeBeforeUnloadListener(): void {
    window.addEventListener('beforeunload', () => {
      // Clear timer to prevent memory leaks
      this.clearRefreshTimer();
    });
  }

  ngOnDestroy(): void {
    this.clearRefreshTimer();
  }
}
