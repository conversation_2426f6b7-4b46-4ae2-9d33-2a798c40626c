<h2 class="form-header" mat-dialog-title>
  <span
    >{{ title }} for <strong>{{ jobCodeName }}</strong></span
  >
  <mat-icon matTooltip="Delete" *ngIf="isEditMode && !isViewMode" (click)="deleteLog(deleteParam.parentId, null, worklogData?.id)" class="modal-delete-btn cursor-pointer">delete</mat-icon>
</h2>
<hr class="mb-1" />
<mat-dialog-content>
  <div class="p-25" fxLayoutAlign="center center" fxFlex="100" *ngIf="false">
    <mat-progress-spinner color="warn" mode="indeterminate"></mat-progress-spinner>
  </div>
  <form #workLogForm="ngForm">
    <div class="top-action-bar">
      <div class="date-time-block">
        <mat-form-field class="total-time-field">
          <input
            [disabled]="true"
            matInput
            type="text"
            placeholder="Total time"
            name="totalTime"
            aria-label="totalTime"
            [(ngModel)]="worklogData.totalWorkLogTimeStr"
            #logTimeStr="ngModel"
            [autofocus]="true"
          />
        </mat-form-field>
        <span>
          &nbsp;for <strong>{{ data.date | date : "fullDate" }}</strong></span
        >
      </div>

      <span matTooltip="Add new form" *ngIf="!isViewMode" (click)="addNewForm()"><mat-icon class="add-new">add_box</mat-icon></span>
    </div>
    <mat-card-content>
      <ng-container *ngFor="let item of worklogData?.logs; let i = index">
        <div class="worklog-secondary-head">
          <h4 class="worklog-head">Worklog - {{ i + 1 }}</h4>

          <mat-icon
            *ngIf="worklogData?.logs?.length > 1 && !isViewMode"
            matTooltip="Delete Worklog - {{ i + 1 }}"
            (click)="deleteLog(deleteParam.childId, i, item?.id)"
            class="modal-delete-btn cursor-pointer"
            >delete</mat-icon
          >
        </div>
        <div class="time-input-wrapper">
          <div class="time-input-field">
            <mat-form-field>
              <input
                [disabled]="isViewMode"
                matInput
                type="text"
                placeholder="Hours"
                [name]="'timeLog - ' + i"
                [id]="'timeLog-' + i"
                [aria-label]="'timeLog - ' + i"
                [(ngModel)]="item.logTimeStr"
                #logTimeStr="ngModel"
                [autofocus]="true"
                (keyup)="setTotalTime()"
                (focus)="removeTimeFromTotal(item)"
                (blur)="addTimeInTotal(item)"
                required
              />
              <small *ngIf="checkIfFieldIsInvalid(logTimeStr, i)" class="validation-error mat-text-warn"> {{ checkIfFieldIsInvalid(logTimeStr, i) }} </small>
            </mat-form-field>
          </div>
          <div>e.g. (4h 30m)</div>
        </div>

        <mat-form-field>
          <input [disabled]="isViewMode" matInput type="text" placeholder="Ticket Link" [name]="'link - ' + i" [aria-label]="'link - ' + i" [(ngModel)]="item.ticketLink" #link="ngModel" />
        </mat-form-field>

        <mat-form-field>
          <textarea
            [disabled]="isViewMode"
            matInput
            rows="4"
            placeholder="Description"
            [name]="'description - ' + i"
            [aria-label]="'description - ' + i"
            [(ngModel)]="item.description"
            (change)="item.description = item.description.trim()"
            #description="ngModel"
          ></textarea>
        </mat-form-field>
        <hr />
      </ng-container>
    </mat-card-content>
  </form>
</mat-dialog-content>
<hr />
<mat-dialog-actions fxLayoutAlign="end">
  <button class="bt-flat" type="button" (click)="closeDialog()">Cancel</button>
  <button [disabled]="isSaving || disableSaveButton()" mat-raised-button class="bt-sfl" type="submit" *ngIf="!isViewMode" (click)="saveWorkLog()">
    Save <mat-progress-spinner *ngIf="isSaving" [diameter]="20" [strokeWidth]="3" mode="indeterminate" style="display: inline-block"></mat-progress-spinner>
  </button>
</mat-dialog-actions>
