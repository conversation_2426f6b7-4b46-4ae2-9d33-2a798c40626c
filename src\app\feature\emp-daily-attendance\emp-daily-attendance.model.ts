import {Sort, Pageable} from 'src/app/shared/utils/utils';



export class ClockInOutDTO {
  uaaUserId: number;
  clockIn: any;
  clockOut: any;
  id: number;
  userName: string;
}


export class ClockInOutPageable {
  content: ClockInOutDTO[];
  pageable: Pageable;
  totalElements: number;
  totalPages: number;
  last: boolean;
  numberOfElements: number;
  first: boolean;
  sort: Sort;
  size: number;
  number: number;
}


