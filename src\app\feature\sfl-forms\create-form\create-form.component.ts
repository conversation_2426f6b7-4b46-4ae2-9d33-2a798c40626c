import {Component, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {Subscription} from 'rxjs';
import {QUESTION_TYPE, FORM_GENERATOR, OptionsDTOS, QuestionsDTOS, SflFormsModel, SubQuestionsDTOS} from '../sfl-forms.model';
import {SharedService, Variable} from '../../../shared';
import {SflFormsService} from '../sfl-forms.service';
import {MatAutocompleteSelectedEvent} from '@angular/material';
import {ActivatedRoute, Router} from '@angular/router';
import {AppConfig} from '../../../app.config';
import {PreviewModalComponent} from '../preview-modal/preview-modal.component';
import {MatDialog} from '@angular/material/dialog';


@Component({
  selector: 'sfl-create-form',
  templateUrl: './create-form.component.html'
})
export class CreateFormComponent implements OnInit, On<PERSON><PERSON>roy {
  subscription: Subscription = new Subscription();
  sflFormsModel: SflFormsModel = new SflFormsModel();
  showLoader = true;
  changeQuestionAddBtn = false;
  isFormTypeExist = false;
  formURL = AppConfig._FORMS;
  questionTypes = QUESTION_TYPE;
  formGenerator = FORM_GENERATOR;
  formID: string;

  constructor(
    private matDialog: MatDialog,
    private sharedService: SharedService,
    private sflFormsService: SflFormsService,
    private activatedRoute: ActivatedRoute,
    private router: Router
  ) {
    this.subscription.add(this.activatedRoute.paramMap.subscribe((params) => {
      this.formID = params.get(Variable.ID);
    }));
  }

  ngOnInit() {
    this.sflFormsModel.version = 1;
    if (this.formID) {
      this.getCreatedFormsById();
    } else {
      this.showLoader = false;
    }
  }

  saveForm(): void {
    this.sflFormsModel.createdBy = Number(this.sharedService.getUaaUserId());
    this.subscription.add(
      this.sflFormsService.createForm(this.sflFormsModel).subscribe(() => {
        this.router.navigate([AppConfig._FORMS]).then();
      })
    );
  }

  getCreatedFormsById(): void {
    this.subscription.add(
      this.sflFormsService.getCreatedFormsById(this.formID).subscribe((res: SflFormsModel) => {
        this.sflFormsModel = res;
        this.sflFormsModel.version++;
        this.sflFormsModel.id = null;
        this.showLoader = false;
      })
    );
  }

  checkFormType(): void {
    if (this.sflFormsModel.formType) {
      this.subscription.add(
        this.sflFormsService.checkFormType(this.sflFormsModel.formType).subscribe((res: boolean) => {
          this.isFormTypeExist = res;
        })
      );
    } else {
      this.isFormTypeExist = false;
    }
  }

  removeGenerator(generator: string): void {
    const index = this.sflFormsModel.generators.indexOf(generator);
    if (index >= 0) {
      this.sflFormsModel.generators.splice(index, 1);
    }
  }

  selectedGenerator(event: MatAutocompleteSelectedEvent): void {
    this.sflFormsModel.generators.push(event.option.value);
  }

  addQuestion(type: string): void {
    const question = new QuestionsDTOS();
    question.type = type;
    if (question.type === QUESTION_TYPE.MULTI_RADIO) {
      question.subQuestionsDTOS.push(new SubQuestionsDTOS());
      question.optionsDTOS.push(new OptionsDTOS());
    }
    this.sflFormsModel.questionsDTOS.push(question);
  }

  removeQuestion(index: number): void {
    if (index >= 0) {
      this.sflFormsModel.questionsDTOS.splice(index, 1);
    }
  }

  moveQuestion(from: number, to: number) {
    this.sflFormsModel.questionsDTOS.splice(
      to,
      0,
      this.sflFormsModel.questionsDTOS.splice(from, 1)[0]
    );
  }

  addOption(index: number): void {
    this.sflFormsModel.questionsDTOS[index].optionsDTOS.push(new OptionsDTOS());
  }

  removeOption(questionIndex: number, index: number): void {
    if (index >= 0) {
      this.sflFormsModel.questionsDTOS[questionIndex].optionsDTOS.splice(index, 1);
    }
  }

  moveOption(questionIndex: number, from: number, to: number) {
    this.sflFormsModel.questionsDTOS[questionIndex].optionsDTOS.splice(
      to,
      0,
      this.sflFormsModel.questionsDTOS[questionIndex].optionsDTOS.splice(from, 1)[0]
    );
  }

  addSubQuestion(index: number): void {
    this.sflFormsModel.questionsDTOS[index].subQuestionsDTOS.push(new SubQuestionsDTOS());
  }

  removeSubQuestion(questionIndex: number, index: number): void {
    if (index >= 0) {
      this.sflFormsModel.questionsDTOS[questionIndex].subQuestionsDTOS.splice(index, 1);
    }
  }

  moveSubQuestion(questionIndex: number, from: number, to: number) {
    this.sflFormsModel.questionsDTOS[questionIndex].subQuestionsDTOS.splice(
      to,
      0,
      this.sflFormsModel.questionsDTOS[questionIndex].subQuestionsDTOS.splice(from, 1)[0]
    );
  }

  previewForm() {
    this.matDialog.open(PreviewModalComponent, {
      data: this.sflFormsModel
    });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
