import { HttpErrorResponse } from "@angular/common/http";
import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from "@angular/core";
import { MatDialog, MatPaginator, MatSort, MatTableDataSource } from "@angular/material";
import { Subscription } from "rxjs";
import { finalize } from "rxjs/operators";
import { PageableQuery, SnackBarService, Variable } from "src/app/shared";
import { AppConfig } from "../../app.config";
import { Client } from "../client/client.model";
import { ClientService } from "../client/client.service";
import { ProjectModalComponent } from "./project-modal/project-modal.component";
import { FilterProject, PROJECT_DISPLAY_COLUMNS, Project, ProjectPageable } from "./project.model";
import { ProjectService } from "./project.service";

@Component({
  selector: "sfl-project",
  templateUrl: "./project.component.html",
})
export class ProjectComponent implements OnI<PERSON>t, OnD<PERSON>roy {
  @ViewChild(MatSort, { static: false }) sort: MatSort;
  @ViewChild(MatPaginator, { static: false }) paginator: MatPaginator;

  showLoader = true;
  noDataFound = true;
  displayedColumns = PROJECT_DISPLAY_COLUMNS;
  dataSource = new MatTableDataSource<Project>([]);
  updateProject: Project = new Project();
  subscription: Subscription = new Subscription();
  clients: Client[] = [];
  projects: Project[] = [];
  projectPageable: ProjectPageable = new ProjectPageable();
  pageable: PageableQuery = new PageableQuery();
  filterProject: FilterProject = new FilterProject();
  projectDetailsURL = AppConfig._PROJECT_DETAILS;

  constructor(private matDialog: MatDialog, private projectService: ProjectService, private clientService: ClientService, private snackBarService: SnackBarService) {
    this.pageable.page = 0;
    this.pageable.size = 10;
    this.pageable.sort = Variable.NAME;
    this.pageable.direction = Variable.ASC;
    this.filterProject.projectStatus = Variable.DEFAULT_ACTIVE_FILTER;
  }

  ngOnInit() {
    this.onLoadData();
    this.subscription.add(
      this.projectService.invokeEvent.subscribe(() => {
        this.onLoadData();
      })
    );
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

  onLoadData() {
    this.getProjects();
    this.getClients();
    this.applyFilter();
  }

  saveProject(element: Project) {
    this.matDialog.open(ProjectModalComponent, {
      width: Variable.BOX_WIDTH_VALUE,
      data: element,
    });
  }

  updateProjectStatus(id, ob) {
    this.updateProject.id = id;
    this.updateProject.active = ob.checked;
    this.projectService.updateProjectStatus(this.updateProject).subscribe(
      () => {
        this.applyFilter(false);
      },
      (error: HttpErrorResponse) => {
        if (error.error && error.error.detail) {
          this.snackBarService.error(error.error.detail);
        }
        this.applyFilter(false);
      }
    );
  }

  getSorting(event) {
    this.pageable.sort = event.active;
    this.pageable.direction = event.direction;
    this.pageable.page = 0;
    this.applyFilter(false);
  }

  getPagination(event) {
    this.pageable.size = event.pageSize;
    this.pageable.page = event.pageIndex;
    this.applyFilter(false);
  }

  applyFilter(set_page_zero = true) {
    if (set_page_zero) {
      this.pageable.page = 0;
    }

    this.showLoader = true;
    this.noDataFound = true;
    const currentSort = this.pageable.sort;
    this.pageable.sort = this.pageable.sort + "," + this.pageable.direction;
    this.subscription.add(
      this.projectService
        .getFilterProject(this.filterProject, this.pageable)
        .pipe(
          finalize(() => {
            this.noDataFound = this.dataSource.data.length <= 0;
            this.showLoader = false;
          })
        )
        .subscribe((res: ProjectPageable) => {
          this.projectPageable = res;
          this.dataSource = new MatTableDataSource<Project>(this.projectPageable.content);
          this.pageable.size = this.projectPageable.pageable.pageSize;
          this.pageable.page = this.projectPageable.pageable.pageNumber;
          this.showLoader = false;
        })
    );
    this.pageable.sort = currentSort;
  }

  resetFilter(parameter: string) {
    this.filterProject[parameter] = undefined;
  }

  resetAllFilter() {
    this.filterProject = new FilterProject();
    this.filterProject.projectStatus = Variable.DEFAULT_ACTIVE_FILTER;
    this.applyFilter();
  }

  getProjects() {
    this.subscription.add(
      this.projectService.getProjects().subscribe((res: Project[]) => {
        this.projects = res;
      })
    );
  }

  getClients() {
    this.subscription.add(
      this.clientService.getClients().subscribe((res: Client[]) => {
        this.clients = res;
      })
    );
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
