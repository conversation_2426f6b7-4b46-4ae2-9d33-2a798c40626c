import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { RouterModule } from '@angular/router';
import { technologyRoutes } from './technology.route';
import { SharedModule } from 'src/app/shared/shared.module';
import { TechnologyComponent} from './technology.component';
import { TechnologyService } from './technology.service';
import { TechnologyModalComponent } from './technology-modal/technology-modal.component';

@NgModule({
  imports: [
    RouterModule.forChild(technologyRoutes),
    SharedModule
  ],
  declarations: [
    TechnologyComponent,
    TechnologyModalComponent
  ],
  providers: [
    TechnologyService
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class TechnologyModule { }
