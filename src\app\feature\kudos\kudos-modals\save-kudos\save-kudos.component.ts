import { Component, Inject, <PERSON><PERSON><PERSON><PERSON>, OnInit } from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material";
import { Subscription } from "rxjs";
import { Variable } from "../../../../shared";
import { KudosComponent } from "../../kudos.component";
import { Kudos } from "../../kudos.model";
import { KudosService } from "../../kudos.service";

@Component({
  selector: "sfl-update-award",
  templateUrl: "./save-kudos.component.html",
})
export class SaveKudosComponent implements OnInit, OnDestroy {
  url = "";
  selectedFile: File;
  subscription: Subscription = new Subscription();
  kudos: Kudos = new Kudos();

  constructor(@Inject(MAT_DIALOG_DATA) public data: Kudos, public dialogRef: MatDialogRef<KudosComponent>, private kudosService: KudosService) {}

  ngOnInit() {
    if (this.data) {
      this.kudos = this.data;
    }
  }

  onSelectFile(files: FileList) {
    this.selectedFile = files.item(0);
    const reader = new FileReader();
    reader.readAsDataURL(files[0]);
    reader.onload = (event) => {
      this.url = String(event.target["result"]);
    };
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  saveKudos(file: File) {
    this.kudos.file = file;
    if (this.data) {
      this.updateKudos();
    } else {
      this.addKudos();
    }
  }

  modelToFormData(): FormData {
    const formData: FormData = new FormData();
    formData.append(Variable.NAME, this.kudos.name);
    formData.append(Variable.FILE, this.kudos.file);
    formData.append(Variable.DESCRIPTION, this.kudos.description);
    return formData;
  }

  addKudos() {
    this.subscription.add(
      this.kudosService.addKudos(this.modelToFormData()).subscribe(() => {
        this.closeDialog();
      })
    );
  }

  updateKudos() {
    this.subscription.add(
      this.kudosService.updateKudos(this.modelToFormData(), this.kudos.id).subscribe(() => {
        this.dialogRef.close();
      })
    );
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
