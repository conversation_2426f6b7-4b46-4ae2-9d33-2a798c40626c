<h2 mat-dialog-title>Edit Employee Details</h2>
<hr class="mb-1" />

<mat-dialog-content>
  <form #editLevelFrom="ngForm">
    <mat-card-content>
      <mat-form-field>
        <input type="text" matInput placeholder="First Name" name="firstName" aria-label="name" [(ngModel)]="employeeDetails.firstName" #firstName="ngModel" required />
        <mat-error *ngIf="firstName.touched && firstName.invalid">
          <small class="mat-text-warn" *ngIf="name?.errors.required">First name is required.</small>
        </mat-error>
      </mat-form-field>
      <mat-form-field>
        <input type="text" matInput placeholder="Last Name" name="lastName" aria-label="name" [(ngModel)]="employeeDetails.lastName" #lastName="ngModel" required />
        <mat-error *ngIf="lastName.touched && lastName.invalid">
          <small class="mat-text-warn" *ngIf="name?.errors.required">Last name is required.</small>
        </mat-error>
      </mat-form-field>
      <mat-form-field>
        <input
          matInput
          type="email"
          placeholder="Email"
          name="email"
          aria-label="email"
          pattern="^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$"
          [(ngModel)]="employeeDetails.email"
          #email="ngModel"
          required
        />
        <mat-error *ngIf="email.touched && email.invalid">
          <small class="mat-text-warn" *ngIf="email?.errors.required">Email is required.</small>
          <small class="mat-text-warn" *ngIf="email?.errors.pattern">Invalid Email.</small>
        </mat-error>
      </mat-form-field>

      <mat-form-field>
        <input matInput placeholder="Phone Number" name="phoneNumber" aria-label="phone-number" [(ngModel)]="employeeDetails.phoneNo" #phoneNumber="ngModel" />
      </mat-form-field>

      <mat-form-field>
        <mat-select placeholder="Select Role" name="role" [(ngModel)]="employeeDetails.authorityId">
          <mat-option *ngFor="let role of roles" [value]="role.id">{{ role.name }} </mat-option>
        </mat-select>
      </mat-form-field>
      <mat-form-field>
        <mat-select placeholder="Select Employee Designation" name="designation" [(ngModel)]="employeeDetails.designationId" required>
          <mat-option *ngFor="let designation of designations" [value]="designation.id">{{ designation.name }} </mat-option>
        </mat-select>
      </mat-form-field>
    </mat-card-content>
  </form>
</mat-dialog-content>

<hr />
<mat-dialog-actions fxLayoutAlign="end">
  <button class="bt-flat" type="button" (click)="closeDialog()">Cancel</button>
  <button mat-raised-button class="bt-sfl" type="submit" [disabled]="editLevelFrom.form.invalid" (click)="saveEmployeeDetails()">Save</button>
</mat-dialog-actions>
