import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Inject } from "@angular/core";
import { MatDialogRef, MAT_DIALOG_DATA } from "@angular/material";
import { Router } from "@angular/router";
import { ProjectComponent } from "../project.component";
import { Project } from "../project.model";
import { Subscription } from "rxjs";
import { ProjectService } from "../project.service";
import { Client } from "../../client/client.model";
import { HttpErrorResponse } from "@angular/common/http";
import { SweetAlertService } from "src/app/shared/service/sweetalert.service";
import { ErrorMessage } from "../../../shared";
import { AppConfig } from "../../../app.config";
import { ClientService } from "../../client/client.service";

@Component({
  selector: "sfl-add-project",
  templateUrl: "./project-modal.component.html",
})
export class ProjectModalComponent implements <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> {
  subscription: Subscription = new Subscription();
  project: Project;
  clients: Client[] = [];
  isNewClient = false;
  isProjectEdit = false;
  currentDate = new Date();

  constructor(
    public dialogRef: MatDialogRef<ProjectComponent>,
    private router: Router,
    private projectService: ProjectService,
    private clientService: ClientService,
    private sweetService: SweetAlertService,
    @Inject(MAT_DIALOG_DATA) public data: Project
  ) {}

  ngOnInit() {
    this.project = new Project();
    this.project.clientDTO = new Client();
    this.getClients();
    if (this.data) {
      this.isProjectEdit = true;
      this.project = this.data;
    } else {
      this.isProjectEdit = false;
    }
  }

  addNewProject() {
    this.subscription.add(
      this.projectService.createProjectWithClient(this.project).subscribe(
        () => {
          this.successProject();
        },
        (error: HttpErrorResponse) => {
          this.errorProject(error);
        }
      )
    );
  }

  updateProject() {
    this.subscription.add(
      this.projectService.updateProject(this.project).subscribe(
        () => {
          this.successProject();
        },
        (error: HttpErrorResponse) => {
          this.errorProject(error);
        }
      )
    );
  }

  successProject() {
    this.projectService.callMethodOfWorklogComponent();
    this.closeDialog();
    this.router.navigate([AppConfig._PROJECT]).then();
  }

  errorProject(error: HttpErrorResponse) {
    if (error.error === ErrorMessage.ProjectExist) {
      this.sweetService.errorAlert(ErrorMessage.ProjectExistMsg).then();
    }
    this.closeDialog();
    this.router.navigate([AppConfig._PROJECT]).then();
  }

  getClients() {
    this.subscription.add(
      this.clientService.getClients().subscribe((res: Client[]) => {
        this.clients = res;
      })
    );
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  saveProject() {
    if (this.isProjectEdit) {
      this.updateProject();
    } else {
      this.addNewProject();
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
