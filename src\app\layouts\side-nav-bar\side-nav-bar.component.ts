import { MediaMatcher } from "@angular/cdk/layout";
import { ChangeDetectorRef, Component, ElementRef, On<PERSON><PERSON>roy, OnInit, ViewChild } from "@angular/core";
import { NavigationEnd, Router } from "@angular/router";
import { Subscription } from "rxjs";
import "rxjs/add/operator/filter";
import { filter } from "rxjs/operators";
import { MENU_ITEM_TYPE } from "./side-nav-bar.model";
import { SideNavBarService } from "./side-nav-bar.service";

@Component({
  selector: "sfl-side-nav-bar-layout",
  templateUrl: "./side-nav-bar.component.html",
})
export class SideNavBarComponent implements OnInit, OnD<PERSON>roy {
  @ViewChild("sideMenu", { static: false }) sideMenu;

  SMALL_WIDTH_BREAKPOINT = 960;
  private subscription: Subscription;
  url: string;
  readonly MENU_TYPE = MENU_ITEM_TYPE;
  options = {
    collapsed: false,
    compact: false,
    boxed: false,
    dark: false,
    dir: "ltr",
  };

  mediaMatcher: MediaQueryList;
  private readonly _mobileQueryListener: () => void;

  constructor(private _element: ElementRef, private router: Router, public sideNavBarService: SideNavBarService, media: MediaMatcher, changeDetectorRef: ChangeDetectorRef) {
    this.mediaMatcher = media.matchMedia(`(max-width: ${this.SMALL_WIDTH_BREAKPOINT}px)`);
    this._mobileQueryListener = () => changeDetectorRef.detectChanges();
    this.mediaMatcher.addListener(this._mobileQueryListener);
  }

  ngOnInit(): void {
    this.url = this.router.url;
    this.subscription = this.router.events.pipe(filter((event) => event instanceof NavigationEnd)).subscribe((event: NavigationEnd) => {
      document.querySelector(".app-inner > .mat-drawer-content > div").scrollTop = 0;
      this.url = event.url;
      this.runOnRouteChange();
    });
  }

  runOnRouteChange(): void {
    if (this.isOver()) {
      this.sideMenu.close();
    }
  }

  isOver(): boolean {
    return this.mediaMatcher.matches;
  }

  toggleSideBar() {
    // sideMenu.toggle();
  }

  trackByFn(index, item) {
    return index;
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
