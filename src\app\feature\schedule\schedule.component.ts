import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from "@angular/core";
import { <PERSON><PERSON><PERSON><PERSON>, MatDialogConfig } from "@angular/material";
import * as _ from "lodash";
import * as moment from "moment";
import { Subscription } from "rxjs";
import { finalize } from "rxjs/operators";
import { Events, Item, Period, Section, Text } from "src/app/shared/modules/ngx-time-scheduler/ngx-time-scheduler.model";
import { NgxTimeSchedulerService } from "src/app/shared/modules/ngx-time-scheduler/ngx-time-scheduler.service";
import { DateUtils, SharedService, Variable } from "../../shared";
import { Client } from "../client/client.model";
import { ClientService } from "../client/client.service";
import { Employee } from "../employee-management/employee-management.model";
import { EmployeeManagementService } from "../employee-management/employee-management.service";
import { Project } from "../project/project.model";
import { ProjectService } from "../project/project.service";
import { Technology } from "../technology/technology.model";
import { TechnologyService } from "../technology/technology.service";
import { ScheduleModalComponent } from "./schedule-modal/schedule-modal.component";
import { SCEDULE_DATE_WISE_FILTER_LIST, ScheduleFilters, ScheduleModal, ScheduleModel, WORK_LOAD_FILTER_LIST } from "./schedule.model";
import { ScheduleService } from "./schedule.service";

@Component({
  selector: "sfl-schedule",
  templateUrl: "./schedule.component.html",
})
export class ScheduleComponent implements OnInit, OnDestroy {
  originalSchedules: ScheduleModel[] = [];
  allocatedSchedules: ScheduleModel[] = [];
  exportData: ScheduleModel[] = [];
  projects: Project[] = [];
  users: Employee[] = [];
  clients: Client[] = [];
  periods: Period[] = [];
  sections: Section[] = [];
  items: Item[] = [];
  events: Events = new Events();
  text: Text = new Text();
  isStartEndDate = false;
  startDate!: string;
  endDate!: string;
  subscription: Subscription = new Subscription();
  filters: ScheduleFilters = new ScheduleFilters();
  editSchedule = new ScheduleModel();
  addEmployeeSchedule = new ScheduleModel();
  addText = Variable.ADD;
  freeSearch = Variable.FREE_SEARCH.replace(" ", "");
  showLoader = true;
  isExporting = false;
  currentPeriod: Period;
  technologies: Technology[] = [];
  dateWiseFilter: string[] = SCEDULE_DATE_WISE_FILTER_LIST;
  workLoadFilter: string[] = WORK_LOAD_FILTER_LIST;

  constructor(
    private scheduleService: ScheduleService,
    public dialog: MatDialog,
    private projectService: ProjectService,
    private clientService: ClientService,
    private employeeService: EmployeeManagementService,
    private readonly technologyService: TechnologyService,
    private readonly timeSchedulerService: NgxTimeSchedulerService
  ) {}

  ngOnInit(): void {
    this.getProjects();
    this.getClients();
    this.getUsers();
    this.startDate = this.convertStartDateToString(new Date());
    this.endDate = this.convertEndDateToString(moment(moment(new Date(this.startDate)).format("YYYY-MM-DD"), "YYYY-MM-DD").businessAdd(5)["_d"]);
    this.text.SectionTitle = Variable.EMPLOYEE_NAME;
    this.periods = this.getPeriods();
    this.currentPeriod = this.periods[0];
    this.events.SectionClickEvent = this.addTask();
    this.events.PeriodChange = (start, end, period) => {
      this.startDate = this.convertStartDateToString(start["_d"]);
      this.endDate = this.convertEndDateToString(end["_d"]);
      if (period) {
        this.currentPeriod = period;
      }
      if (this.filters?.workload) {
        this.getSchedules();
      } else {
        this.showLoader = true;
        this.filterOutSchedule(this.originalSchedules);
        this.timeSchedulerService.refresh();
        setTimeout(() => {
          this.showLoader = false;
        }, 0);
      }
    };
    this.events.ItemClicked = (item: Item) => {
      this.editSchedule = this.searchSchedule(item.id);
      this.openScheduleDialog(Variable.EDIT, false);
    };
    this.getTechnologies();
    this.getSchedules();
  }

  getStartingDate() {
    return moment(new Date(this.startDate));
  }

  addTask() {
    return (section: Section) => {
      this.addEmployeeSchedule.uaaUserId = section.id;
      this.addEmployeeSchedule.fromDate = moment().startOf("date").toDate();
      this.addEmployeeSchedule.total_hours = 8;
      this.openScheduleDialog(Variable.ADD, true);
    };
  }

  openScheduleDialog(action: string, addSchedule: boolean): void {
    const scheduleModal = new ScheduleModal();
    scheduleModal.action = action;
    if (action === Variable.ADD) {
      if (addSchedule) {
        scheduleModal.schedule = this.addEmployeeSchedule;
      } else {
        scheduleModal.schedule = new ScheduleModel();
      }
    } else {
      scheduleModal.schedule = this.editSchedule;
    }

    const matDialogConfig = new MatDialogConfig();
    matDialogConfig.data = scheduleModal;
    matDialogConfig.width = Variable.BOX_WIDTH_VALUE;
    const dialogRef = this.dialog.open(ScheduleModalComponent, matDialogConfig);

    dialogRef.afterClosed().subscribe((isNotCancelled) => {
      if (isNotCancelled) {
        this.getSchedules();
      } else {
        this.addEmployeeSchedule = new ScheduleModel();
        this.editSchedule = new ScheduleModel();
      }
    });
  }

  exportSchedule(scheduler: any): void {
    this.isExporting = true;
    this.exportData = [];
    this.filterSchedule();
  }

  private dataIsValidForView(dataStartDate: Date, dataEndDate: Date): boolean {
    const startDate = new Date(this.startDate);
    const endDate = new Date(this.endDate);
    return (
      (startDate <= dataStartDate && dataStartDate <= endDate) ||
      (startDate <= dataEndDate && dataEndDate <= endDate) ||
      (dataStartDate <= startDate && startDate <= dataEndDate) ||
      (dataStartDate <= endDate && endDate <= dataEndDate)
    );
  }

  private convertStartDateToString(date: Date): string {
    return moment(date).utcOffset(Variable.IST_OFFSET).set({ hour: 0, minute: 0, second: 0 }).format();
  }

  private convertEndDateToString(date: Date): string {
    return moment(date).utcOffset(Variable.IST_OFFSET).set({ hour: 23, minute: 59, second: 59 }).format();
  }

  private filterSchedule(): void {
    const startDate = new Date(this.startDate);
    const endDate = new Date(this.endDate);
    this.allocatedSchedules.forEach((schedule) => {
      const dataStartDate = new Date(schedule.fromDate);
      const dataEndDate = new Date(schedule.toDate);
      if (this.dataIsValidForView(dataStartDate, dataEndDate)) {
        this.exportData.push({ ...schedule });
      }
    });
    if (this.exportData.length) {
      this.exportData.forEach((schedule) => {
        const dataStartDate = new Date(schedule.fromDate);
        const dataEndDate = new Date(schedule.toDate);
        if (startDate > dataStartDate) {
          schedule.fromDate = this.convertStartDateToString(startDate);
        }
        if (endDate < dataEndDate) {
          schedule.toDate = this.convertEndDateToString(endDate);
        }
      });

      this.exportData.sort((a, b) => a.uaaUserName.localeCompare(b.uaaUserName));
    }
    this.getScheduleExcel();
  }

  getScheduleExcel(): void {
    import("xlsx").then((xlsx) => {
      const worksheet = xlsx.utils.aoa_to_sheet(this.getScheduleForExport(this.exportData));
      worksheet["A1"].s = { font: { sz: 24, bold: true }, alignment: { horizontal: "center" } };
      worksheet["!merges"] = [{ s: { r: 0, c: 0 }, e: { r: 0, c: 6 } }];
      const colWidth = 25;
      const columnCount = this.getColumnCount(worksheet);
      worksheet["!cols"] = Array(columnCount).fill({ wch: colWidth });
      worksheet["!name"] = "ScheduleReport";
      const workbook = { Sheets: { ScheduleReport: worksheet }, SheetNames: ["ScheduleReport"] };
      const excelBuffer: any = xlsx.write(workbook, { bookType: "xlsx", type: "array" });
      SharedService.saveAsExcelFile(excelBuffer, this.getFileName);
      this.isExporting = false;
    });
  }

  private getColumnCount(worksheet: any): number {
    let maxCol = 0;
    for (const cell in worksheet) {
      if (cell.charAt(0) === "!") continue;
      const col = cell.charCodeAt(0) - "A".charCodeAt(0);
      if (col > maxCol) maxCol = col;
    }
    return maxCol + 1;
  }

  get getFileName(): string {
    const filterStartDate = this.getDDMMYYYYFormat(new Date(this.startDate));
    const filterEndDate = this.getDDMMYYYYFormat(new Date(this.endDate));
    return `ScheduleReport_${filterStartDate}To${filterEndDate}`;
  }

  getDDMMYYYYFormat(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const day = date.getDate().toString().padStart(2, "0");
    return `${day}${month}${year}`;
  }

  private transformTechnologyArray(arr: Technology[]): string {
    let technologies: string[] = [];
    if (arr.length) {
      technologies = arr.map((technology) => technology.name);
    }
    return technologies.join(", ");
  }

  private convertDateToDD_MM_YYYY(date: Date): string {
    const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
    const pad = (value: number) => value.toString().padStart(2, "0");
    const day = pad(date.getDate());
    const month = months[date.getMonth()];
    const year = date.getFullYear();

    return `${day}-${month}-${year}`;
  }

  private getScheduleForExport(schedules: ScheduleModel[]) {
    let aoaData: Array<string[]> = [];
    const header = [`Schedule Report [${this.convertDateToDD_MM_YYYY(new Date(this.startDate))} To ${this.convertDateToDD_MM_YYYY(new Date(this.endDate))}]`];
    const Title = ["Employee Name", "Technology", "Per Day", "From Date", "To Date", "Project", "Comments"];
    aoaData = [header, Title, []];
    schedules.forEach((schedule) => {
      const newDataArray = [
        schedule.uaaUserName,
        this.transformTechnologyArray(schedule.technologyDTOList),
        String(schedule.per_day_hours),
        DateUtils.convertDateDdmmyy(schedule.fromDate),
        DateUtils.convertDateDdmmyy(schedule.toDate),
        schedule.projectName,
        schedule.note,
      ];
      aoaData.push(newDataArray);
    });
    return aoaData;
  }

  getPeriods(): Period[] {
    return [
      {
        name: Variable.SEVEN_DAYS,
        timeFramePeriod: 1440,
        timeFrameOverall: 1440 * 7,
        timeFrameHeaders: [Variable.MMM_YYYY, Variable.DD_DDD],
        classes: "",
      },
      {
        name: Variable.FOURTEEN_DAYS,
        timeFramePeriod: 1440,
        timeFrameOverall: 1440 * 14,
        timeFrameHeaders: [Variable.MMM_YYYY, Variable.DD_DDD],
        classes: "",
      },
      {
        name: Variable.TWENTY_EIGHT_DAYS,
        timeFramePeriod: 1440,
        timeFrameOverall: 1440 * 28,
        timeFrameHeaders: [Variable.MMM_YYYY, Variable.DD],
        classes: "",
      },
    ];
  }

  getSchedules() {
    this.showLoader = true;
    if (this.filters?.workload) {
      this.filters.startDate = moment(this.startDate).format(Variable.YYYY_MM_DD);
      this.filters.endDate = moment(this.endDate).format(Variable.YYYY_MM_DD);
    }
    this.sections = [];
    if (this.filters.projectName || this.filters.uaaUserId) {
      this.sections = [];
    }

    this.subscription.add(
      this.scheduleService
        .getSchedules(this.filters)
        .pipe(
          finalize(() => {
            this.showLoader = false;
          })
        )
        .subscribe((res: ScheduleModel[]) => {
          this.originalSchedules = res;
          this.filterOutSchedule(this.originalSchedules);
        })
    );
  }

  private filterOutSchedule(originalArray: ScheduleModel[]): void {
    this.allocatedSchedules = [];
    originalArray.forEach((schedule) => {
      const dataStartDate = new Date(schedule.fromDate);
      const dataEndDate = new Date(schedule.toDate);
      if (this.dataIsValidForView(dataStartDate, dataEndDate)) {
        this.allocatedSchedules.push({ ...schedule });
      }
    });
    this.transformScheduleForScheduler();
  }

  private transformScheduleForScheduler(): void {
    this.sections = [];
    this.items = new Array<Item>();
    this.allocatedSchedules.forEach((data) => {
      const item = new Item();
      item.id = data.id;
      item.sectionID = data.uaaUserId;
      item.start = moment(data.fromDate).utcOffset(Variable.IST_OFFSET);
      item.end = moment(data.toDate).utcOffset(Variable.IST_OFFSET);
      item.classes = "time-sch-item-" + data.per_day_hours;
      item.name = `${data.projectName} (${data.per_day_hours}h)`;
      this.items.push(item);
      const section = new Section();
      section.id = data.uaaUserId;
      section.name = `${data?.uaaUserName ?? ""}-(${data?.designationName ?? ""})-${data?.technologyDTOList.map((t) => t.name).join(", ") ?? ""}`;
      const foundIndex = _.findIndex(this.sections, (o) => {
        return o.id === data.uaaUserId;
      });
      if (foundIndex === -1) {
        this.sections.push(section);
      }
    });
    this.sections = _.sortBy(this.sections, [(section: Section) => section.name]);
  }

  getUsers() {
    this.subscription.add(
      this.employeeService.getEmployees().subscribe((res: Employee[]) => {
        this.users = res;
      })
    );
  }

  getProjects() {
    this.subscription.add(
      this.projectService.getActiveProjects().subscribe((res: Project[]) => {
        this.projects = res;
      })
    );
  }

  getClients() {
    this.subscription.add(
      this.clientService.getClients().subscribe((res: Client[]) => {
        this.clients = res;
      })
    );
  }

  searchSchedule(scheduleId: any) {
    return this.allocatedSchedules.find((item) => {
      return item.id === Number(scheduleId);
    });
  }

  resetFilter(parameter: string) {
    this.filters[parameter] = undefined;
    if (parameter === "filterBy") {
      this.isStartEndDate = true;
    } else {
      this.isStartEndDate = false;
    }
  }

  // get all Technology list
  getTechnologies() {
    this.subscription.add(
      this.technologyService.getTechnologies().subscribe((res: Technology[]) => {
        this.technologies = res;
      })
    );
  }

  resetAllFilter() {
    this.filters = new ScheduleFilters();
    this.getSchedules();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
