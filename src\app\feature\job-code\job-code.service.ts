import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { AppConfig } from "src/app/app.config";
import { HttpClientService } from "src/app/shared";
import { JobCode } from "./job-code";
import { PinDto, TimeSheetResponse } from "../timesheet/timesheet.model";

@Injectable({
  providedIn: "root",
})
export class JobCodeService {
  constructor(private http: HttpClientService) {}

  saveJobCode(jobCode: JobCode): Observable<TimeSheetResponse> {
    return this.http.post(AppConfig.JOB_CODE, jobCode);
  }
  updateJobCode(jobCode: JobCode) {
    return this.http.put(AppConfig.JOB_CODE, jobCode);
  }

  getJobCodes() {
    return this.http.get(AppConfig.JOB_CODE);
  }

  updateStatus(id: number, status: boolean) {
    const url = `${AppConfig.JOB_CODE}/${id}?isActive=${status}`;
    return this.http.patch(url, null);
  }

  pinUnpinJobCode(pinDto: PinDto) {
    return this.http.post(`${AppConfig.JOB_CODE}/pin`, pinDto);
  }

  getUserPinnedJobCode(id: number) {
    return this.http.get(`${AppConfig.JOB_CODE}/pin?userId=${id}`);
  }
}
