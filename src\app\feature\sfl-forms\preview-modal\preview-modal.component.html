<h2 mat-dialog-title>
  {{ data?.formName }}
  <mat-chip-list class="d-inline-block">
    <mat-chip>{{ data?.formType }}</mat-chip>
  </mat-chip-list>
</h2>
<p>{{data?.description}}</p>
<hr class="mb-1">

<mat-dialog-content class="mb-1">
  <mat-card-content fxLayout="column">
    <table class="w-100">
      <tr>
        <td>
          <mat-form-field>
            <mat-chip-list #chipList>
              <mat-chip *ngFor="let generator of data.generators">
                {{generator | replaceString: '-' | titlecase}}
              </mat-chip>
              <input placeholder="Form Generators" aria-label="generator" [matChipInputFor]="chipList" [readOnly]="true">
            </mat-chip-list>
          </mat-form-field>
        </td>
      </tr>

      <tr>
        <td class="pb-1">
          <span class="question-required-notice-star"></span>
          <span>Required</span>
        </td>
      </tr>

      <tr *ngFor="let question of data.questionsDTOS; let i=index">
        <td>
          <div class="questions-text">
            <span class="question-number">{{i+1}}. </span>
            <span [ngClass]="question.required? 'question-required-notice-star': ''">{{question.text}}</span>
            <div class="question-ml">
              <span class="question-hint">{{question.hint}}</span>
            </div>
          </div>
          <div class="question-ml" *ngIf="question.type === questionType.TEXT">
            <mat-form-field>
              <input matInput aria-label="a-text" placeholder="Enter your answer">
            </mat-form-field>
          </div>
          <div class="question-ml" *ngIf="question.type === questionType.TEXT_AREA">
            <mat-form-field>
              <textarea matInput aria-label="a-textarea" placeholder="Enter your answer"></textarea>
            </mat-form-field>
          </div>
          <div class="question-ml" *ngIf="question.type === questionType.RADIO">
            <mat-radio-group class="d-inline-block">
              <mat-radio-button class="mr-10px" color="warn" *ngFor="let option of question.optionsDTOS" [value]="option.optionName">
                {{option.optionName}}
              </mat-radio-button>
            </mat-radio-group>
          </div>
          <div class="question-ml" *ngIf="question.type === questionType.MULTI_RADIO">
            <table>
              <thead>
                <tr>
                  <th></th>
                  <th class="px-1" *ngFor="let option of question.optionsDTOS">
                    <span>{{option.optionName}}</span>
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let subQuestion of question.subQuestionsDTOS">
                  <td class="questions-text"><span>{{subQuestion.text}}</span></td>
                  <td class="text-center" *ngFor="let option of question.optionsDTOS">
                    <mat-radio-group>
                      <mat-radio-button color="warn" [value]="option.optionName"></mat-radio-button>
                    </mat-radio-group>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </td>
      </tr>
    </table>
  </mat-card-content>
</mat-dialog-content>
<hr>

<mat-dialog-actions fxLayoutAlign="end">
  <button mat-raised-button class="bt-sfl" type="submit" (click)="closeDialog()">Close</button>
</mat-dialog-actions>
