import { Injectable } from '@angular/core';
import { AppConfig } from '../../app.config';
import { HttpClientService, createRequestOption, PageableQuery } from '../../shared';
import { Technology } from './technology.model';
import { Subject } from 'rxjs';


@Injectable({
  providedIn: 'root'
})
export class TechnologyService {

  invokeEvent: Subject<any> = new Subject();

  constructor(private http: HttpClientService) { }

  callMethodOfWorklogComponent() {
    this.invokeEvent.next();
  }

  getTechnologiesPageable(pageableObject: PageableQuery) {
    return this.http.get(AppConfig.TECHNOLOGY, {
      params: createRequestOption(pageableObject)
    });
  }

  getTechnologies() {
    return this.http.get(AppConfig.ALL_TECHNOLOGY);
  }

  createTechnology(technology: Technology) {
    return this.http.post(AppConfig.TECHNOLOGY, technology);
  }

  updateTechnology(technology: Technology) {
    return this.http.put(AppConfig.TECHNOLOGY, technology);
  }

  deleteTechnology(technology: Technology) {
    return this.http.delete(AppConfig.TECHNOLOGY + '/' + technology.id);
  }

}
