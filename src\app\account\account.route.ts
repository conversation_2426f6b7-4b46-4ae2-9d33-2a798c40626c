import { Routes } from '@angular/router';
import { LoginComponent, ForgotPasswordComponent } from '.';
import {ChangePasswordComponent} from './change-password/change-password.component';
import {AuthGuardService} from '../shared/service/auth-guard.service';


export const accountRoutes: Routes = [
  {
      path: '',
      component: LoginComponent
  },
  {
      path: 'login',
      component: LoginComponent
  },
  {
      path: 'forgot-password',
      component: ForgotPasswordComponent
  },
  {
    path: 'change-password',
    canActivate: [AuthGuardService],
    component: ChangePasswordComponent
  }
];
