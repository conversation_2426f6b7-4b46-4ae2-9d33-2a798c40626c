import { Injectable } from '@angular/core';
import {HttpClientService} from '../../shared';
import {AppConfig} from '../../app.config';
import {FilterCharts} from './report-dashboard.model';


@Injectable({
  providedIn: 'root'
})
export class ReportDashboardService {

  constructor(private http: HttpClientService) { }

  getPerWorkTypeTotalHours(parameterValue: FilterCharts) {
    return this.http.post(AppConfig.PER_WORK_TYPE_TOTAL_HOURS, parameterValue);
  }

  getDateWiseTotalHours(parameterValue: FilterCharts) {
    return this.http.post(AppConfig.DATE_WISE_TOTAL_HOURS, parameterValue);
  }

  getPerProjectTotalHours(parameterValue: FilterCharts) {
    return this.http.post(AppConfig.PER_PROJECT_TOTAL_HOURS, parameterValue);
  }

  getPerUserTotalHours(parameterValue: FilterCharts) {
    return this.http.post(AppConfig.PER_USER_TOTAL_HOURS, parameterValue);
  }

}
