import { HttpParams } from "@angular/common/http";
import { SortDirection } from "@angular/material/sort";
import * as moment from "moment";
import { Variable } from "../constants/Variable.constants";

export class Utils {
  public static getCopyrightYear() {
    const startYear = 2018;
    const currentYear = new Date().getFullYear();
    if (startYear === currentYear) {
      return currentYear;
    } else {
      return startYear + "-" + currentYear;
    }
  }
}

export class DateUtils {
  public static convertDate(str) {
    if (str !== undefined && str !== null && str !== "") {
      let dtToStr;

      if (new Date().getTimezoneOffset() > 0) {
        const dt = new Date(str);
        dtToStr = dt.toISOString().split("T")[0];
      } else {
        const dt = new Date(str),
          mnth = ("0" + (dt.getMonth() + 1)).slice(-2),
          day = ("0" + dt.getDate()).slice(-2);
        dtToStr = [dt.getFullYear(), mnth, day].join("-");
      }
      return dtToStr;
    } else {
      return str;
    }
  }

  public static convertDateDdmmyy(str) {
    if (str !== undefined && str !== null && str !== "") {
      let dtToStr;

      if (new Date().getTimezoneOffset() > 0) {
        const dt = new Date(str);
        dtToStr = dt.toISOString().split("T")[0];
      } else {
        const dt = new Date(str),
          mnth = ("0" + (dt.getMonth() + 1)).slice(-2),
          day = ("0" + dt.getDate()).slice(-2);
        dtToStr = [day, mnth, dt.getFullYear()].join("-");
      }
      return dtToStr;
    } else {
      return str;
    }
  }

  public static convertStrToDate(str) {
    if (str !== undefined && str !== null && str !== "") {
      let dt;
      if (new Date().getTimezoneOffset() > 0) {
        dt = new Date(str.replace(/-/g, "/"));
      } else {
        dt = new Date(str);
      }
      return dt;
    } else {
      return str;
    }
  }

  public static getTodaysDate() {
    return moment().format(Variable.YYYY_MM_DD);
  }
}

export const createRequestOption = (req?: any): HttpParams => {
  let options: HttpParams = new HttpParams();
  if (req) {
    Object.keys(req).forEach((key) => {
      options = options.set(key, req[key]);
    });
  }
  return options;
};

export class PageableQuery {
  size: number;
  page: number;
  sort: string;
  direction: string | SortDirection;
}

export class Pageable {
  offset: number;
  pageNumber: number;
  pageSize: number;
  paged: boolean;
  sort: Sort;
  unpaged: boolean;
}

export class Sort {
  sorted: boolean;
  unsorted: boolean;
}
