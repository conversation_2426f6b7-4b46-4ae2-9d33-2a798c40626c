import { ChangeDetectionStrategy, ChangeDetector<PERSON><PERSON>, <PERSON><PERSON>nent, <PERSON>ement<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, Query<PERSON>ist, ViewChildren } from "@angular/core";
import { MatDialog, MatTableDataSource } from "@angular/material";
import * as ExcelJS from "exceljs";
import { saveAs } from "file-saver";
import moment from "moment";
import { Subscription } from "rxjs";
import { finalize } from "rxjs/operators";
import { ROLES, SharedService, SnackBarService, Variable } from "src/app/shared";
import { ConvertSecondsToHourPipe } from "src/app/shared/pipes/time-converter.pipe";
import * as XLSX from "xlsx";
import { JobCodeListItem } from "../job-code/job-code";
import { JobCodeService } from "../job-code/job-code.service";
import { Project } from "../project/project.model";
import { ProjectService } from "../project/project.service";
import { AddEditWorklogComponent } from "./add-edit-worklog/add-edit-worklog.component";
import { Employees, FilterName, IdNameDto, ModalMode, PinDto, TIME_SHEET_DISPLAY_COLUMNS, TechnologyWithEmployees, TimeSheetFilter, TimeTrack, WorkLog, WorklogListItem } from "./timesheet.model";
import { TimesheetService } from "./timesheet.service";
@Component({
  selector: "app-timesheet",
  templateUrl: "./timesheet.component.html",
  styleUrls: ["./timesheet.component.css"],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ConvertSecondsToHourPipe],
})
export class TimesheetComponent implements OnInit, OnDestroy {
  workLogList: WorklogListItem[] = [];
  isExporting = false;
  subscription: Subscription = new Subscription();
  FilterName = FilterName;
  loggedInUser: IdNameDto;
  showLoader = true;
  noDataFound = true;
  projects: Project[] = [];
  filterParam: TimeSheetFilter = new TimeSheetFilter();
  jobCodes: JobCodeListItem[];
  selectedJobCodeString: string;
  selectedJobCodeObj: JobCodeListItem;
  filteredJobCode: JobCodeListItem[] = [];
  currentDate: Date = new Date();
  startingDate!: Date;
  endingDate!: Date;
  allDates!: Date[];
  allDatesToDisplay!: string[];
  technologies: TechnologyWithEmployees[] = [];
  users: Employees[] = [];
  calendarDates!: Date[];
  dateRangeStr = "";
  monthOrWeek = "2";
  dailyTotal: any;
  displayedColumns = TIME_SHEET_DISPLAY_COLUMNS;
  pinnedJobCodes: IdNameDto[] = [];
  cellHeight: string;

  @ViewChildren("headerCell") headerCells: QueryList<ElementRef> | null;

  constructor(
    private matDialog: MatDialog,
    private jobCodeService: JobCodeService,
    private sharedService: SharedService,
    private timeSheetService: TimesheetService,
    private projectService: ProjectService,
    private cdf: ChangeDetectorRef,
    private snackBarService: SnackBarService,
    private convertSecondsToHour: ConvertSecondsToHourPipe
  ) {}

  async ngOnInit(): Promise<void> {
    this.loggedInUser = {
      id: this.sharedService.getUaaUserId(),
      name: this.sharedService.getUserName(),
    };
    this.filterParam.uaaUserId = this.loggedInUser.id;
    await this.getAllPinnedJobCode();
    await this.getAllJobCodes();
    this.setRangeDates();
    if (this.hasAdminPermission()) {
      this.getTechnologies();
      this.getProjects();
    }
  }

  private setSearchFilter(): void {
    this.filterParam.fromDate = moment(this.startingDate).format("YYYY-MM-DDTHH:mm:ssZ");
    this.filterParam.toDate = moment(this.endingDate).format("YYYY-MM-DDTHH:mm:ssZ");
    this.getWorkLogs();
  }

  getTechnologies() {
    this.subscription.add(
      this.timeSheetService.getAllTechnologyAndUsers().subscribe((res: TechnologyWithEmployees[]) => {
        this.technologies = res;
        this.cdf.detectChanges();
        this.setEmployees();
      })
    );
  }

  setEmployees(): void {
    if (!this.filterParam?.technologyId) {
      this.users = [];
      for (const tech of this.technologies) {
        this.users = this.users.concat(tech.users);
      }
    } else {
      const technology = this.technologies.find((tech) => tech.technologyId === this.filterParam?.technologyId);
      if (technology) {
        this.users = technology.users;
      } else {
        this.users = [];
      }
    }
    this.users.sort((a, b) => a.name.localeCompare(b.name));
    this.users = this.removeDuplicates(this.users, "id");
    this.cdf.detectChanges();
  }

  removeDuplicates(arr: Employees[], key: string): Employees[] {
    return arr.filter((employee, index, arr) => arr.findIndex((t) => t[key] === employee[key]) === index);
  }

  setRangeDates(): void {
    if (this.monthOrWeek === "1") {
      this.startingDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), 1);
      this.endingDate = new Date(this.startingDate.getFullYear(), this.startingDate.getMonth() + 1, 0);
    } else {
      this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), this.currentDate.getDate());
      while (this.currentDate.getDay() !== 1) {
        this.currentDate.setDate(this.currentDate.getDate() - 1);
      }
      this.startingDate = this.currentDate;
      this.endingDate = new Date(this.startingDate.getFullYear(), this.startingDate.getMonth(), this.startingDate.getDate() + 6);
    }
    this.calculateDates();
    this.setSearchFilter();
  }

  calculateDates(): void {
    this.allDates = [];
    this.calendarDates = [];
    const traverseDate = new Date(this.startingDate);
    while (traverseDate <= this.endingDate) {
      this.allDates.push(new Date(traverseDate));
      traverseDate.setDate(traverseDate.getDate() + 1);
    }
    this.calendarDates = [this.allDates[0], this.allDates[this.allDates.length - 1]];
    this.dateRangeStr = `${moment(this.calendarDates[0]).format(Variable.DD_MM_YYYY)} - ${moment(this.calendarDates[1]).format(Variable.DD_MM_YYYY)}`;
    this.setDisplayColumn();
  }

  setDisplayColumn(): void {
    this.displayedColumns = [];
    this.allDatesToDisplay = [];
    this.allDates.forEach((date, i) => {
      const dayDateFormat = moment(date).format("ddd D");
      const stringWithNextLine = `${dayDateFormat.split(" ").join("\n")}`;
      this.displayedColumns.push(stringWithNextLine);
      this.allDatesToDisplay.push(stringWithNextLine);
    });
    this.displayedColumns = ["jobCodeName", "totalSeconds", ...this.displayedColumns];
    this.cdf.detectChanges();
  }

  nextRange(): void {
    const newDate = new Date(this.startingDate);
    this.monthOrWeek === "1" ? newDate.setMonth(newDate.getMonth() + 1) : newDate.setDate(newDate.getDate() + 7);
    this.currentDate = newDate;
    this.setRangeDates();
  }

  previousRange(): void {
    const newDate = new Date(this.startingDate);
    this.monthOrWeek === "1" ? newDate.setMonth(newDate.getMonth() - 1) : newDate.setDate(newDate.getDate() - 7);
    this.currentDate = newDate;
    this.setRangeDates();
  }

  goToToday(): void {
    this.cellHeight = "auto";
    this.currentDate = new Date();
    this.setRangeDates();
  }

  async getAllJobCodes(): Promise<void> {
    try {
      const jobCodes = await this.jobCodeService.getJobCodes().toPromise();
      this.jobCodes = jobCodes.map((jobCode) => ({
        ...jobCode,
        displayName: `${jobCode.name} / ${jobCode.projectName}`,
      }));
      this.cdf.detectChanges();
    } catch (error) {}
  }

  onStateChange(value: string): void {
    this.filteredJobCode = value ? this.filterJobCodes(value) : [];
  }

  filterJobCodes(name: string): JobCodeListItem[] {
    const activeJobCodes = this.jobCodes.filter((jobCode) => jobCode.isActive);
    return activeJobCodes.filter((jobCode) => jobCode?.displayName?.toLowerCase().includes(name.toLowerCase()));
  }

  resetJobCodeField(): void {
    this.selectedJobCodeString = "";
    this.selectedJobCodeObj = null;
    this.filteredJobCode = [];
  }

  onJobCodeAdd(): void {
    this.selectedJobCodeObj = this.jobCodes.find((jobcode) => jobcode.name === this.selectedJobCodeString);
    const jobCodeAlreadyAdded = this.workLogList[0]?.timeTrack.some((jobCode) => jobCode.jobCodeId === this.selectedJobCodeObj.id);
    if (jobCodeAlreadyAdded) {
      this.snackBarService.error(Variable.JOB_CODE_ALREADY_ADDED);
      return;
    } else {
      const newJobCode = new TimeTrack();
      newJobCode.jobCodeId = this.selectedJobCodeObj.id;
      newJobCode.jobCodeName = this.selectedJobCodeObj.name;
      newJobCode.jobCodeDisplayName = this.selectedJobCodeObj.displayName;
      newJobCode.isPinned = false;
      this.changePinStatus(newJobCode);
    }
    this.resetJobCodeField();
  }

  openModal(e: TimeTrack, date: Date): void {
    let worklogData: WorkLog;
    let mode: ModalMode = ModalMode.ADD_MODE;
    if (e?.workLogs?.length) {
      e?.workLogs.forEach((dto) => {
        if (moment(dto.logDate).isSame(moment(date), "day")) {
          worklogData = dto;
        }
      });
    }
    if (worklogData) {
      mode = ModalMode.EDIT_MODE;
    }
    if (this.filterParam.employeeId || this.filterParam.technologyId || this.filterParam.projectId || !e.isActive) {
      mode = ModalMode.VIEW_MODE;
    }
    if (!e.isActive && !worklogData) {
      this.snackBarService.error(Variable.SELECTED_JOB_CODE_INACTIVE.replace("{jobCodeName}", e?.jobCodeName));
      return;
    }
    if (mode === ModalMode.VIEW_MODE && !worklogData) {
      return;
    }
    const dialogRef = this.matDialog.open(AddEditWorklogComponent, {
      width: Variable.BOX_WIDTH_VALUE,
      data: {
        mode: mode,
        date: date,
        worklogData,
        jobCode: { id: e?.jobCodeId, name: e?.jobCodeName },
        userId: this.loggedInUser.id,
        dailyTotal: this.dailyTotal,
      },
    });
    dialogRef.afterClosed().subscribe((res: boolean) => {
      if (res) {
        this.getWorkLogs();
      }
    });
  }

  private getWorkLogs(): void {
    this.workLogList = [];
    this.showLoader = true;
    this.cdf.detectChanges();
    this.subscription.add(
      this.timeSheetService
        .getAllWorkLogs(this.filterParam)
        .pipe(
          finalize(() => {
            this.noDataFound = this.workLogList?.length <= 0;
            this.showLoader = false;
            this.cdf.detectChanges();
          })
        )
        .subscribe({
          next: (res: WorklogListItem[]) => {
            this.workLogList = res;
            this.workLogList.forEach((userList) => {
              userList.timeTrack
                .sort((a, b) => a.jobCodeName.localeCompare(b.jobCodeName))
                .forEach((jobCodeDto) => {
                  jobCodeDto.isActive = this.jobCodes.find((jobcode) => jobcode.id === jobCodeDto.jobCodeId).isActive;
                  jobCodeDto.jobCodeDisplayName = this.jobCodes.find((jobcode) => jobcode.id === jobCodeDto.jobCodeId).displayName ?? jobCodeDto.jobCodeName;
                  jobCodeDto.isPinned = false;
                });
            });
            if (!(this.filterParam.employeeId || this.filterParam.technologyId || this.filterParam.projectId) && res.length === 1) {
              this.setPinnedOrder();
            } else {
              this.workLogList.forEach((user) => {
                user.timeTrack;
              });
            }
            this.setDailyTotal();
            setTimeout(() => {
              this.findMaxHeightHeaderCell();
            }, 0);
          },
          error: (error) => {
            this.snackBarService.error(error.error.message);
          },
        })
    );
  }

  getProjects(): void {
    this.subscription.add(
      this.projectService.getActiveProjects().subscribe((res: Project[]) => {
        this.projects = res;
      })
    );
  }

  private setPinnedOrder(): void {
    const timeTrackList = this.workLogList[0].timeTrack;
    const finalList: TimeTrack[] = [];
    if (this.pinnedJobCodes.length) {
      this.pinnedJobCodes.forEach((pinnedJobCode) => {
        const jobCodeIndex = timeTrackList.findIndex((jobCode) => jobCode.jobCodeId === pinnedJobCode.id);
        if (jobCodeIndex !== -1) {
          const pinnedJobCode = timeTrackList[jobCodeIndex];
          pinnedJobCode.isPinned = true;
          finalList.push(pinnedJobCode);
          timeTrackList.splice(jobCodeIndex, 1);
        } else {
          const newJobCode = new TimeTrack();
          newJobCode.jobCodeId = pinnedJobCode.id;
          newJobCode.jobCodeName = pinnedJobCode.name;
          newJobCode.totalWorkLogTime = 0;
          newJobCode.isPinned = true;
          newJobCode.isActive = this.jobCodes.find((jobcode) => jobcode.id === newJobCode.jobCodeId).isActive;
          newJobCode.jobCodeDisplayName = this.jobCodes.find((jobCode) => jobCode.id === newJobCode.jobCodeId)?.displayName ?? newJobCode.jobCodeName;
          finalList.push(newJobCode);
        }
      });
    }
    timeTrackList.sort((a, b) => a.jobCodeName.localeCompare(b.jobCodeName));
    this.workLogList[0].timeTrack = [...finalList, ...timeTrackList];
    this.cdf.detectChanges();
  }

  private setDailyTotal(): void {
    this.dailyTotal = {};
    this.workLogList.forEach((user) => {
      this.dailyTotal[user.user.id] = {};
      user.timeTrack.forEach((jobCode) => {
        jobCode.workLogs.forEach((worklog) => {
          const date = moment(worklog.logDate).format("YYYY-MM-DDTHH:mm:ssZ").split("T")[0];
          if (this.dailyTotal[user.user.id][date]) {
            this.dailyTotal[user.user.id][date] += worklog.timeTrackLogsTotal;
          } else {
            this.dailyTotal[user.user.id][date] = worklog.timeTrackLogsTotal;
          }
        });
      });
    });
  }

  getDailyTotal(userId: number, dateTime: Date): number {
    if (this.dailyTotal) {
      const date = moment(dateTime).format(Variable.YYYY_MM_DD);
      return this.dailyTotal[`${userId}`][date];
    }
  }

  getDataSource(data: WorklogListItem) {
    return new MatTableDataSource<TimeTrack>(data.timeTrack);
  }

  resetFilter(str: string) {
    switch (str) {
      case FilterName.TECHNOLOGY:
        this.filterParam.technologyId = null;
        break;
      case FilterName.PROJECT:
        this.filterParam.projectId = null;
        break;
      case FilterName.EMPLOYEE:
        this.filterParam.uaaUserId = null;
        this.filterParam.employeeId = null;
        break;
      case FilterName.ALL:
        this.filterParam.uaaUserId = null;
        this.filterParam.employeeId = null;
        this.filterParam.technologyId = null;
        this.filterParam.projectId = null;
        break;
      default:
        break;
    }
    if (!this.filterParam.uaaUserId && !this.filterParam.technologyId && !this.filterParam.projectId) {
      this.filterParam.uaaUserId = this.loggedInUser.id;
    }
    this.setEmployees();
    this.getWorkLogs();
  }

  setFilterParam(str: string) {
    switch (str) {
      case FilterName.TECHNOLOGY:
        this.filterParam.employeeId = null;
        this.filterParam.uaaUserId = null;
        this.filterParam.projectId = null;
        break;
      case FilterName.PROJECT:
        this.filterParam.employeeId = null;
        this.filterParam.uaaUserId = null;
        this.filterParam.technologyId = null;
        break;
      case FilterName.EMPLOYEE:
        this.filterParam.technologyId = null;
        this.filterParam.projectId = null;
        break;

      default:
        break;
    }
    setTimeout(() => {
      this.filterParam.uaaUserId = this.filterParam.employeeId;
      this.cdf.detectChanges();
      this.setEmployees();
      this.getWorkLogs();
    }, 0);
  }

  changePinStatus(e: TimeTrack) {
    if (e.isActive === false && !e.isPinned) {
      this.snackBarService.error(Variable.SELECTED_JOB_CODE_INACTIVE.replace("{jobCodeName}", e?.jobCodeName));
      return;
    }
    this.showLoader = true;
    e.isPinned = !e.isPinned;
    const obj = new PinDto(this.loggedInUser.id, e.jobCodeId, e.isPinned);
    this.subscription.add(
      this.jobCodeService.pinUnpinJobCode(obj).subscribe({
        next: async () => {
          await this.getAllPinnedJobCode();
          this.getWorkLogs();
        },
      })
    );
  }

  async getAllPinnedJobCode(): Promise<void> {
    try {
      const res = await this.jobCodeService.getUserPinnedJobCode(this.loggedInUser.id).toPromise();
      this.pinnedJobCodes = res;
    } catch (error) {}
  }

  hasAdminPermission(): boolean {
    const roles = this.sharedService.getRole();
    return roles.includes(ROLES.SUPER_ADMIN) || roles.includes(ROLES.ADMIN) || roles.includes(ROLES.LEAD) || roles.includes(ROLES.HR);
  }

  isWeekend(dateStr: Date) {
    const date = moment(dateStr);
    const dayOfWeek = date.day();
    return dayOfWeek === 0 || dayOfWeek === 6;
  }

  isToday(date: Date): boolean {
    const currentDate = new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate());
    if (moment(currentDate).isSame(date)) {
      return true;
    }
    return false;
  }

  getDDMMYYYY(date: Date): string {
    return moment(date).format("DD/MM/YYYY");
  }

  exportTimeSheet(): void {
    this.isExporting = true;
    const EXPORT_DATA: string[][] = [this.createHeaderRow()];
    let totalSeconds = 0;
    this.workLogList.forEach((userLogs) => {
      totalSeconds = totalSeconds + (userLogs.totalAllJobCodeLogTime ?? 0);
      if (userLogs?.timeTrack?.length) {
        EXPORT_DATA.push([]);
        EXPORT_DATA.push([`${userLogs.user.name} (${userLogs.technologyList.map((tech) => tech.technologyName).join(", ")})`]);
        EXPORT_DATA.push(...this.getUserTimeTrackRows(userLogs));
        EXPORT_DATA.push(this.getLastRowOfUserTimeSheet(userLogs));
      }
    });
    EXPORT_DATA.push([]);
    EXPORT_DATA.push(["Total Of All Users", this.convertSecondsToHour.transform(totalSeconds)]);
    this.exportDataFn(EXPORT_DATA);
  }

  createHeaderRow(): string[] {
    const header = ["Job Codes", "Total Hours", ...this.allDates.map((date) => moment(date).format("DD MMM YYYY"))];
    return header;
  }

  getUserTimeTrackRows(userLogs: WorklogListItem): string[][] {
    const timeTrackRows: string[][] = [];
    userLogs.timeTrack.forEach((jobCode) => {
      const userLogsData = [jobCode.jobCodeDisplayName, this.convertSecondsToHour.transform(jobCode.totalWorkLogTime), ...this.allDates.map((date) => this.getTotalHoursForDate(jobCode, date))];
      timeTrackRows.push(userLogsData);
    });
    return timeTrackRows;
  }

  getTotalHoursForDate(jobCode: TimeTrack, date: Date): string {
    const formattedDate = moment(date).format("YYYY-MM-DD");
    const logEntry = jobCode.workLogs.find((log) => moment(log.logDate).isSame(formattedDate, "day"));
    return logEntry ? this.convertSecondsToHour.transform(logEntry.timeTrackLogsTotal) : "";
  }

  getLastRowOfUserTimeSheet(userLogs: WorklogListItem): string[] {
    const lastRow: string[] = ["Total", this.convertSecondsToHour.transform(userLogs.totalAllJobCodeLogTime), ...this.allDates.map((date) => this.getTotalHoursForUserAndDate(userLogs, date))];
    return lastRow;
  }

  getTotalHoursForUserAndDate(userLogs: WorklogListItem, date: Date): string {
    const formattedDate = moment(date).format("YYYY-MM-DD");
    const totalHours = this.dailyTotal[String(userLogs.user.id)][formattedDate];
    return totalHours ? this.convertSecondsToHour.transform(totalHours) : "";
  }

  exportDataFn(data: string[][]) {
    const ws: XLSX.WorkSheet = XLSX.utils.aoa_to_sheet(data);
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "TimeSheetData");
    XLSX.writeFile(wb, this.fileName);
    this.isExporting = false;
  }

  private get fileName(): string {
    const startDate = this.getDDMMYYYYFormat(this.allDates[0]);
    const endDate = this.getDDMMYYYYFormat(this.allDates[this.allDates.length - 1]);
    let appliedFilterName = "";
    if (this.filterParam.uaaUserId) {
      appliedFilterName = this.users.find((user) => user.id === this.filterParam.uaaUserId).name;
    }
    if (this.filterParam.technologyId) {
      appliedFilterName = this.technologies.find((tech) => tech.technologyId === this.filterParam.technologyId).technologyName;
    }
    if (this.filterParam.projectId) {
      appliedFilterName = this.projects.find((project) => project.id === this.filterParam.projectId).name;
    }
    return `TimeSheet_${appliedFilterName ? `${appliedFilterName}_` : ""}${startDate}_to_${endDate}.xlsx`;
  }

  isExportable(): boolean {
    return !this.workLogList.some((userLogs) => {
      return userLogs.timeTrack.length;
    });
  }

  private getDDMMYYYYFormat(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const day = date.getDate().toString().padStart(2, "0");
    return `${day}${month}${year}`;
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  onClickIndividualExport() {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Worklog");

    // Add the user information at the top
    worksheet.addRow([`'Name: ', this.workLogList[0].user.name  (${moment(this.startingDate).format("DD-MM-YYYY")} to ${moment(this.endingDate).format("DD-MM-YYYY")})`]);
    worksheet.mergeCells("A1:E1");
    worksheet.getCell("A1").value = `Name: ${this.workLogList[0].user.name} :  (${moment(this.startingDate).format("DD-MM-YYYY")} to ${moment(this.endingDate).format("DD-MM-YYYY")})`;
    worksheet.getCell("A1").font = { bold: true };
    worksheet.getCell("A1").alignment = { horizontal: "center" };

    // Add headers
    const headers = ["Job Code", "Total", "Date", "Log", "Description"];
    const headerRow = worksheet.addRow(headers);
    headerRow.eachCell((cell) => {
      cell.font = { bold: true, size: 12 };
      cell.alignment = { vertical: "middle", horizontal: "center" };
    });

    // Add data
    const jobCodeCells: { [key: string]: number[] } = {};
    let currentRow = 3;
    let maxDescriptionLength = 0;

    this.workLogList[0].timeTrack.forEach((track: any) => {
      track.workLogs.forEach((log: any) => {
        log.logs.forEach((entry: any) => {
          const row = worksheet.addRow([
            track.jobCodeName,
            this.convertSecondsToHM(track.totalWorkLogTime),
            new Date(log.logDate).toLocaleDateString(),
            this.convertSecondsToHM(entry.logTime),
            entry.description,
          ]);
          row.getCell(1).alignment = { vertical: "middle", horizontal: "center" };
          row.getCell(2).alignment = { vertical: "middle", horizontal: "center" };
          row.getCell(3).alignment = { vertical: "middle", horizontal: "center" };
          row.getCell(4).alignment = { vertical: "middle", horizontal: "center" };

          maxDescriptionLength = Math.max(maxDescriptionLength, entry.description.length);

          if (!jobCodeCells[track.jobCodeName]) {
            jobCodeCells[track.jobCodeName] = [currentRow];
          } else {
            jobCodeCells[track.jobCodeName].push(currentRow);
          }
          currentRow++;
        });
      });
    });

    // Merge cells with the same job code
    Object.keys(jobCodeCells).forEach((jobCode) => {
      const rows = jobCodeCells[jobCode];
      if (rows.length > 1) {
        worksheet.mergeCells(`A${rows[0]}:A${rows[rows.length - 1]}`);
        worksheet.mergeCells(`B${rows[0]}:B${rows[rows.length - 1]}`);
      }
    });

    // Add monthly total
    worksheet.addRow([]);
    const totalRow = worksheet.addRow(["", "", "Monthly Total", this.convertSecondsToHM(this.workLogList[0].totalAllJobCodeLogTime), ""]);
    totalRow.eachCell((cell, colNumber) => {
      if (colNumber === 3 || colNumber === 4) {
        cell.alignment = { vertical: "middle", horizontal: "center" };
        cell.font = { bold: true };
      }
    });

    // Set column widths
    worksheet.getColumn(1).width = 18; // Job Code
    worksheet.getColumn(2).width = 18; // Total
    worksheet.getColumn(3).width = 18; // Date
    worksheet.getColumn(4).width = 18; // Log
    worksheet.getColumn(5).width = maxDescriptionLength + 5; // Description

    // Add borders
    worksheet.eachRow((row) => {
      row.eachCell({ includeEmpty: false }, (cell) => {
        cell.border = {
          top: { style: "thin" },
          left: { style: "thin" },
          bottom: { style: "thin" },
          right: { style: "thin" },
        };
      });
    });

    // Save the workbook
    workbook.xlsx.writeBuffer().then((buffer) => {
      const blob = new Blob([buffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });
      const startDate = this.getDDMMYYYYFormat(this.allDates[0]);
      const endDate = this.getDDMMYYYYFormat(this.allDates[this.allDates.length - 1]);
      saveAs(blob, `${`TimeSheet_${this.workLogList[0].user.name}${startDate}_to_${endDate}.xlsx`}`);
    });
  }

  convertSecondsToHM(seconds: number): string {
    const h = Math.floor(seconds / 3600);
    const m = Math.floor((seconds % 3600) / 60);
    return `${h}h ${m}m`;
  }

  findMaxHeightHeaderCell() {
    this.cellHeight = "auto";
    this.cdf.detectChanges();
    let maxHeight = 0;

    this.headerCells.forEach((cell: ElementRef) => {
      const cellHeight = cell.nativeElement.offsetHeight;
      if (cellHeight > maxHeight) {
        maxHeight = cellHeight;
        this.cellHeight = `${cellHeight}px`;
      }
    });
    this.cdf.detectChanges();
  }
}
