import { Variable } from "src/app/shared";

export class JobCode {
  id?: number;
  name: string;
  clientId: number;
  projectId: number;
  technologyId: number;
  tagName: string;
  isActive = true;
  isBillable = false;
  description: string;
}

export interface JobCodeListItem {
  id: number;
  name: string;
  clientId: number;
  clientName: string;
  technologyId: number;
  technologyName: string;
  projectId: number;
  projectName: string;
  isActive: boolean;
  tagName: string;
  isBillable: boolean;
  description: string;
  createdBy: string;
  displayName?: string;
}
export class JobCodeModal {
  isEdit: boolean;
  obj: JobCodeListItem;
}

export const JOB_CODE_DISPLAY_COLUMNS = ["name", "clientName", "projectName", "technologyName", "isActive", Variable.ACTION];
