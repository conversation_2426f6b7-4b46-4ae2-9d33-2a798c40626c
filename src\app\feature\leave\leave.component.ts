import { Component, OnInit, ViewChild, On<PERSON><PERSON>roy } from '@angular/core';
import { MatDialog, MatSort, MatPaginator, MatTableDataSource } from '@angular/material';
import { Leave, FilterLeaves, LeavePageable, LEAVE_TYPE_LIST, LEAVE_DATE_WISE_FILTER_LIST, LEAVE_DISPLAY_COLUMNS } from './leave.model';
import { Subscription } from 'rxjs';
import { LeaveService } from './leave.service';
import {PageableQuery, ROLES, SharedService} from '../../shared';
import { Variable } from '../../shared';
import { DateUtils } from '../../shared';
import { Employee } from '../employee-management/employee-management.model';
import { LeaveModalComponent } from './leave-modal/leave-modal.component';
import { ActivatedRoute } from '@angular/router';
import { SweetAlertService } from 'src/app/shared/service/sweetalert.service';
import {finalize} from 'rxjs/operators';
import {EmployeeManagementService} from '../employee-management/employee-management.service';

@Component({
  selector: 'sfl-leave',
  templateUrl: './leave.component.html'
})
export class LeaveComponent implements OnInit, OnDestroy {
  @ViewChild(MatSort, {static: false}) sort: MatSort;
  @ViewChild(MatPaginator, {static: false}) paginator: MatPaginator;

  showLoader = true;
  noDataFound = true;
  displayedColumns = LEAVE_DISPLAY_COLUMNS;
  dateFormat = Variable.MM_DD_YYYY;
  dataSource = new MatTableDataSource<Leave>([]);
  userId: number;
  subscription: Subscription = new Subscription();
  leavePageable: LeavePageable = new LeavePageable();
  users: Employee[] = [];
  filterLeaves: FilterLeaves = new FilterLeaves();
  updateLeave: Leave = new Leave();
  leaveTypes: string[] = LEAVE_TYPE_LIST;
  dateWiseFilter: string[] = LEAVE_DATE_WISE_FILTER_LIST;
  freeSearch = Variable.FREE_SEARCH.replace(' ', '');
  uptoYear = Variable.UPTO_YEAR.replace(' ', '');
  pageable: PageableQuery = new PageableQuery();
  isActive = false;

  constructor(
    private matDialog: MatDialog,
    private sharedService: SharedService,
    private leaveService: LeaveService,
    private employeeService: EmployeeManagementService,
    private activatedRoute: ActivatedRoute,
    private sweetAlertService: SweetAlertService
  ) {
    this.pageable.page = 0;
    this.pageable.size = 10;
    this.pageable.sort = Variable.FROM_DATE;
    this.pageable.direction = Variable.DESC;
    this.filterLeaves.leaveStatus = Variable.DEFAULT_INACTIVE_FILTER;
  }

  ngOnInit() {
    const currentUserRole = this.sharedService.getRole();
    if (currentUserRole.includes(ROLES.SUPER_ADMIN) ||
      currentUserRole.includes(ROLES.ADMIN) ||
      currentUserRole.includes(ROLES.LEAD) ||
      currentUserRole.includes(ROLES.HR)) {
      this.isActive = true;
    } else {
      this.displayedColumns.splice(this.displayedColumns.indexOf(Variable.ACTION), 1);
      this.filterLeaves.uaaUserId = this.sharedService.getUaaUserId();
    }
    this.activatedRoute.queryParams.subscribe((params) => {
      if (params && params.addLeave) {
        setTimeout(() => {
          this.openSaveLeave(null);
        }, 0);
      }
    });
    this.getUsers();
    this.applyFilter();
    this.subscription.add(
      this.leaveService.invokeEvent.subscribe(() => {
        this.getUsers();
        this.applyFilter();
      })
    );
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

  getSorting(event) {
    this.pageable.sort = event.active;
    this.pageable.direction = event.direction;
    this.pageable.page = 0;
    this.applyFilter(false);
  }

  getPagination(event) {
    this.pageable.size = event.pageSize;
    this.pageable.page = event.pageIndex;
    this.applyFilter(false);
  }

  applyFilter(set_page_zero: boolean = true) {
    this.pageable.page = set_page_zero ? 0 : this.pageable.page;
    this.showLoader = true;
    this.noDataFound = true;

    if (this.filterLeaves.filterBy !== this.freeSearch) {
      this.filterLeaves.startDate = undefined;
      this.filterLeaves.endDate = undefined;
    }
    this.filterLeaves.startDate = DateUtils.convertDate(this.filterLeaves.startDate);
    this.filterLeaves.endDate = DateUtils.convertDate(this.filterLeaves.endDate);

    const currentSort = this.pageable.sort;
    this.pageable.sort = this.pageable.sort + ',' + this.pageable.direction;
    this.subscription.add(
      this.leaveService.getFilterLeaves(this.filterLeaves, this.pageable)
        .pipe(
          finalize(() => {
            this.noDataFound = this.dataSource.data.length <= 0;
            this.showLoader = false;
          })
        )
        .subscribe((res: LeavePageable) => {
          this.leavePageable = res;
          this.dataSource = new MatTableDataSource<Leave>(this.leavePageable.content);
          this.pageable.size = this.leavePageable.pageable.pageSize;
          this.pageable.page = this.leavePageable.pageable.pageNumber;
        })
    );
    this.pageable.sort = currentSort;

    this.filterLeaves.startDate = DateUtils.convertStrToDate(this.filterLeaves.startDate);
    this.filterLeaves.endDate = DateUtils.convertStrToDate(this.filterLeaves.endDate);
  }

  openSaveLeave(leave: Leave) {
    this.matDialog.open(LeaveModalComponent, {
      width: Variable.BOX_WIDTH_VALUE,
      data: leave
    });
  }

  updateLeaveStatus(id, ob) {
    this.updateLeave.id = id;
    this.updateLeave.status = ob.checked;
    this.leaveService.updateLeaveStatus(this.updateLeave).subscribe(() => {
      this.applyFilter(false);
    });
  }

  resetFilter(parameter: string) {
    this.filterLeaves[parameter] = undefined;
  }

  resetAllFilter() {
    this.filterLeaves = new FilterLeaves();
    this.filterLeaves.leaveStatus = Variable.DEFAULT_INACTIVE_FILTER;
    this.filterLeaves.uaaUserId = !this.isActive ? this.sharedService.getUaaUserId() : null;
    this.applyFilter();
  }

  getUsers() {
    this.subscription.add(
      this.employeeService.getEmployees().subscribe((res: Employee[]) => {
        this.users = res;
      })
    );
  }

  getLeaveExcel() {
    this.filterLeaves.startDate = DateUtils.convertDate(this.filterLeaves.startDate);
    this.filterLeaves.endDate = DateUtils.convertDate(this.filterLeaves.endDate);
    this.subscription.add(
      this.leaveService.getLeaveFile(this.filterLeaves, this.sharedService.getUserId()).subscribe((data: any) => {
        window.open(data.fileUrl, Variable._BLANK);
      })
    );

    this.filterLeaves.startDate = DateUtils.convertStrToDate(this.filterLeaves.startDate);
    this.filterLeaves.endDate = DateUtils.convertStrToDate(this.filterLeaves.endDate);
  }

  canEdit(element) {
    if (!this.isActive && this.sharedService.getUaaUserId() !== element.uaaUserId) {
      return true;
    } else {
      const today = DateUtils.getTodaysDate();
      return element.status && (element.fromDate === today || element.fromDate <= today);
    }
  }

  deleteLeave(element) {
    const deleteConfirm = this.sweetAlertService.deleteAlert('this leave?');
    deleteConfirm.then((value) => {
      if (value) {
        this.subscription.add(this.leaveService.deleteLeave(element.id).subscribe(() => {
          this.applyFilter(false);
        }));
      }
    });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
