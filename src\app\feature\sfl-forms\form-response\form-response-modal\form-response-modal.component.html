<h2 mat-dialog-title>
  Filled by: {{ data.filledBy }} on {{ data.createdDate | date: 'MM/dd/yyyy' }}
</h2>
<p>{{ sflForm?.formName }}</p>
<hr class="mb-1">

<mat-dialog-content class="mb-1">
  <mat-card-content fxLayout="column">
    <div class="p-25" fxLayoutAlign="center center" fxFlex="100" *ngIf="showLoader">
      <mat-progress-spinner color="warn" mode="indeterminate"></mat-progress-spinner>
    </div>
    <table *ngIf="!showLoader" class="w-auto">
      <tr *ngFor="let question of sflForm.questionsDTOS; let i=index">
        <td class="py-1" *ngIf="question.id === formResponse.answersDTOS[i].questionId">
          <div class="questions-text">
            <span class="question-number">{{i+1}}. </span>
            <span [ngClass]="question.required? 'question-required-notice-star': ''">{{question.text}}</span>
            <div class="question-ml">
              <span class="question-hint">{{question.hint}}</span>
            </div>
          </div>

          <div class="question-ml" *ngIf="question.type === questionType.TEXT">
            <mat-form-field class="form-response-text-field">
              <input matInput placeholder="Enter your answer" aria-label="q-text" [value]="formResponse.answersDTOS[i].answer" readonly>
            </mat-form-field>
          </div>

          <div class="question-ml" *ngIf="question.type === questionType.TEXT_AREA">
            <mat-form-field class="form-response-text-field">
                <textarea matInput placeholder="Enter your answer" aria-label="q-textarea" [value]="formResponse.answersDTOS[i].answer" readonly></textarea>
            </mat-form-field>
          </div>

          <div class="question-ml" *ngIf="question.type === questionType.RADIO">
            <mat-radio-group class="d-inline-block" [value]="formResponse.answersDTOS[i].answer" disabled>
              <mat-radio-button class="mr-10px" color="warn" *ngFor="let option of question.optionsDTOS" [value]="option.optionName">
                {{option.optionName}}
              </mat-radio-button>
            </mat-radio-group>
          </div>

          <div class="question-ml" *ngIf="question.type === questionType.MULTI_RADIO">
            <table>
              <thead>
              <tr>
                <th></th>
                <th>
                  <span class="d-inline-block px-1 text-center w-60px" *ngFor="let option of question.optionsDTOS">{{option.optionName}}</span>
                </th>
              </tr>
              </thead>
              <tbody>
              <tr *ngFor="let subQuestion of question.subQuestionsDTOS; let j=index">
                <td class="questions-text">
                  <span>{{subQuestion.text}}</span>
                </td>
                <td *ngIf="subQuestion.id === formResponse.answersDTOS[i].subQuestionsAnswerDTOS[j].subQuestionId">
                  <mat-radio-group class="d-inline-block" [value]="formResponse.answersDTOS[i].subQuestionsAnswerDTOS[j].answer" disabled>
                    <mat-radio-button color="warn" class="px-1 m-0 text-center w-60px" *ngFor="let option of question.optionsDTOS" [value]="option.optionName"></mat-radio-button>
                  </mat-radio-group>
                </td>
              </tr>
              </tbody>
            </table>
          </div>

        </td>
      </tr>
    </table>
  </mat-card-content>
</mat-dialog-content>
<hr>

<mat-dialog-actions fxLayoutAlign="end">
  <button mat-raised-button class="bt-sfl" type="submit" (click)="closeDialog()">Close</button>
</mat-dialog-actions>
