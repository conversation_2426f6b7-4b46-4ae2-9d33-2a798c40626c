import { Routes } from '@angular/router';
import { ProjectComponent, ProjectModalComponent } from './index';
import { ProjectDetailsComponent } from './project-details/project-details.component';
import {RoleGuardService} from '../../shared/service/role-guard.service';
import {ROLES} from '../../shared';
import {PreviewModalComponent} from '../sfl-forms/preview-modal/preview-modal.component';

export const projectRoutes: Routes = [
  {
    path: '',
    canActivate: [RoleGuardService],
    data: {
      allowedRoles: [ROLES.SUPER_ADMIN, ROLES.ADMIN, ROLES.LEAD]
    },
    component: ProjectComponent,
  },
  {
    path: '',
    canActivate: [RoleGuardService],
    data: {
      allowedRoles: [ROLES.SUPER_ADMIN, ROLES.ADMIN, ROLES.LEAD]
    },
    component: ProjectModalComponent
  },
  {
    path: 'details/:id',
    component: ProjectDetailsComponent
  },
  {
    path: 'preview-modal',
    component: PreviewModalComponent
  }
];
