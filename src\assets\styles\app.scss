html,
body {
  height: 100%;
}

body {
  background: #f2f3f8;
  background-size: cover;
  padding: 0;
  margin: 0;
  overflow: hidden;
}

a {
  text-decoration: none;
  color: inherit;
}

.mat-drawer-container {
  background-color: #f2f3f8;
}

.mat-icon-button {
  color: #505050;
}

.float-left {
  float: left;
}

.float-right {
  float: right;
}

.sub-title-font-color {
  color: #66666f;
}

.green-snackbar {
  background: #4caf50;
}

.red-snackbar {
  background: #f44336;
}

.green-snackbar .mat-simple-snackbar-action {
  background: #1b5e20;
  color: #ffffff !important;
}

.red-snackbar .mat-simple-snackbar-action {
  background: #b71c1c;
  color: #ffffff !important;
}

.mat-simple-snackbar-action .mat-button {
  min-width: auto !important;
}

.mat-badge-warn {
  color: #ffffff;
  background: #f44336 !important;
  padding: 5px 10px;
  font-size: 13px;
  margin-bottom: 10px;
}

.mat-step-header .mat-step-icon {
  background-color: #ff6106;
}

.mat-text-warn {
  color: #f44336 !important;
}

.text-center {
  text-align: center !important;
}

.pb-1 {
  padding-bottom: 1rem !important;
}

.pt-1 {
  padding-top: 1rem !important;
}

.m-1 {
  margin: 1rem !important;
}

.mt-10px {
  margin-top: 10px !important;
}

.mr-10px {
  margin-right: 10px !important;
}

.mr-1 {
  margin-right: 1rem !important;
}

.mb-1 {
  margin-bottom: 1rem !important;
}

.outside-view {
  min-height: 100%;
  margin-bottom: -60px;
}

.account-wrapper {
  padding: 40px 0 80px 0;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  min-width: 100%;
}

.account {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  &.register {
    max-width: 650px;
  }
}

.mat-form-field {
  width: 100%;
}

.account .sub-title-font-color {
  font-size: 14px;
}

.mat-expansion-panel-header-description,
.mat-expansion-panel-header-title {
  flex-grow: 0 !important;
}

.mat-expansion-panel-header {
}

.mat-expansion-panel-header-title,
.mat-nav-list .mat-list-item {
  color: #505050;
}

.mat-nav-list .mat-list-item.active {
  color: #ff6106;
}

.mat-expansion-panel:hover .mat-expansion-panel-header-title,
.mat-nav-list .mat-list-item:hover,
.ext-nav-list:hover a {
  color: #ff6106 !important;
}

.mat-nav-list .mat-list-item:hover {
  background: rgba(0, 0, 0, 0.04) !important;
}

.mat-nav-list .mat-list-item .mat-list-text {
  display: inline-block !important;
}

.p-0 .mat-list-item-content {
  padding: 0 !important;
}

.mat-nav-list .mat-list-item.mat-list-item-with-avatar {
  height: 48px !important;
}

.mat-list-text {
  padding-left: 0 !important;
}

.ext-nav-list .mat-list-text {
  width: 100% !important;
}

.branding {
  margin-top: -8px;
  padding: 18.5px 18.5px;
}

.logo {
  width: auto;
  height: 30px;
  vertical-align: top;
}
.logo-text {
  vertical-align: text-top;
  color: #8e8e8e;
  font-weight: 600;
  font-size: 28px;
  margin-left: 3px;
}

.mat-drawer-content > .ps {
  position: relative;
  height: 93%;
  min-height: 93%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-box-direction: normal;
  -webkit-box-orient: vertical;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0.33333333rem;
}

.mat-form .mat-card.mat-card {
  padding: 0;
}

.mat-form .mat-card {
  padding: 0;
  border-radius: 2px;
  -webkit-box-shadow: 0 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12);
  box-shadow: 0 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12);
  color: rgba(0, 0, 0, 0.87);
}

.mat-form .mat-tab-group .mat-tab-body-wrapper .mat-card {
  box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12) !important;
  padding: 10px 0 0 0 !important;
  border-radius: 2px;
  color: rgba(0, 0, 0, 0.87);
}

.mat-card-actions {
  padding: 8px 0 0;
}

.mat-card-actions,
.mat-card-content,
.mat-card-subtitle,
.mat-card-title {
  margin-bottom: 0;
}

.mat-form .mat-card .mat-card-title {
  padding-top: 1rem;
  padding-left: 1rem;
  padding-right: 1rem;
  margin-bottom: 10px;
  line-height: 1;
  font-size: 16px;
  font-weight: 400;
  display: grid;
}

.mat-form .mat-card .mat-card-subtitle {
  padding-left: 1rem;
  padding-right: 1rem;
  line-height: 1;
  font-size: 13px;
  display: grid;
}

.mat-form .mat-card .mat-card-content {
  padding: 1rem;
  margin-bottom: 0;
  position: relative;
}

.mat-form .mat-card .mat-card-actions,
.mat-form .mat-card .mat-card-actions:last-child {
  padding: 0.5rem;
  margin: 0;
}

.mat-header {
  margin: -0.33333333rem;
  margin-bottom: 7px;
}

.mat-header .mat-card {
  padding: 10px 15px;
  height: 35px;
}

.mat-header.button .mat-card-title {
  padding-top: 0;
}

.mat-header .mat-card-title {
  margin-bottom: 0;
  padding-top: 7px;
}

.mat-header .mat-card-title {
  font-size: 22px;
}

.mat-list {
  padding-top: 0 !important;
}

.mat-list.less-data .text-time {
  margin-top: -5px;
}

.mat-list.less-data .mat-list-button.mat-icon-button {
  margin-top: -10px;
}

.mat-list .mat-card {
  padding-bottom: 0;
}

.list-grid {
  margin-bottom: 2px;
  margin-left: 5px;
  margin-right: 5px;
}

.list-grid span .mat-icon {
  background-color: #5262bc;
  color: #ffffff;
  padding: 4px;
  cursor: pointer;
}

.list-grid .mat-icon:hover {
  background-color: #29315e;
}

.list-grid .active .mat-icon {
  background-color: #29315e;
}

.list-grid .active .filter {
  background-color: #29315e;
}

.mat-list .mat-card-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 5px;
  display: grid;
}

.mat-card-title {
  margin-bottom: 0;
  padding-bottom: 0 !important;
}

.mat-list .mat-card-subtitle {
  margin-bottom: 5px;
  display: grid;
}

.mat-list-button.mat-icon-button {
  margin-top: -20px;
  margin-right: -20px;
}

.mat-tree .mat-list-button.mat-icon-button {
  margin-top: 0;
}

.mat-list .mat-card-avatar {
  margin-right: 15px;
  margin-top: -5px;
}

.mat-paginator {
  width: 100%;
  margin-top: 10px;
}

.mat-table {
  width: 100%;
  margin: 0.33333333rem;
  display: grid;
}

.mat-slider {
  width: 100%;
  margin-top: 5px;
  padding: 0 !important;
}

.mat-slider-horizontal .mat-slider-wrapper {
  left: 0;
  right: 0;
}

mat-radio-group {
  display: flex;
  flex-direction: column;
  margin: 15px 0;
}

mat-radio-button {
  margin: 5px;
}

hr {
  margin-top: 0;
  margin-bottom: 0;
  border-top: 1px solid rgba(0, 0, 0, 0.12);
  border-right-width: 0;
  border-bottom-width: 0;
  border-left-width: 0;
}

mat-toolbar.main-header {
  background: #ffffff !important;
  position: relative;
  -webkit-box-shadow: 0 1px 8px rgba(0, 0, 0, 0.3);
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.3);
  z-index: 9;
}

mat-toolbar.main-header .search-bar .search-form {
  background: white;
  position: relative;
  border-radius: 2px;
  margin-right: 1rem;
  display: block;
  max-width: 800px;
}

mat-toolbar.main-header .search-bar .search-form .material-icons {
  position: absolute;
  top: 50%;
  left: 10px;
  margin-top: -12px;
  color: rgba(0, 0, 0, 0.87);
}

mat-toolbar.main-header .search-bar .search-form input {
  font-size: 1rem;
  padding: 0.8rem 0.75rem;
  z-index: 2;
  cursor: text;
  text-indent: 30px;
  border: none;
  background: transparent;
  width: 100%;
  outline: 0;
}

/* Menu */
.app-inner {
  position: inherit !important;
  height: calc(100vh - 64px);
}

.menu-drawer {
  margin-top: 56px;
  padding-top: 8px;
}

.mat-drawer-transition .mat-drawer-content {
  transition-duration: 0.4s;
  transition-timing-function: cubic-bezier(0.25, 0.8, 0.25, 1);
  transition-property: transform, margin-left, margin-right;
}

.mat-list-option:hover,
.mat-nav-list .mat-list-item:hover {
  background-color: #ffffff;
}

.mat-nav-list .mat-list-item {
  cursor: pointer;
  outline: 0;
  display: inherit;
}

mat-nav-list.sub-menu {
  display: contents;
}

.mat-list-item.open > .mat-list-item-content {
  max-height: 2000px;
  background: mat-color(rgba(0, 0, 0, 0.04), "hover");
}

.mat-nav-list a {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 48px;
  padding: 0 16px;
}

.sub-menu {
  padding-top: 0;
  overflow: hidden;
  transition: 0.5s max-height cubic-bezier(0.35, 0, 0.25, 1);
  max-height: 0;
  transform: translateZ(0) !important;
}

mat-list mat-list-item .mat-list-item-content,
mat-list a[mat-list-item] .mat-list-item-content,
mat-nav-list mat-list-item .mat-list-item-content,
mat-nav-list a[mat-list-item] .mat-list-item-content {
  font-size: 0.875rem !important;
}

/* Badges */
.mat-menu-item {
  font-size: 0.875rem !important;
}

.menu .mat-menu-item {
  line-height: 35px;
  height: 35px;
  padding: 0 15px;
}

.menu .mat-menu-item .material-icons {
  font-size: 18px;
  height: 18px;
  width: 15px;
}

/* Footer */
.footer {
  color: #4c4c4c;
  background: #f2f3f8;
  padding: 10px 20px;
  bottom: 0;
  right: 0;
  left: 0;
  height: 20px;
  margin-top: 20px;
}

/* Responsive Data table */
.mat-table {
  overflow: auto;
}

.mobile-label {
  display: none;
}

.mat-header-cell {
  padding-right: 10px;
  color: #646464;
  font-weight: 700;
}

.mat-cell,
.mat-footer-cell {
  padding-right: 10px;
  flex: 1;
  align-items: center;
  overflow: hidden;
  word-wrap: break-word;
  min-height: inherit;
  text-overflow: ellipsis;
  line-height: 1.5;
  font-family: "Roboto", "Helvetica Neue", "sans-serif";
}

/* Tabs */
.mat-tab-header {
  margin: 5px 5px 0 5px;
}

.mat-tab-group .mat-card {
  margin-top: 0;
}

.mat-tab-labels {
  background-color: #ffffff;
}

.mat-tab-label-active {
  color: #000000;
  opacity: 1 !important;
}

.mat-tab-body-wrapper .mat-card {
  box-shadow: none !important;
  padding: 10px 0 0 0 !important;
}

.mat-card [mat-card-widget] {
  height: auto;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-grid-row-align: center;
  -ms-flex-align: center;
  align-items: center;
  align-content: center;
  max-width: 100%;
  padding: 1.3rem;
}

.mat-card [mat-card-float-icon] {
  position: absolute;
  right: 15px;
  top: 50%;
  margin-top: -20px;
  width: 40px;
  height: 40px;
}

.mat-card [mat-card-widget] [mat-card-widget-title],
.mat-card [mat-card-widget] p {
  margin: 5px 0 0;
  padding: 0;
  line-height: 1.1 !important;
}

.card-widget.mat-card {
  padding: 10px 0;
}

.mat-card {
  margin: 0.33333333rem;
}

.card-widget.mat-card [mat-card-float-icon] {
  width: 90px;
  text-align: right;
}

.card-widget.mat-card [mat-card-float-icon] strong {
  clear: both;
  display: block;
}

.card-widget [mat-card-widget] {
  cursor: pointer;
}

.font-size-15em {
  font-size: 15em;
  margin: 0;
}

.error {
  color: #ffffff;
  text-align: center;
  margin: 50px 0;
}

.mat-badge-content {
  position: relative;
  top: 5px !important;
}

.mat-badge-medium.mat-badge-after .mat-badge-content {
  right: -10px !important;
}

.rating .mat-icon {
  margin-left: 15px;
  font-size: 14px;
}

.mat-progress-bar {
  position: fixed !important;
  z-index: 9999;
}

/* Dialog */
.mat-dialog-container {
  width: 750px !important;
  max-width: 100% !important;
  margin: 30px;
}

.mat-dialog-container h2 hr {
  margin-bottom: 20px;
  margin-top: 5px;
}

.cdk-global-overlay-wrapper {
  background: rgba(0, 0, 0, 0.288);
  pointer-events: auto;
}

/* Tree View */
.mat-tree {
  width: 100%;
  margin: 0.33333333rem;
  display: grid;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.range-label {
  font-size: 11px;
  color: #3f51b5;
  font-family: Roboto, Helvetica Neue, sans-serif;
}

/* Progress Bar */
.mat-progress-bar.mat-warn .mat-progress-bar-fill::after {
  background-color: #f44336 !important;
}

.mat-progress-bar.mat-warn .mat-progress-bar-buffer {
  background-color: #ffffff !important;
}

.mat-progress-bar.mat-warn .mat-progress-bar-background {
  background-color: #c5cae9 !important;
}

.mat-form .mat-card .mat-card-subtitle .mat-chip-list .mat-chip-list-wrapper {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  margin: 10px;
}

.mat-slider-thumb-label {
  transform: rotate(45deg) !important;
  border-radius: 50% 50% 0 !important;
}

.mat-slider-thumb {
  transform: scale(0) !important;
}

.mat-slider-thumb-label-text {
  opacity: 1 !important;
}

.font-md .mat-card-subtitle {
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 2);
}

.font-sm .mat-card-content {
  font-size: 14px;
  color: rgba(0, 0, 0, 1);
  font-weight: 400;
}

.font-lg {
  font-size: 20px;
}

.mat-form .mat-card .mat-card-title.margin-bottom-zero {
  margin-bottom: 0;
}

.mat-form .mat-card .mat-card-subtitle.detail-header {
  margin-left: 16px;
  margin-bottom: 0;
}

.truncate {
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.d-flex {
  display: flex !important;
}

.align-item-center {
  align-items: center !important;
}

.mat-form .mat-card .mat-card-actions.margin-left-10,
.mat-form .mat-card .mat-card-actions:last-child.margin-left-10 {
  margin-left: 10px;
}

.mat-error.login {
  margin-top: -10px;
  margin-bottom: 5px;
}

.mat-button-toggle {
  color: rgba(0, 0, 0, 0.87);
}

.warn {
  color: red;
}

.bg-sfl {
  background-color: #ff590a !important;
  color: #ffffff !important;
}

.bt-sfl {
  background-color: #e0e0e0;
}
.bt-sfl:hover {
  background-color: #c7c7c7;
}

.bt-flat {
  border: 0 none;
  background-color: transparent;
  color: #515151;
  margin-right: 5px !important;
  padding: 0 18px;
  cursor: pointer;
  border-radius: 2px;
  line-height: 34px;
  outline: 0;
}

.bt-flat:hover {
  background-color: #f3f3f3;
}

.success {
  color: green;
}

.mat-form-field-subscript-wrapper {
  font-size: 100%;
  margin-top: 0.66667em;
  top: calc(100% - 1.79167em);
}

table {
  counter-reset: section;
}

.count:before {
  counter-increment: section;
  content: counter(section);
}

.ml-1 {
  margin-left: 1rem !important;
}
.ml-5 {
  margin-left: 5rem !important;
}

.p-2 {
  padding: 2rem !important;
}

.p-10 {
  padding: 10px;
}

.p-25 {
  padding: 25px !important;
}

.menu-Item-name {
  font-size: 16px;
  font-weight: 600;
}

.user-char {
  vertical-align: baseline !important;
}

.profile-btn {
  border-radius: 50% !important;
  width: 40px;
  height: 40px;
  background-color: #ff590a;
  color: #f1f3f4;
  font-size: 25px;
  min-width: 40px !important;
  padding: 0 !important;
  &:hover {
    background-color: #ff590a;
  }
}

.mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-bar {
  background-color: #f5b5a4;
}
.mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-thumb {
  background-color: #ff590a;
}

.mat-slide-toggle.mat-checked .mat-ripple-element {
  background-color: #f5b5a4;
}

.link {
  color: #f37d3f;
}

.w-auto {
  width: auto;
}

.w-max-content {
  width: max-content;
}

.w-100 {
  width: 100%;
}

.w-60px {
  width: 60px !important;
}

.w-100px {
  width: 100px;
}

.w-250px {
  width: 250px;
}

.v-align-middle {
  vertical-align: middle !important;
}

.vertical-divider {
  height: 95%;
  border: 1px solid #eaeaea;
}

.login-head {
  text-align: center;
}
.login-head h3 {
  margin-bottom: 5px;
  margin-top: 0;
}
.mat-focused .mat-form-field-label {
  color: #ff7e3a !important;
}

.mat-form-field-ripple {
  background-color: #ff7e3a !important;
}
.mat-header-row {
  background-color: #efefef;
}

.table-mat-tooltip {
  font-size: 14px !important;
  margin-top: -2px !important;
}

.filter-clear-icon {
  font-size: 1.5em !important;
}

.profile-img-card {
  width: 110px;
  height: 110px;
  border-radius: 8px;
  background-color: #ffffff;
}
.profile-image {
  width: 110px;
  height: 110px;
  border-radius: 8px;
  object-fit: cover;
}
.profile-upload-overlay {
  display: inline-block;
  position: absolute;
  top: 0;
  left: 0;
  height: 110px;
  width: 110px;
  text-align: center;
  border-radius: 8px;
  line-height: 120px;
  background-color: rgba(255, 255, 255, 0.8);
  color: #000000;
  transition: all 0.3s ease;
  opacity: 0;
  &:hover {
    opacity: 0.9;
  }
}

.profile-upload-icon {
  font-size: 48px !important;
  height: 48px !important;
  width: 48px !important;
  color: #ff590a;
}
.profile-upload-text {
  display: inline-block;
  margin: 0 auto !important;
  color: #ff590a;
  position: absolute;
  top: 15px;
  left: 15%;
  font-size: 12px;
}

.table-font-color {
  color: #515151;
}

.profile-title-name {
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  margin: 0 !important;
  color: #ff9554;
}

.profile-icon-text {
  margin-top: 15px;
  & > mat-icon {
    color: #505050;
    font-size: 22px;
    width: 22px;
    height: 22px;
    line-height: 22px;
  }
  & > span {
    position: relative;
    margin-left: 7px;
  }
}

.d-none {
  display: none !important;
}
.d-inline-block {
  display: inline-block;
}

.mt-40 {
  margin-top: 40px !important;
}

.mat-form-field.search-header {
  font-size: 14px;
  color: #0000008f;
  margin-top: 1.2rem;
}
.search-header .mat-form-field-underline {
  display: none !important;
}
.search-header .mat-form-field-flex {
  border-radius: 8px !important;
  background-color: #f1f3f4;
}

.modal-delete-btn {
  color: red;
  float: right;
}

.circle-image {
  border-radius: 50%;
  padding-top: 5px;
  height: 150px;
  width: 150px;
  padding-bottom: 5px;
  align-self: center;
}

.award-detail-table {
  padding-left: 40px;
}

.award-details-text {
  font: 16px Roboto, "Helvetica Neue", sans-serif;
  position: relative;
  padding-left: 10%;
}

.details-text {
  width: 120px;
  height: 30px;
}

.m-0 {
  margin: 0 !important;
}

.cursor-pointer {
  cursor: pointer;
}

.wd-fit-content {
  width: fit-content !important;
}

.h-fit-content {
  height: fit-content !important;
}

.award-image {
  width: -webkit-fill-available;
  justify-content: center;
}

.no-award-image {
  width: unset;
  height: 150px;
  margin: 20px auto 0;
  font-size: 18px;
}

.add-award-button {
  width: -webkit-fill-available;
  margin-right: 0 !important;
}

.pt-10 {
  padding-top: 10px !important;
}

.child-padding {
  padding-top: 0 !important;
  padding-left: 13px !important;
}

.main-menu-group {
  border-left: 0 !important;
}

mat-icon.icon-padding {
  padding: 0 14px !important;
  font-size: 8px !important;
  height: 8px !important;
  width: 8px !important;
}

.mat-nav-list .mat-list-item.admin-list-item:hover {
  background: #f8f8f8 !important;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.admin-side-bar.mat-list-item.ng-star-inserted.open a.relative.main-menu-group.item-color.ng-star-inserted {
  font-weight: bold;
}

mat-list-item .active {
  color: #ff6106 !important;
}

.body-content {
  margin: 1%;
  padding: 15px;
  border-radius: 2px;
  background-color: #ffffff;
  -webkit-box-shadow: 0 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12);
  box-shadow: 0 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12);
}

.mat-header-row {
  background-color: #fff;
}

.section-title-btn {
  width: 150px;
}

.pos-rel {
  position: relative;
  & > canvas {
    position: absolute;
    left: 0;
    top: 0;
    pointer-events: none;
  }
}

.chartAreaWrapper {
  width: 100%;
  overflow-x: auto;
}

.half-day-section {
  margin: 10px 0;
  font: 16px Roboto, "Helvetica Neue", sans-serif;
}

span.mat-placeholder-required.mat-form-field-required-marker {
  color: #ff0000 !important;
}

.h-min-10 {
  min-height: 10px;
}

$chip-status: (
  default: #eee,
  primary: #4fc3eb,
  alternative: #ef5565,
  alert: #5d9cec,
  danger: #ff6c51,
  warning: #ffcf55,
  success: #47af4a,
);

$chip-status: (
  default: #eee,
  primary: #4fc3eb,
  alternative: #ef5565,
  alert: #5d9cec,
  danger: #ff6c51,
  warning: #ffcf55,
  success: #47af4a,
);

.custom-chip {
  border-radius: 0.75em;
  display: inline-block !important;
  font-size: 11px;
  line-height: 1em;
  padding: 0.35em 0.75em;
  text-transform: uppercase;
  font-weight: bold;
  margin: 1px;
}

@function checkColorValue($color) {
  $redValue: red($color);
  $greenValue: green($color);
  $blueValue: blue($color);

  $l: 1 - (0.299 * $redValue + 0.587 * $greenValue + 0.114 * $blueValue) / 255;
  @if ($l < 0.4) {
    @return #000;
  } @else {
    @return #fff;
  }
}

@each $status, $bgcolor in $chip-status {
  .custom-chip-#{$status} {
    $color: $bgcolor;
    background: $color;
    color: checkColorValue($color);
  }
}

@for $i from 1 through 100 {
  .spacer-#{$i} {
    min-height: #{$i}px;
  }
}

.no-data {
  text-transform: uppercase;
  font-size: 24px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.card-counter {
  box-shadow: 2px 2px 10px #dadada;
  margin: 5px;
  padding: 20px 10px;
  background-color: #fff;
  height: 100px;
  border-radius: 5px;
  transition: 0.3s linear all;
  position: relative;
}

.card-counter:hover {
  box-shadow: 4px 4px 20px #dadada;
  transition: 0.3s linear all;
}
.card-counter.danger {
  background-color: #fff;
  color: orangered;
}

.card-counter .mat-icon {
  font-size: 5em;
  opacity: 0.2;
}

.card-counter .count-numbers {
  position: absolute;
  right: 35px;
  top: 20px;
  font-size: 30px;
  font-weight: bold;
  display: block;
  color: #000000;
}

.card-counter .count-name {
  position: absolute;
  right: 35px;
  top: 65px;
  font-style: italic;
  text-transform: capitalize;
  display: block;
  font-size: 18px;
  color: #000000;
}

.py-1 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.px-1 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.mat-spinner {
  margin: 0 auto;
}

.h-100 {
  height: 100%;
}

.kudos-image {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.sfl-card {
  padding: 5px;
}

.header-divider {
  margin: 0 -15px;
  display: block;
}

.question-card {
  box-shadow: none !important;
  border-radius: 4px !important;
  border: 1px dashed #0000001f;
  min-width: 50%;
  overflow-y: auto;
}

.questions-text {
  font-size: 17px;
  line-height: 1.3;
  color: #333;
  white-space: pre-wrap;
  -ms-word-wrap: break-word;
  word-wrap: break-word;
  margin-bottom: 3px;
  padding-right: 10px !important;
}

.question-number {
  width: 20px;
  display: inline-block;
}

.question-ml {
  margin-left: 20px;
}

.question-hint {
  color: #666;
  line-height: 1.4;
  white-space: pre-wrap;
  font-style: italic;
  word-wrap: break-word;
  margin-top: 5px;
}

.question-required-notice-star::after {
  color: red;
  font-size: 17px;
  content: " * ";
  font-weight: 700;
}

.generated-edit-text {
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  width: 100%;
  height: 100%;
  line-height: 1.5;
  font-size: 14px;
  padding: 1em 0;
  text-align: center;
  background-color: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  &:hover {
    opacity: 1;
  }
}

.form-response-table {
  margin: 1rem auto;
  width: 60%;
  min-width: 550px;
  background: #fff;
  padding: 0 15px;
}

.form-response-text-field .mat-form-field-required-marker {
  display: none;
}

.menu-header-text {
  margin-left: 1rem !important;
  font-size: 0.8rem !important;
  font-weight: bold !important;
}

.filter-input {
  width: 160px;
}

.filter-button-group {
  width: 200px;
}

.time-sch-item-content {
  white-space: pre !important;
}

i.material-icons.info-btn {
  color: #ff590a;
  cursor: pointer;
  width: 10%;
  text-align: right;
}

.dialog-header h2 {
  width: 90%;
  float: left;
}

.sfl-kudos-purpose {
  word-break: break-word;
}

.time-sch-item-1 {
  background-color: #f28c5a !important;
}
.time-sch-item-2 {
  background-color: #f07c42 !important;
}
.time-sch-item-3 {
  background-color: #ee6c2b !important;
}
.time-sch-item-4 {
  background-color: #ed6725 !important;
}
.time-sch-item-5 {
  background-color: #d45211 !important;
}
.time-sch-item-6 {
  background-color: #a5400d !important;
}
.time-sch-item-7 {
  background-color: #762e0a !important;
}
.time-sch-item-8 {
  background-color: #5e2508 !important;
}

.no-data-found-text {
  color: #8e8e8e;
  text-align: center;
}

@media (max-width: 450px) {
  .truncate {
    max-width: 200px;
    display: inline-block;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .mat-header .mat-card-title {
    font-size: 20px;
  }

  .mat-cell,
  .mat-footer-cell {
    flex: 1;
    display: inline-block;
    max-width: 290px;
    white-space: nowrap;
    line-height: 1.5;
    word-wrap: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
    font-family: "Roboto", "Helvetica Neue", "sans-serif";
  }

  .mat-form .mat-card .mat-card-subtitle .mat-chip-list .mat-chip-list-wrapper {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
    margin: 10px;
  }

  .mat-form .mat-card .info .mat-card-title {
    padding-left: 1rem;
    padding-right: 1rem;
    line-height: 1;
    font-size: 16px;
    font-weight: 400;
    display: -ms-grid;
    display: grid;
  }

  .mat-form .mat-card .info .mat-card-subtitle {
    margin-bottom: 0;
  }

  .profile-img-card {
    margin-left: 0 !important;
  }

  .profile-title-name {
    margin: 0 !important;
    font-size: 1.5rem !important;
  }

  .question-ml {
    margin-left: auto;
  }
}

@media (max-width: 1170px) {
  .footer {
    height: 40px;
  }

  .footer .float-left,
  .footer .float-right {
    float: none !important;
    text-align: center;
  }

  .mat-header {
    margin: 0;
  }

  .list-grid {
    margin-top: 10px;
  }

  .vertical-divider {
    display: none;
  }

  .app-inner {
    height: calc(100vh - 54px);
  }
}

@media (max-width: 786px) {
  .ml-5 {
    margin-left: 1rem !important;
  }
  .mobile-label {
    width: 130px;
    display: inline-block;
    font-weight: bold;
  }

  .mat-header-row {
    display: none;
  }

  mat-cell:first-child,
  mat-footer-cell:first-child,
  mat-header-cell:first-child {
    padding-left: 0 !important;
  }

  mat-cell {
    min-height: auto !important;
    padding: 5px 0;
  }

  .mat-row {
    flex-direction: column;
    align-items: start;
    padding: 5px 24px;
    min-height: auto !important;
  }

  .truncate {
    display: inline-block;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .mat-cell,
  .mat-footer-cell {
    flex: 1;
    line-height: 1.5;
    word-wrap: break-word;
    overflow: hidden;
    display: inline-block;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-family: "Roboto", "Helvetica Neue", "sans-serif";
  }
}

@media (max-width: 1366px) {
  .mat-drawer-content > .ps {
    height: 89%;
    min-height: 89%;
  }
}

.title-font-size {
  font-size: 14px !important;
}
