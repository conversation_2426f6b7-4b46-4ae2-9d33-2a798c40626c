<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Kernel | Sunflower Lab</title>
    <base href=/portal />
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,700|Material+Icons" rel="stylesheet" />
    <link rel="icon" type="image/x-icon" href="favicon.ico" />
    <style type="text/css">
      @-webkit-keyframes loader-spin {
        0% {
          -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
        }

        100% {
          -webkit-transform: rotate(360deg);
          transform: rotate(360deg);
        }
      }

      @keyframes loader-spin {
        0% {
          -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
        }

        100% {
          -webkit-transform: rotate(360deg);
          transform: rotate(360deg);
        }
      }

      .loader {
        position: absolute;
        margin: -18px 0 0 -18px;
        border: 3.6px solid #ff974d;
        box-sizing: border-box;
        overflow: hidden;
        width: 36px;
        height: 36px;
        left: 50%;
        top: 50%;
        animation: loader-spin 2s linear infinite reverse;
        -webkit-filter: url(#goo);
        filter: url(#goo);
        box-shadow: 0 0 0 1px #ff974d inset;
      }

      .loader:before {
        content: "";
        position: absolute;
        -webkit-animation: loader-spin 2s cubic-bezier(0.59, 0.25, 0.4, 0.69) infinite;
        animation: loader-spin 2s cubic-bezier(0.59, 0.25, 0.4, 0.69) infinite;
        background: #ff974d;
        -webkit-transform-origin: top center;
        transform-origin: top center;
        border-radius: 50%;
        width: 150%;
        height: 150%;
        top: 50%;
        left: -12.5%;
      }
    </style>
    <script>
      var global = global || window;
      var Buffer = Buffer || [];
      var process = process || {
        env: { DEBUG: undefined },
        version: [],
      };
    </script>
  </head>

  <body>
    <sfl-app-main>
      <div class="loader">
        <svg>
          <defs>
            <filter id="goo">
              <feGaussianBlur in="SourceGraphic" stdDeviation="2" result="blur" />
              <feColorMatrix in="blur" mode="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 5 -2" result="gooey" />
              <feComposite in="SourceGraphic" in2="gooey" operator="atop" />
            </filter>
          </defs>
        </svg>
      </div>
    </sfl-app-main>
  </body>
</html>
