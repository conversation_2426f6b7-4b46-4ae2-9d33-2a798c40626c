import {Injectable} from '@angular/core';
import {createRequestOption, HttpClientService, PageableQuery} from '../../shared';
import {AppConfig} from '../../app.config';
import {FilterGeneratedForm, FormResponse, GenerateForm, SflFormsModel} from './sfl-forms.model';


@Injectable({
  providedIn: 'root'
})
export class SflFormsService {

  constructor(private http: HttpClientService) { }

  getCreatedForms() {
    return this.http.get(AppConfig.CREATE_FORM);
  }

  getCreatedFormsById(id: string) {
    return this.http.get(AppConfig.CREATE_FORM + id);
  }

  createForm(sflFormsModel: SflFormsModel) {
    return this.http.post(AppConfig.CREATE_FORM, sflFormsModel);
  }

  updateFormGenerator(formId: string, generator: string[]) {
    return this.http.patch(AppConfig.CREATE_FORM + formId, generator);
  }

  deleteCreatedForm(id: string) {
    return this.http.delete(AppConfig.CREATE_FORM + id);
  }

  checkFormType(formType: string) {
    return this.http.get(AppConfig.CREATE_FORM_TYPE + formType);
  }

  getFilteredGeneratedForms(filterGeneratedForm: FilterGeneratedForm, pageableObject: PageableQuery) {
    return this.http.post(AppConfig.FILTER_GENERATED_FORMS, filterGeneratedForm, {
      params: createRequestOption(pageableObject)
    });
  }

  createGenerateForm(generateForm: GenerateForm) {
    return this.http.post(AppConfig.GENERATED_FORM, generateForm);
  }

  updateGenerateForm(generateForm: GenerateForm) {
    return this.http.put(AppConfig.GENERATED_FORM + generateForm.id, generateForm);
  }

  deleteGeneratedForm(id: string) {
    return this.http.delete(AppConfig.GENERATED_FORM + id);
  }

  getCreatedFormsForProject(projectId: number, userId: number) {
    return this.http.get(AppConfig.CREATE_FORM_FOR_PROJECT + projectId + '/' + userId);
  }

  addFormResponse(formResponse: FormResponse) {
    return this.http.post(AppConfig.FORM_RESPONSE, formResponse);
  }

  getFormsResponse(formId: string) {
    return this.http.get(AppConfig.GENERATE_FORM_RESPONSE + formId);
  }

  getFormResponseById(responseId: string) {
    return this.http.get(AppConfig.FORM_RESPONSE + responseId);
  }

}
