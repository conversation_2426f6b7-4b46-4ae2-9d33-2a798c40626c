import { Component, OnInit, Inject, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA, MatTableDataSource, MatSort, MatPaginator } from '@angular/material';
import { Subscription } from 'rxjs';
import { ProjectService } from '../project.service';
import { ProjectEstHoursDTO, ProjectIdDialogData } from '../project.model';

@Component({
  selector: 'sfl-project-workhours-modal',
  templateUrl: './project-workhours-model.component.html'
})
export class WorkHoursComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  projectEstHoursDTO = new ProjectEstHoursDTO();
  isEdit = false;

  constructor(
    public dialogRef: MatDialogRef<WorkHoursComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ProjectIdDialogData,
    private projectService: ProjectService
  ) { }

  ngOnInit() {
    this.subscription.add(this.projectService.getProjectEstHours(this.data.projectId).subscribe((res: ProjectEstHoursDTO) => {
      if (res) {
        this.projectEstHoursDTO = res;
        this.isEdit = true;
      } else {
        this.projectEstHoursDTO.projectId = this.data.projectId;
      }
    }));
  }

  closeModal(): void {
    this.dialogRef.close();
  }

  saveWorkHours() {
    if (this.isEdit) {
      this.subscription.add(this.projectService.updateProjectEstHours(this.projectEstHoursDTO).subscribe(() => {
        this.closeModal();
      }));
    } else {
      this.subscription.add(this.projectService.saveProjectEstHours(this.projectEstHoursDTO).subscribe(() => {
        this.closeModal();
      }));
    } 
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
