import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { RouterModule } from '@angular/router';
import { SharedModule } from '../../shared/shared.module';
import { ReportDashboardComponent } from './index';
import { dashboardRoutes } from './report-dashboard.route';
import {ReportDashboardService} from './report-dashboard.service';
import { ProjectService } from '../project/project.service';
import {DatePipe} from '@angular/common';
import {EmployeeManagementService} from '../employee-management/employee-management.service';
import {WorklogService} from '../worklog/worklog.service';
import {ClientService} from '../client/client.service';


@NgModule({
  imports: [
    RouterModule.forChild(dashboardRoutes),
    SharedModule
  ],
  declarations: [
    ReportDashboardComponent
  ],
  providers: [
    ReportDashboardService,
    ProjectService,
    EmployeeManagementService,
    WorklogService,
    ClientService,
    DatePipe
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})

export class ReportDashboardModule { }
