import { Component, OnInit, Inject, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA, MatTableDataSource, MatSort, MatPaginator } from '@angular/material';
import { Subscription } from 'rxjs';
import { ProjectService } from '../project.service';
import { WorkHoursReportDTO, WORK_HOURS_REPORT_COLUMNS, ProjectIdDialogData } from '../project.model';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'sfl-project-workhours-report-modal',
  templateUrl: './project-workhours-report-model.component.html'
})
export class WorkHoursReportComponent implements OnInit, OnDestroy {
  @ViewChild(MatSort, { static: false }) set setSort(sort: MatSort) {
    this.dataSource.sort = sort;
  }

  subscription = new Subscription();
  workHoursReportDTOs: WorkHoursReportDTO[] = [];
  displayedColumns = WORK_HOURS_REPORT_COLUMNS;
  dataSource = new MatTableDataSource<WorkHoursReportDTO>([]);
  showLoader = true;
  noDataFound = false;

  constructor(
    public dialogRef: MatDialogRef<WorkHoursReportComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ProjectIdDialogData,
    private projectService: ProjectService
  ) { }

  ngOnInit() {
    this.subscription.add(this.projectService.getWorkHoursReportByProjectId(this.data.projectId).subscribe((res: WorkHoursReportDTO[]) => {
        this.dataSource = new MatTableDataSource<WorkHoursReportDTO>(res);
        if(!this.dataSource.data){
          this.noDataFound = true;
        }
        this.showLoader = false;
      })
    );
  }

  closeModal(): void {
    this.dialogRef.close();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
