import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from "@angular/core";

import { MatPaginatorModule } from "@angular/material";
import { RouterModule } from "@angular/router";
import { SharedModule } from "src/app/shared/shared.module";
import { ClientService } from "../client/client.service";
import { ProjectService } from "../project/project.service";
import { TechnologyService } from "../technology/technology.service";
import { JobCodeModalComponent } from "./job-code-modal/job-code-modal.component";
import { JobCodeComponent } from "./job-code.component";
import { JobCodeRoutes } from "./job-code.route";
import { JobCodeService } from "./job-code.service";

@NgModule({
  imports: [RouterModule.forChild(JobCodeRoutes), SharedModule, MatPaginatorModule],
  declarations: [JobCodeComponent, JobCodeModalComponent],
  entryComponents: [JobCodeModalComponent],
  providers: [ProjectService, TechnologyService, ClientService, JobCodeService],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class JobCodeModule {}
