import { Injectable } from '@angular/core';
import { AppConfig } from '../../app.config';
import { HttpClientService } from '../../shared';
import { EmployeeTechnologies } from './employee-technologies.model';

@Injectable({
  providedIn: 'root'
})
export class EmployeeTechnologyService {
  constructor(private readonly http: HttpClientService) {}

  addEmployeeTechnology(uaaUserTechnologyDTO: EmployeeTechnologies) {
    return this.http.post(AppConfig.EMPLOYEE_TECHNOLOGIES, uaaUserTechnologyDTO);
  }

  getEmployeeTechnology() {
    return this.http.get(AppConfig.GET_EMPLOYEE_TECHNOLOGY);
  }

  updateEmployeeTechnology(uaaUserTechnologyDTO: EmployeeTechnologies) {
    return this.http.put(AppConfig.EMPLOYEE_TECHNOLOGIES, uaaUserTechnologyDTO);
  }
}
