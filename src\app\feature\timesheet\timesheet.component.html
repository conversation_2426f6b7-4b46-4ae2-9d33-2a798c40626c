<div class="body-content" fxLayout="column" fxFlex="100">
  <div fxFlex fxLayout="row" fxLayoutAlign="space-between" class="mb-1">
    <mat-card-title class="p-10">Time Sheet</mat-card-title>
    <div>
      <button mat-raised-button class="bt-sfl mr-10px" *ngIf="hasAdminPermission()" [disabled]="isExportable()" (click)="exportTimeSheet()">
        Export TimeSheet
        <mat-progress-spinner *ngIf="isExporting" [diameter]="20" [strokeWidth]="3" mode="indeterminate" style="display: inline-block"></mat-progress-spinner>
      </button>
      <button mat-raised-button class="bt-sfl" style="margin-left: auto" (click)="onClickIndividualExport()">Individual Export</button>
    </div>
  </div>
  <hr class="header-divider" />
  <div fxLayout="row column" fxLayoutAlign="start center" fxFlex="100" class="sfl-card">
    <mat-card-actions class="filter-input ml-1 mr-1">
      <mat-form-field>
        <mat-select [(ngModel)]="monthOrWeek" (selectionChange)="setRangeDates()">
          <mat-option value="1"> Month </mat-option>
          <mat-option value="2"> Week </mat-option>
        </mat-select>
      </mat-form-field>
    </mat-card-actions>

    <mat-card-actions fxLayout="row" fxLayoutAlign="space-between center" class="filter-input ml-1 mr-1">
      <button mat-icon-button (click)="previousRange()"><mat-icon aria-hidden="false" aria-label="Example home icon">arrow_back_ios</mat-icon></button>
      <mat-form-field class="date-range cursor-pointer">
        <span (click)="sDate.open()">{{ dateRangeStr }}</span>
        <input
          style="display: none"
          matInput
          name="startDate"
          aria-label="start-date"
          (focus)="sDate.open()"
          (click)="sDate.open()"
          [matDatepicker]="sDate"
          [(ngModel)]="currentDate"
          (ngModelChange)="setRangeDates()"
        />
        <mat-datepicker #sDate></mat-datepicker>
      </mat-form-field>
      <button mat-icon-button (click)="nextRange()"><mat-icon aria-hidden="false" aria-label="Example home icon">arrow_forward_ios</mat-icon></button>
    </mat-card-actions>

    <div class="ml-5 w-100 job-code" *ngIf="!(filterParam.employeeId || filterParam.technologyId || filterParam.projectId)" fxLayout="row" fxLayoutAlign="start center">
      <mat-form-field class="ml-1 mr-1">
        <input matInput placeholder="Add Job Code" aria-label="jobCode" [matAutocomplete]="auto" #jobCode [(ngModel)]="selectedJobCodeString" (ngModelChange)="onStateChange($event)" />
        <mat-autocomplete (optionSelected)="onJobCodeAdd()" #auto="matAutocomplete">
          <mat-option *ngFor="let jobCode of filteredJobCode" [value]="jobCode.name">
            <span>{{ jobCode.displayName }}</span>
          </mat-option>
        </mat-autocomplete>
      </mat-form-field>
    </div>

    <button mat-raised-button class="bt-sfl" style="margin-left: auto" (click)="goToToday()">Go to Today</button>
  </div>

  <div class="d-flex">
    <mat-card-actions class="filter-input ml-1 mr-1" *ngIf="hasAdminPermission() && technologies.length">
      <mat-form-field>
        <mat-select placeholder="Select Technology" name="technology" (ngModelChange)="setFilterParam(FilterName.TECHNOLOGY)" [(ngModel)]="filterParam.technologyId" #technology="ngModel">
          <mat-option *ngFor="let technology of technologies" [value]="technology?.technologyId">{{ technology?.technologyName }}</mat-option>
        </mat-select>
        <button mat-icon-button matSuffix class="float-center" matTooltip="clear" *ngIf="filterParam.technologyId" (click)="resetFilter(FilterName.TECHNOLOGY)">
          <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
        </button>
      </mat-form-field>
    </mat-card-actions>

    <mat-card-actions class="filter-input ml-1 mr-1" *ngIf="hasAdminPermission() && users.length">
      <mat-form-field>
        <mat-select placeholder="Select Employee" name="employeeId" (ngModelChange)="setFilterParam(FilterName.EMPLOYEE)" [(ngModel)]="filterParam.employeeId" #employeeId="ngModel">
          <mat-option *ngFor="let user of users" [value]="user.id">{{ user?.name }}</mat-option>
        </mat-select>
        <button mat-icon-button matSuffix class="float-center" matTooltip="clear" *ngIf="filterParam.employeeId" (click)="resetFilter(FilterName.EMPLOYEE)">
          <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
        </button>
      </mat-form-field>
    </mat-card-actions>

    <mat-card-actions class="filter-input ml-1 mr-1" *ngIf="hasAdminPermission() && projects.length">
      <mat-form-field>
        <mat-select placeholder="Select Project" name="projectId" (ngModelChange)="setFilterParam(FilterName.PROJECT)" [(ngModel)]="filterParam.projectId">
          <mat-option *ngFor="let project of projects" [value]="project?.id">{{ project?.name }}</mat-option>
        </mat-select>
        <button mat-icon-button matSuffix matTooltip="clear" *ngIf="filterParam.projectId" (click)="resetFilter(FilterName.PROJECT)">
          <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
        </button>
      </mat-form-field>
    </mat-card-actions>

    <mat-card-actions class="filter-input ml-1 mr-1 d-flex reset-filter" *ngIf="hasAdminPermission() && (filterParam.employeeId || filterParam.technologyId || filterParam.projectId)">
      <button class="bt-flat" type="button" (click)="resetFilter(FilterName.ALL)">Reset Filter</button>
    </mat-card-actions>
  </div>
  <div class="sfl-card">
    <div class="p-25" fxLayoutAlign="center center" fxFlex="100" *ngIf="showLoader">
      <mat-progress-spinner color="warn" mode="indeterminate"></mat-progress-spinner>
    </div>
    <div *ngIf="!showLoader && noDataFound" class="w-100 mt-40 no-data-found-text">No Worklogs Found.</div>
    <div class="w-100" *ngIf="!showLoader && !noDataFound">
      <div class="table-container" *ngFor="let user of workLogList">
        <ng-container *ngIf="user.timeTrack.length">
          <table class="w-auto" mat-table class="time-sheet" [dataSource]="getDataSource(user)">
            <ng-container matColumnDef="jobCodeName">
              <mat-header-cell *matHeaderCellDef fxFlex="30" class="jobcode-cell"> Job Code </mat-header-cell>
              <mat-cell [style.height]="cellHeight" #headerCell class="link" *matCellDef="let element" fxFlex="30" class="jobcode-cell"
                ><div fxLayout="row" fxLayoutAlign="start center" style="width: 100%">
                  <mat-icon
                    class="pin cursor-pointer"
                    [ngStyle]="{ color: element.isPinned ? '#f37d3f' : 'grey' }"
                    (click)="changePinStatus(element)"
                    *ngIf="!(filterParam.technologyId || filterParam.employeeId || filterParam.projectId)"
                    mat-list-icon
                    >push_pin</mat-icon
                  ><span class="jobcode-display-name ml-1">{{ element.jobCodeDisplayName }}</span>
                </div>
              </mat-cell>
              <mat-footer-cell [style.height]="cellHeight" *matFooterCellDef="let element" fxFlex="30" class="jobcode-cell">
                {{ user.user.name }} &nbsp;
                <sub
                  >(
                  <ng-container *ngFor="let tech of user.technologyList; let last = last"> {{ tech.technologyName }}<span *ngIf="!last">, </span> </ng-container>
                  )</sub
                >
              </mat-footer-cell>
            </ng-container>

            <ng-container matColumnDef="totalSeconds" [matColumnDef]="totalSeconds">
              <mat-header-cell *matHeaderCellDef fxFlex="10"> Total Hours </mat-header-cell>
              <mat-cell [style.height]="cellHeight" class="totalHours" *matCellDef="let element" fxFlex="10">
                {{ element.totalWorkLogTime | convertSecondsToHour }}
              </mat-cell>
              <mat-footer-cell [style.height]="cellHeight" *matFooterCellDef="let element" fxFlex="10">{{ user.totalAllJobCodeLogTime | convertSecondsToHour }}</mat-footer-cell></ng-container
            >

            <ng-container *ngFor="let date of allDatesToDisplay; let i = index" [matColumnDef]="date">
              <mat-header-cell matTooltip="{{ getDDMMYYYY(allDates[i]) }}" [ngClass]="{ 'grey-bg': isWeekend(allDates[i]), 'orange-color': isToday(allDates[i]) }" *matHeaderCellDef fxFlex="10">
                {{ date }}
              </mat-header-cell>
              <mat-cell
                class="cursor-pointer"
                [ngClass]="{ 'grey-bg': isWeekend(allDates[i]) }"
                *matCellDef="let element"
                (click)="openModal(element, allDates[i])"
                [style.height]="cellHeight"
                fxFlex="10"
              >
                {{ element | displayTime : allDates[i] }}</mat-cell
              >
              <mat-footer-cell [style.height]="cellHeight" [ngClass]="{ 'grey-bg': isWeekend(allDates[i]), 'orange-color': isToday(allDates[i]) }" *matFooterCellDef="let element" fxFlex="10"
                >{{ getDailyTotal(user.user.id, allDates[i]) | convertSecondsToHour }}
              </mat-footer-cell></ng-container
            >

            <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
            <mat-row *matRowDef="let row; columns: displayedColumns"></mat-row>
            <mat-footer-row *matFooterRowDef="displayedColumns"></mat-footer-row>
          </table>
        </ng-container>
      </div>
    </div>
  </div>
</div>
