<h2 mat-dialog-title>Generate Form</h2>
<hr class="mb-1">

<mat-dialog-content>
  <form #generatedForm="ngForm">
    <mat-card-content>

      <mat-form-field>
        <mat-select placeholder="Form Name" name="createdFormId" [(ngModel)]="generateForm.createdFormId" #createdFormId="ngModel" required>
          <mat-option *ngFor="let createdForm of createdForms" [value]="createdForm.id">
            {{createdForm?.formName}} &nbsp; <i>Version &nbsp;{{createdForm?.version}}</i>
          </mat-option>
        </mat-select>
        <mat-error *ngIf="createdFormId.touched && createdFormId.invalid">
          <small class="mat-text-warn" *ngIf="createdFormId?.errors.required">Form Name is required.</small>
        </mat-error>
      </mat-form-field>

      <button *ngIf="generateForm.createdFormId" class="bt-flat mb-1" type="button" (click)="previewForm(generateForm.createdFormId)">
        Preview the form
      </button>

      <mat-form-field>
        <input matInput type="text" autocomplete="off" placeholder="Start Date" name="startDate" aria-label="start-date"
               [matDatepicker]="sDate" [max]="generateForm.endDate"
               (click)="sDate.open()" [(ngModel)]="generateForm.startDate" #startDate="ngModel" required>
        <mat-datepicker-toggle matSuffix [for]="sDate"></mat-datepicker-toggle>
        <mat-datepicker #sDate></mat-datepicker>
        <mat-error *ngIf="startDate.touched && startDate.invalid">
          <small class="mat-text-warn" *ngIf="startDate?.errors.required">Start Date is required.</small>
        </mat-error>
      </mat-form-field>

      <mat-form-field>
        <input matInput type="text" autocomplete="off" placeholder="End Date" name="endDate" aria-label="end-date"
               [matDatepicker]="eDate" [min]="generateForm.startDate"
               (click)="eDate.open()" [(ngModel)]="generateForm.endDate" #endDate="ngModel" required>
        <mat-datepicker-toggle matSuffix [for]="eDate"></mat-datepicker-toggle>
        <mat-datepicker #eDate></mat-datepicker>
        <mat-error *ngIf="endDate.touched && endDate.invalid">
          <small class="mat-text-warn" *ngIf="endDate?.errors.required">End Date is required.</small>
        </mat-error>
      </mat-form-field>

      <mat-form-field>
        <input matInput type="number" placeholder="Number of Sprint" name="sprint" aria-label="sprint"
               [(ngModel)]="generateForm.sprint" #sprint="ngModel" pattern="[1-9]{1}[0-9]*" required>
        <mat-error *ngIf="sprint.touched && sprint.invalid">
          <small class="mat-text-warn" *ngIf="sprint?.errors.required">Sprint Number is required.</small>
          <small class="mat-text-warn" *ngIf="sprint?.errors.pattern">Should be a number.</small>
        </mat-error>
      </mat-form-field>

      <mat-form-field>
        <mat-chip-list #fillableList>
          <mat-chip *ngFor="let fillable of generateForm.fillableBy" (removed)="removeGenerator(fillable)">
            {{fillable | replaceString: '-' | titlecase}}
            <mat-icon matChipRemove>cancel</mat-icon>
          </mat-chip>
          <input name="generators" placeholder="Who can fill this form" aria-label="generator" [matAutocomplete]="auto" [matChipInputFor]="fillableList">
        </mat-chip-list>
        <mat-autocomplete #auto="matAutocomplete" (optionSelected)="selectedGenerator($event)">
          <mat-option *ngFor="let generator of formGenerator" [value]="generator">
            {{generator | replaceString: '-' | titlecase}}
          </mat-option>
        </mat-autocomplete>
      </mat-form-field>
    </mat-card-content>
  </form>
</mat-dialog-content>
<hr>

<mat-dialog-actions fxLayoutAlign="end">
  <button class="bt-flat" type="submit" (click)="closeDialog()">Cancel</button>
  <button mat-raised-button class="bt-sfl" type="submit" (click)="saveForm()"
          [style.cursor]="generatedForm.form.invalid ? 'not-allowed' : 'pointer'" [disabled]="generatedForm.form.invalid">
    Save
  </button>
</mat-dialog-actions>
