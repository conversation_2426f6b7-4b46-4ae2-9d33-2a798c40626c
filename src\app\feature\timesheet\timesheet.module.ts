import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from "@angular/core";
import { RouterModule } from "@angular/router";
import { SnackBarService } from "src/app/shared";
import { SharedModule } from "src/app/shared/shared.module";
import { JobCodeService } from "../job-code/job-code.service";
import { TechnologyService } from "../technology/technology.service";
import { AddEditWorklogComponent } from "./add-edit-worklog/add-edit-worklog.component";
import { TimesheetComponent } from "./timesheet.component";
import { TimeSheetRoutes } from "./timesheet.route";
import { TimesheetService } from "./timesheet.service";

@NgModule({
  imports: [RouterModule.forChild(TimeSheetRoutes), SharedModule],
  declarations: [TimesheetComponent, AddEditWorklogComponent],
  entryComponents: [AddEditWorklogComponent],
  providers: [JobCodeService, TimesheetService, TechnologyService, TechnologyService, SnackBarService],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class TimesheetModule {}
