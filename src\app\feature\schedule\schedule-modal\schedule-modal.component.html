<h2 mat-dialog-title>
  Schedule
  <mat-icon (click)="deleteSchedule(data.id)" *ngIf="scheduleModal.action === editTextLabel" matTooltip="Delete" class="modal-delete-btn cursor-pointer">delete</mat-icon>
</h2>
<hr class="mb-1" />

<mat-dialog-content>
  <form #projectForm="ngForm">
    <mat-card-content>
      <mat-form-field>
        <mat-select placeholder="Select Project" name="projectId" [(ngModel)]="data.projectId" #project="ngModel" required>
          <mat-option *ngFor="let project of projects" [value]="project.id">{{ project?.name }}</mat-option>
        </mat-select>
        <mat-error *ngIf="project.touched && project.invalid">
          <small class="mat-text-warn" *ngIf="project?.errors.required">Project is required.</small>
        </mat-error>
      </mat-form-field>

      <mat-form-field>
        <mat-select placeholder="Select Employee" name="uaaUserId" [(ngModel)]="data.uaaUserId" #user="ngModel" required>
          <mat-option *ngFor="let user of users" [value]="user.id">{{ user?.fullName }}</mat-option>
        </mat-select>
        <mat-error *ngIf="user.touched && user.invalid">
          <small class="mat-text-warn" *ngIf="user?.errors.required">User is required.</small>
        </mat-error>
      </mat-form-field>

      <mat-form-field>
        <mat-select placeholder="Select Technology" name="technology" [(ngModel)]="data.technologyDTOList" #technology="ngModel" multiple [compareWith]="compareFn" required>
          <mat-option *ngFor="let technology of technologies" [value]="technology">{{ technology?.name }}</mat-option>
        </mat-select>
        <mat-error *ngIf="technology.touched && technology.invalid">
          <small class="mat-text-warn" *ngIf="technology?.errors.required">Technology is required.</small>
        </mat-error>
      </mat-form-field>

      <mat-form-field>
        <input
          type="number"
          matInput
          placeholder="Per Day Hours"
          name="per_day_hours"
          aria-label="per-day-hours"
          #per_day_hours="ngModel"
          required
          [(ngModel)]="data.per_day_hours"
          (change)="data.total_hours = data.per_day_hours * getWorkingDays(data.toDate, data.fromDate)"
        />
        <mat-error *ngIf="per_day_hours.touched && per_day_hours.invalid">
          <small class="mat-text-warn" *ngIf="per_day_hours?.errors.required">Per day hours is required.</small>
        </mat-error>
      </mat-form-field>

      <mat-form-field>
        <input
          matInput
          placeholder="From date"
          name="fromDate"
          aria-label="from-date"
          (focus)="sDate.open()"
          (click)="sDate.open()"
          (dateChange)="data.total_hours = data.per_day_hours * getWorkingDays(data.toDate, data.fromDate)"
          [matDatepicker]="sDate"
          [matDatepickerFilter]="workingDaysFilter"
          [max]="data.toDate"
          [(ngModel)]="data.fromDate"
        />
        <mat-datepicker-toggle matSuffix [for]="sDate"></mat-datepicker-toggle>
        <mat-datepicker #sDate></mat-datepicker>
      </mat-form-field>

      <mat-form-field>
        <input
          matInput
          placeholder="To date"
          name="toDate"
          aria-label="to-date"
          (focus)="eDate.open()"
          (click)="eDate.open()"
          (dateChange)="data.total_hours = data.per_day_hours * getWorkingDays(data.toDate, data.fromDate)"
          [matDatepicker]="eDate"
          [matDatepickerFilter]="workingDaysFilter"
          [min]="data.fromDate"
          [(ngModel)]="data.toDate"
        />
        <mat-datepicker-toggle matSuffix [for]="eDate"></mat-datepicker-toggle>
        <mat-datepicker #eDate></mat-datepicker>
      </mat-form-field>

      <mat-form-field>
        <input type="number" matInput placeholder="Total Hours" name="total_hours" aria-label="hours" disabled readonly [(ngModel)]="data.total_hours" />
      </mat-form-field>

      <mat-form-field>
        <textarea placeholder="Note" name="note" matInput aria-label="note" maxlength="255" minlength="1" [(ngModel)]="data.note" (change)="data.note = data.note.trim()"></textarea>
      </mat-form-field>
    </mat-card-content>
  </form>
</mat-dialog-content>
<hr />

<mat-dialog-actions fxLayoutAlign="end">
  <button class="bt-flat" type="submit" (click)="closeDialog()">Cancel</button>
  <button mat-raised-button class="bt-sfl" type="submit" (click)="saveSchedule()" [disabled]="projectForm.form.invalid">Save</button>
</mat-dialog-actions>
