<div class="body-content" fxLayout="column" fxFlex="100">
  <div fxFlex fxLayout="row" fxLayoutAlign="space-between" class="mb-1">
    <mat-card-title class="p-10">Technology</mat-card-title>
    <button mat-raised-button class="bt-sfl" (click)="openSaveTechnology(null)" >Add New Technology</button>
  </div>

  <hr class="header-divider">

  <div class="sfl-card">
    <div class="p-25" fxLayoutAlign="center center" fxFlex="100" *ngIf="showLoader">
      <mat-progress-spinner color='warn' mode="indeterminate"></mat-progress-spinner>
    </div>

    <div *ngIf="!showLoader && noDataFound" class="w-100 mt-40 no-data-found-text">No Technology Found.</div>

    <div class="w-100" *ngIf="!showLoader && !noDataFound">
      <table class="w-auto" mat-table matSort matSortDisableClear
             [dataSource]="dataSource" [matSortActive]="pageable.sort" [matSortDirection]="pageable.direction"
             (matSortChange)="getSorting($event)">
        <ng-container matColumnDef="name">
          <mat-header-cell *matHeaderCellDef mat-sort-header> Technology Name </mat-header-cell>
          <mat-cell *matCellDef="let element">
            {{element?.name}}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="action">
            <mat-header-cell *matHeaderCellDef fxLayoutAlign="center center"> Action </mat-header-cell>
            <mat-cell *matCellDef="let element" fxLayoutAlign="center center">
                <button mat-icon-button (click)="openSaveTechnology(element)" ><mat-icon>edit</mat-icon></button>
                <button mat-icon-button (click)="openDeleteTechnology(element)" ><mat-icon>delete</mat-icon></button>
            </mat-cell>
          </ng-container>
        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
      </table>
      <mat-paginator [length]="technologyPageable.totalElements" [pageSizeOptions]="[10, 20, 25]" [pageIndex]="pageable.page" [pageSize]="pageable.size"
                     (page)="getPagination($event)" showFirstLastButtons></mat-paginator>
    </div>
  </div>

</div>
