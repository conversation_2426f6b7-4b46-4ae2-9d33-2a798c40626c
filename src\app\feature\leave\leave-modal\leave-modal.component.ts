import { Component, OnInit, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material';
import { LeaveComponent } from '../leave.component';
import { Router } from '@angular/router';
import {Leave, CalDates, DayHalf, LEAVE_TYPE_LIST} from '../leave.model';
import { LeaveService } from '../leave.service';
import { Employee } from '../../employee-management/employee-management.model';
import { Subscription } from 'rxjs';
import {Variable, DateUtils, SharedService} from '../../../shared';
import * as moment from 'moment-business-days';
import {EmployeeManagementService} from '../../employee-management/employee-management.service';

@Component({
  selector: 'sfl-update-leave',
  templateUrl: './leave-modal.component.html'
})
export class LeaveModalComponent implements OnInit {
  users: Employee[] = [];
  subscription: Subscription = new Subscription();
  leaveTypes: string[] = LEAVE_TYPE_LIST;
  leave: Leave = new Leave();
  calDates: CalDates = new CalDates();
  dayHalf = DayHalf;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: Leave,
    public dialogRef: MatDialogRef<LeaveComponent>,
    private router: Router,
    private sharedService: SharedService,
    private leaveService: LeaveService,
    private employeeService: EmployeeManagementService
  ) {}

  ngOnInit() {
    this.getUsers();
    if (this.data) {
      this.leave = this.data;
    }
  }

  getUsers() {
    this.subscription.add(this.employeeService.getEmployees().subscribe((res: Employee[]) => {
      this.users = res;
    }));
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  saveLeave() {
    this.leave.fromDate = moment(this.leave.fromDate).utcOffset(Variable.IST_OFFSET).set({hour: 19, minute: 30, second: 0}).format();
    this.leave.toDate = moment(this.leave.toDate).utcOffset(Variable.IST_OFFSET).set({hour: 23, minute: 59, second: 59}).format();
    if (!this.leave.halfDay) {
      this.leave.whichHalf = null;
    }

    if (this.data) {
      this.updateLeave();
    } else {
      this.addLeave();
    }
  }

  addLeave() {
    this.leave.uaaUserId = this.sharedService.getUserId();
    this.subscription.add(this.leaveService.createLeave(this.leave).subscribe(() => {
      this.leaveService.callMethodOfWorklogComponent();
      this.dialogRef.close();
    }));
  }

  updateLeave() {
    this.subscription.add(this.leaveService.updateLeave(this.leave).subscribe(() => {
      this.leaveService.callMethodOfWorklogComponent();
      this.dialogRef.close();
    }));
  }

  calculateLeaveDates(action: string) {
    if (this.leave.fromDate && (this.leave.toDate || this.leave.noOfDay || this.leave.halfDay)) {
      if (action === Variable.END__DATE || action === Variable.START__DATE) {
        this.leave.noOfDay = undefined;
      } else if (action === Variable.NO_OF_DAY) {
        this.leave.toDate = undefined;
      }

      this.calDates.fromDate = DateUtils.convertDate(this.leave.fromDate);
      this.calDates.toDate = DateUtils.convertDate(this.leave.toDate);
      this.calDates.noOfDay = this.leave.noOfDay;
      this.calDates.halfDay = this.leave.halfDay;

      this.subscription.add(
        this.leaveService.calculateEndDateAndNoOfDays(this.calDates).subscribe((res: CalDates) => {
          this.leave.fromDate = res.fromDate;
          this.leave.toDate = res.toDate;
          this.leave.noOfDay = res.noOfDay;
          this.leave.fromDate = DateUtils.convertStrToDate(this.leave.fromDate);
          this.leave.toDate = DateUtils.convertStrToDate(this.leave.toDate);
        })
      );
    }
  }

}
