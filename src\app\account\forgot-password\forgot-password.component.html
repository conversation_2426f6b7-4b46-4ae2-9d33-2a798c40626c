<div class="account-wrapper">
  <div class="account">
    <mat-card>
      <mat-card-content>
        <div class="login-head">
          <h3>Forgot Password</h3>
          <p class="sub-title-font-color">Enter your email and we'll send you instructions on how to reset your password.</p>
        </div>

        <div>
          <form fxFlex="100" fxLayout="row column">
            <div class="p-2" fxFlex.gt-lg="48" fxFlex.gt-md="48" fxFlex.gt-sm="100" fxFlex.gt-xs="100" fxLayout="row" fxLayoutAlign="center center">
              <img class="w-100" src="../../../assets/images/logo.png" alt="logo"/>
            </div>

            <div class="vertical-divider"></div>

            <div fxFlex.gt-lg="50" fxFlex.gt-md="50" fxFlex.gt-sm="100" fxFlex.gt-xs="100" class="w-100 p-2">
              <div fxLayout="column" fxLayoutAlign="space-around">
                <div class="pb-1">
                  <mat-form-field>
                    <input matInput type="email" placeholder="Email" name="email" aria-label="email" [(ngModel)]="email" #emailInput="ngModel" required>
                  </mat-form-field>
                  <mat-error *ngIf="emailInput.touched && emailInput.invalid">
                    <small class="mat-text-warn" *ngIf="emailInput?.errors.required">Email is required.</small>
                  </mat-error>
                </div>
                <button mat-raised-button color="primary" class="bg-sfl" type="submit" (click)="forgotPassword()">Reset Password</button>

                <div class="pt-1 pb-1 text-center">
                  <a [routerLink]="['/login']">Login to your account</a>
                </div>
              </div>
            </div>

          </form>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
