import { Routes } from '@angular/router';
import { ErrorComponent, OutsideLayoutComponent, SideNavBarComponent } from './layouts';
import { ROLES } from './shared';
import { AuthGuardService } from './shared/service/auth-guard.service';
import { RoleGuardService } from './shared/service/role-guard.service';

export const AppRoutes: Routes = [
  {
    path: '',
    component: OutsideLayoutComponent,
    loadChildren: () => import('./account/account.module').then(m => m.AccountModule)
  },
  {
    path: '',
    component: SideNavBarComponent,
    canActivate: [AuthGuardService],
    children: [
      {
        path: 'account',
        loadChildren: () => import('./account/account.module').then(m => m.AccountModule)
      },
      {
        path: 'dashboard',
        loadChildren: () => import('./feature/dashboard/dashboard.module').then(m => m.DashboardModule)
      },
      {
        path: 'daily-attendance',
        loadChildren: () => import('./feature/emp-daily-attendance/emp-daily-attendance.module').then(m => m.EmployeeAttendanceModule)
      },
      {
        path: 'profile',
        loadChildren: () => import('./feature/profile/profile.module').then(m => m.ProfileModule)
      },
      {
        path: 'kudos',
        loadChildren: () => import('./feature/kudos/kudos.module').then(m => m.KudosModule)
      },
      // {
      //   path: "worklog",
      //   loadChildren: () => import("./feature/worklog/worklog.module").then((m) => m.WorklogModule),
      // },
      {
        path: 'leave',
        loadChildren: () => import('./feature/leave/leave.module').then(m => m.LeaveModule)
      },
      {
        path: 'project',
        loadChildren: () => import('./feature/project/project.module').then(m => m.ProjectModule)
      },
      {
        path: 'report-dashboard',
        canActivate: [RoleGuardService],
        data: {
          allowedRoles: [ROLES.SUPER_ADMIN, ROLES.ADMIN]
        },
        loadChildren: () => import('./feature/report-dashboard/report-dashboard.module').then(m => m.ReportDashboardModule)
      },
      {
        path: 'employee-management',
        canActivate: [RoleGuardService],
        data: {
          allowedRoles: [ROLES.SUPER_ADMIN, ROLES.ADMIN, ROLES.HR]
        },
        loadChildren: () => import('./feature/employee-management/employee-management.module').then(m => m.EmployeeManagementModule)
      },
      {
        path: 'client',
        canActivate: [RoleGuardService],
        data: {
          allowedRoles: [ROLES.SUPER_ADMIN, ROLES.ADMIN, ROLES.LEAD]
        },
        loadChildren: () => import('./feature/client/client.module').then(m => m.ClientModule)
      },
      {
        path: 'technology',
        canActivate: [RoleGuardService],
        data: {
          allowedRoles: [ROLES.SUPER_ADMIN, ROLES.ADMIN, ROLES.LEAD]
        },
        loadChildren: () => import('./feature/technology/technology.module').then(m => m.TechnologyModule)
      },
      {
        path: 'employee-technologies',
        canActivate: [RoleGuardService],
        data: {
          allowedRoles: [ROLES.SUPER_ADMIN, ROLES.ADMIN, ROLES.LEAD]
        },
        loadChildren: () => import('./feature/employee-technologies/employee-technologies.module').then(m => m.EmployeeTechnologiesModule)
      },
      {
        path: 'schedule',
        canActivate: [RoleGuardService],
        data: {
          allowedRoles: [ROLES.SUPER_ADMIN, ROLES.ADMIN, ROLES.LEAD]
        },
        loadChildren: () => import('./feature/schedule/schedule.module').then(m => m.ScheduleModule)
      },
      {
        path: 'forms',
        canActivate: [RoleGuardService],
        data: {
          allowedRoles: [ROLES.SUPER_ADMIN, ROLES.ADMIN, ROLES.LEAD, ROLES.HR]
        },
        loadChildren: () => import('./feature/sfl-forms/sfl-forms.module').then(m => m.SflFormsModule)
      },
      {
        path: 'job-code',
        canActivate: [RoleGuardService],
        data: {
          allowedRoles: [ROLES.SUPER_ADMIN, ROLES.ADMIN, ROLES.LEAD, ROLES.HR]
        },
        loadChildren: () => import('./feature/job-code/job-code.module').then(m => m.JobCodeModule)
      },
      {
        path: 'time-sheet',
        loadChildren: () => import('./feature/timesheet/timesheet.module').then(m => m.TimesheetModule)
      },
      {
        path: 'reports',
        canActivate: [RoleGuardService],
        data: {
          allowedRoles: [ROLES.SUPER_ADMIN, ROLES.ADMIN]
        },
        loadChildren: () => import('./feature/reports/reports.module').then(m => m.ReportsModule)
      }
    ]
  },

  {
    path: '**',
    component: ErrorComponent
  }
];
