import { HttpClientModule } from "@angular/common/http";
import { NgModule } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { BrowserModule } from "@angular/platform-browser";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";
import { RouterModule } from "@angular/router";
import { NgxWebstorageModule } from "ngx-webstorage";
import { AppRoutes } from "./app.route";
import { MainComponent } from "./layouts";
import { LayoutsModule } from "./layouts/layouts.module";
import { SweetAlertService } from "./shared/service/sweetalert.service";
import { SharedModule } from "./shared/shared.module";

@NgModule({
  declarations: [MainComponent],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    RouterModule.forRoot(AppRoutes, { useHash: true }),
    NgxWebstorageModule.forRoot({ prefix: "SFL-demo", separator: "-" }),
    FormsModule,
    ReactiveFormsModule,
    HttpClientModule,
    SharedModule,
    LayoutsModule,
  ],
  exports: [],
  providers: [SweetAlertService],
  bootstrap: [MainComponent],
})
export class AppModule {}
