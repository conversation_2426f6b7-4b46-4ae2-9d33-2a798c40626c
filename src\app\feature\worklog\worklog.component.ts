import { Component, OnInit, <PERSON>Child, On<PERSON><PERSON>roy } from "@angular/core";
import { MatDialog, MatPaginator, MatTableDataSource, MatSort } from "@angular/material";
import { Subscription } from "rxjs";
import { PageableQuery, SharedService, Variable } from "../../shared";
import { WorklogService } from "./worklog.service";
import { FilterWorklog, Worklog, WorklogPageable, WORKLOG_DISPLAY_COLUMNS } from "./worklog.model";
import { Project } from "../project/project.model";
import { WorkTypes } from "./worklog.model";
import { DateUtils } from "../../shared";
import { SweetAlertService } from "../../shared/service/sweetalert.service";
import { WorklogModalComponent } from "./worklog-modal/worklog-modal.component";
import { ActivatedRoute } from "@angular/router";
import { finalize } from "rxjs/operators";
import { ProjectService } from "../project/project.service";

@Component({
  selector: "sfl-worklog",
  templateUrl: "./worklog.component.html",
})
export class WorklogComponent implements OnInit, OnDestroy {
  @ViewChild(MatSort, { static: false }) sort: MatSort;
  @ViewChild(MatPaginator, { static: false }) paginator: MatPaginator;

  showLoader = true;
  noDataFound = true;
  uaaUserId: number;
  displayedColumns: string[];
  projects: Project[] = [];
  workTypes: WorkTypes[] = [];
  worklog: Worklog[] = [];
  dateFormat = Variable.MM_DD_YYYY;
  dateWiseFilter: string[] = Variable.DATE_WISE_FILTER_LIST;
  filterWorklog: FilterWorklog = new FilterWorklog();
  pageable: PageableQuery = new PageableQuery();
  dataSource = new MatTableDataSource<Worklog>([]);
  subscription: Subscription = new Subscription();
  worklogPageable: WorklogPageable = new WorklogPageable();
  freeSearch = Variable.FREE_SEARCH.replace(" ", "");
  uptoYear = Variable.UPTO_YEAR.replace(" ", "");

  constructor(
    private matDialog: MatDialog,
    private sharedService: SharedService,
    private worklogService: WorklogService,
    private projectService: ProjectService,
    private alertService: SweetAlertService,
    private activatedRoute: ActivatedRoute
  ) {
    this.pageable.page = 0;
    this.pageable.size = 10;
    this.pageable.sort = Variable.Date;
    this.pageable.direction = Variable.DESC;
  }

  ngOnInit() {
    this.activatedRoute.queryParams.subscribe((params) => {
      if (params && params.addWorklog) {
        setTimeout(() => {
          this.openSaveWorklog(null);
        }, 0);
      }
    });
    this.uaaUserId = this.sharedService.getUserId();
    this.onLoadData();
    this.subscription.add(
      this.worklogService.invokeEvent.subscribe(() => {
        this.onLoadData();
      })
    );
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

  onLoadData() {
    this.getProjects();
    this.getWorkTypes();
    this.applyFilter();
  }

  openSaveWorklog(worklog: Worklog) {
    this.matDialog.open(WorklogModalComponent, {
      width: Variable.BOX_WIDTH_VALUE,
      data: worklog,
    });
  }

  getSorting(event) {
    this.pageable.sort = event.active;
    this.pageable.direction = event.direction;
    this.pageable.page = 0;
    this.applyFilter(false);
  }

  getPagination(event) {
    this.pageable.size = event.pageSize;
    this.pageable.page = event.pageIndex;
    this.applyFilter(false);
  }

  openDeleteWorklog(parameterValue: FilterWorklog) {
    const confirmation = this.alertService.deleteAlert(Variable.YOUR_WORKLOG);
    confirmation.then((value) => {
      if (value === true) {
        this.worklogService.deleteWorklog(parameterValue).subscribe(() => {
          this.onLoadData();
        });
      }
    });
  }

  applyFilter(set_page_zero = true) {
    this.pageable.page = set_page_zero ? 0 : this.pageable.page;
    this.showLoader = true;
    this.noDataFound = true;

    if (this.filterWorklog.filterBy !== this.freeSearch) {
      this.filterWorklog.startDate = undefined;
      this.filterWorklog.endDate = undefined;
    }
    this.filterWorklog.startDate = DateUtils.convertDate(this.filterWorklog.startDate);
    this.filterWorklog.endDate = DateUtils.convertDate(this.filterWorklog.endDate);
    const currentSort = this.pageable.sort;
    this.pageable.sort = this.pageable.sort + "," + this.pageable.direction;
    this.filterWorklog.uaaUserId = this.sharedService.getUserId();
    this.subscription.add(
      this.worklogService
        .getFilterWorklog(this.filterWorklog, this.pageable)
        .pipe(
          finalize(() => {
            this.noDataFound = this.dataSource.data.length <= 0;
            this.showLoader = false;
          })
        )
        .subscribe((res: WorklogPageable) => {
          this.worklogPageable = res;
          this.displayedColumns = WORKLOG_DISPLAY_COLUMNS;
          this.dataSource = new MatTableDataSource<Worklog>(this.worklogPageable.content);
          this.pageable.size = this.worklogPageable.pageable.pageSize;
          this.pageable.page = this.worklogPageable.pageable.pageNumber;
        })
    );
    this.pageable.sort = currentSort;

    this.filterWorklog.startDate = DateUtils.convertStrToDate(this.filterWorklog.startDate);
    this.filterWorklog.endDate = DateUtils.convertStrToDate(this.filterWorklog.endDate);
  }

  resetFilter(parameter: string) {
    this.filterWorklog[parameter] = undefined;
  }

  resetAllFilter() {
    this.filterWorklog = new FilterWorklog();
    this.applyFilter();
  }

  getProjects() {
    this.subscription.add(
      this.projectService.getActiveProjects().subscribe((res: Project[]) => {
        this.projects = res;
      })
    );
  }

  getWorkTypes() {
    this.subscription.add(
      this.worklogService.getWorkTypes().subscribe((res: WorkTypes[]) => {
        this.workTypes = res;
      })
    );
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
