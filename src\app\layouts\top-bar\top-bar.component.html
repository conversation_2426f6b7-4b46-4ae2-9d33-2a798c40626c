<mat-toolbar class="main-header">
  <div fxFlex="100" fxLayout="row">

    <div fxLayoutAlign="start center" fxFlex.gt-lg="30" fxFlex.gt-md="35" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
      <button (click)="toggleSidenav.emit()" mat-icon-button>
        <mat-icon>menu</mat-icon>
      </button>
      <div class="ml-1">
        <img class="logo" src="../../../assets/images/logo.png" alt="logo">
        <span class="logo-text"> KERNEL</span>
      </div>
    </div>

    <!-- Search -->
    <form fxLayoutAlign="center center" fxFlex="40">
      <mat-form-field appearance="fill" color="warn" class="search-header">
        <mat-icon matPrefix class="mr-10px">search</mat-icon>
        <mat-label>Search</mat-label>
        <input
          type="text"
          aria-label="Search"
          name="search"
          matInput
          [(ngModel)]="searchTerm"
          [matAutocomplete]="autoComplete"
          (input)="search()"
          #searchField
        />
        <mat-autocomplete #autoComplete="matAutocomplete" [displayWith]="null" (optionSelected)="goToView($event)">
          <mat-optgroup label="Project">
            <mat-option *ngFor="let project of projects" (click)="searchTerm = null" [value]="projectDetailsURL + project.id">
              {{project.name}}
            </mat-option>
            <mat-option *ngIf="projects.length === 0" [disabled]="true" class="text-center m-0">
              No Project Found
            </mat-option>
          </mat-optgroup>
          <mat-optgroup label="User">
            <mat-option *ngFor="let user of users" (click)="searchTerm = null" [value]="employeeProfileURL + user.id">
              {{user.firstName + ' ' + user.lastName}}
            </mat-option>
            <mat-option *ngIf="users.length === 0" [disabled]="true" class="text-center m-0">
              No User Found
            </mat-option>
          </mat-optgroup>
        </mat-autocomplete>
      </mat-form-field>
    </form>

    <div fxLayoutAlign="end center" fxFlex class="ml-1">
      <button mat-button class="bt-sfl profile-btn" [matMenuTriggerFor]="menu">
        <span class="user-char"> {{ userName | uppercase | slice: 0:1 }} </span>
      </button>
      <mat-menu #menu="matMenu">
        <button mat-menu-item [routerLink]="employeeProfileURL">
          <span class="menu-Item-name"> <mat-icon>account_circle</mat-icon> {{ userName | titlecase }} </span>
        </button>
        <button mat-menu-item [routerLink]="changePasswordURL">
          <span><mat-icon>vpn_key</mat-icon> Change Password</span>
        </button>

        <button mat-menu-item (click)="logout()">
          <span><mat-icon>exit_to_app</mat-icon> Log Out </span>
        </button>
      </mat-menu>
    </div>
  </div>
</mat-toolbar>
