<div class="body-content" fxLayout="column" fxFlex="100">
  <div fxFlex fxLayout="row" fxLayoutAlign="space-between" class="mb-1">
    <mat-card-title class="p-10">Projects</mat-card-title>
      <button mat-raised-button class="bt-sfl" (click)="saveProject(null)">Add New Project</button>
  </div>

  <hr class="header-divider">

  <form (ngSubmit)="applyFilter()">
    <div fxLayout="row column" fxLayoutAlign="start center" fxFlex="100" class="sfl-card">
      <mat-card-actions class="filter-input ml-1 mr-1">
        <mat-form-field>
          <mat-select placeholder="Select Project" name="projectName" [(ngModel)]="filterProject.projectName">
            <mat-option *ngFor="let project of projects" [value]="project.name">{{ project?.name }}</mat-option>
          </mat-select>
          <button
            mat-icon-button
            matSuffix
            class="float-center"
            matTooltip="clear"
            *ngIf="filterProject.projectName"
            (click)="resetFilter('projectName')"
          >
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1">
        <mat-form-field>
          <mat-select placeholder="Select Client Name" name="clientName" [(ngModel)]="filterProject.clientName">
            <mat-option *ngFor="let client of clients" [value]="client.clientName">{{ client?.clientName }} </mat-option>
          </mat-select>
          <button
            mat-icon-button
            matSuffix
            class="float-center"
            matTooltip="clear"
            *ngIf="filterProject.clientName"
            (click)="resetFilter('clientName')"
          >
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-input ml-1 mr-1">
        <mat-form-field>
          <mat-select placeholder="Select Project Status" name="projectStatus" [(ngModel)]="filterProject.projectStatus">
            <mat-option value="active">Active</mat-option>
            <mat-option value="inactive">InActive</mat-option>
          </mat-select>
          <button
            mat-icon-button
            matSuffix
            class="float-center"
            matTooltip="clear"
            *ngIf="filterProject.projectStatus"
            (click)="resetFilter('projectStatus')"
          >
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-button-group ml-1">
        <button mat-raised-button class="bt-sfl mr-10px" type="submit">Filter</button>
        <button class="bt-flat" type="button" (click)="resetAllFilter()">Reset All</button>
      </mat-card-actions>
    </div>
  </form>

  <div class="sfl-card">
    <div class="p-25" fxLayoutAlign="center center" fxFlex="100" *ngIf="showLoader">
      <mat-progress-spinner color="warn" mode="indeterminate"></mat-progress-spinner>
    </div>

    <div *ngIf="!showLoader && noDataFound" class="w-100 mt-40 no-data-found-text">No Project Found.</div>

    <div class="w-100" *ngIf="!showLoader && !noDataFound">
      <table
        class="w-auto"
        mat-table
        matSort
        matSortDisableClear
        [dataSource]="dataSource"
        [matSortActive]="pageable.sort"
        [matSortDirection]="pageable.direction"
        (matSortChange)="getSorting($event)"
      >
        <ng-container matColumnDef="name">
          <mat-header-cell *matHeaderCellDef mat-sort-header> Project Name </mat-header-cell>
          <mat-cell
            class="link cursor-pointer"
            *matCellDef="let element"
            [routerLink]="[projectDetailsURL, element?.id]"
          >
            {{ element?.name }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="active">
          <mat-header-cell *matHeaderCellDef fxFlex="10" mat-sort-header> Project Status </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10">
            <mat-slide-toggle (change)="updateProjectStatus(element?.id, $event)" name="active" [(ngModel)]="element.active">
            </mat-slide-toggle>
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="client.clientName">
          <mat-header-cell *matHeaderCellDef fxFlex="10" mat-sort-header> Client Name </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.clientDTO?.clientName | titlecase }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="description">
          <mat-header-cell *matHeaderCellDef fxFlex="10" mat-sort-header> Description </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10">
            <span matTooltipClass="table-mat-tooltip" [matTooltip]="element?.description">
              {{ element?.description }}
            </span>
          </mat-cell>
        </ng-container>

        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxFlex="10"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10">
            <button mat-icon-button (click)="saveProject(element)">
              <mat-icon>edit</mat-icon>
            </button>
          </mat-cell>
        </ng-container>

        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns"></mat-row>
      </table>
      <mat-paginator
        [length]="projectPageable.totalElements"
        [pageSizeOptions]="[10, 20, 25]"
        [pageIndex]="pageable.page"
        [pageSize]="pageable.size"
        (page)="getPagination($event)"
        showFirstLastButtons
      >
      </mat-paginator>
    </div>
  </div>

</div>
