import {Component, OnInit, Inject, OnD<PERSON>roy} from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { ClientComponent } from '../client.component';
import { Client } from '../client.model';
import { ClientService } from '../client.service';
import { HttpErrorResponse } from '@angular/common/http';
import { SnackBarService } from '../../../shared';
import { AppConfig } from '../../../app.config';


@Component({
  selector: 'sfl-add-client',
  templateUrl: './client-modal.component.html'
})
export class ClientModalComponent implements OnInit, OnDestroy {
  client: Client = new Client();
  subscription: Subscription = new Subscription();
  isEdit = false;

  constructor(
    public dialogRef: MatDialogRef<ClientComponent>,
    private router: Router,
    private clientService: ClientService,
    private snackBarService: SnackBarService,
    @Inject(MAT_DIALOG_DATA) public data: Client,
  ) { }

  ngOnInit() {
    if (this.data) {
      this.isEdit = true;
      this.client = this.data;
    }
  }

  saveClient() {
    if (this.isEdit) {
      this.updateClient();
    } else {
      this.addClient();
    }
  }

  addClient() {
    this.subscription.add(this.clientService.createClient(this.client).subscribe(() => {
      this.successClient();
    }, (error: HttpErrorResponse) => {
      this.errorClient(error);
    }));
  }

  updateClient() {
    this.subscription.add(this.clientService.updateClient(this.client).subscribe(() => {
      this.successClient();
    }, (error: HttpErrorResponse) => {
      this.errorClient(error);
    }));
  }

  successClient() {
    this.clientService.callMethodOfWorklogComponent();
    this.closeDialog();
    this.router.navigate([AppConfig._CLIENT]).then();
  }

  errorClient(error: HttpErrorResponse) {
    if (error && error.error && error.error.message) {
      this.snackBarService.error(error.error.message);
    }
    this.closeDialog();
    this.router.navigate([AppConfig._CLIENT]).then();
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
