import {Component, Inject, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {Subscription} from 'rxjs';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {FORM_GENERATOR, SflFormsModel} from '../sfl-forms.model';
import {SflFormsComponent} from '../sfl-forms.component';
import {SflFormsService} from '../sfl-forms.service';
import {MatAutocompleteSelectedEvent} from '@angular/material/autocomplete';


@Component({
  selector: 'sfl-generator-modal',
  templateUrl: './generator-modal.component.html'
})
export class GeneratorModalComponent implements OnInit, OnDestroy {
  subscription: Subscription = new Subscription();
  formGenerator = FORM_GENERATOR;

  constructor(
    private sflFormsService: SflFormsService,
    @Inject(MAT_DIALOG_DATA) public data: SflFormsModel,
    public dialogRef: MatDialogRef<SflFormsComponent>
  ) { }

  ngOnInit() {
  }

  updateFormGenerator() {
    this.subscription.add(
      this.sflFormsService.updateFormGenerator(this.data.id, this.data.generators).subscribe(() => {
        this.closeDialog();
      })
    );
  }

  removeGenerator(generator: string): void {
    const index = this.data.generators.indexOf(generator);
    if (index >= 0) {
      this.data.generators.splice(index, 1);
    }
  }

  selectedGenerator(event: MatAutocompleteSelectedEvent): void {
    const generator = event.option.value;
    if (this.data.generators.indexOf(generator) === -1) {
      this.data.generators.push(generator);
    }
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
