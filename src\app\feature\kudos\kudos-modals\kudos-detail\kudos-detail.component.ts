import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import {Kudos} from '../../kudos.model';
import {KudosComponent} from '../../kudos.component';


@Component({
  selector: 'sfl-award-detail',
  templateUrl: './kudos-detail.component.html'
})
export class KudosDetailComponent implements OnInit {

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: Kudos,
    public dialogRef: MatDialogRef<KudosComponent>
  ) { }

  ngOnInit() {
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

}
