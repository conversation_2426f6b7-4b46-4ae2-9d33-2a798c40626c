import { Pipe, PipeTransform } from "@angular/core";
import * as moment from "moment";
import { TimeTrack } from "src/app/feature/timesheet/timesheet.model";

@Pipe({
  name: "displayTime",
  pure: true,
})
export class DisplayTimePipe implements PipeTransform {
  transform(value: TimeTrack, date: Date): string {
    if (value.workLogs?.length) {
      let foundTime: string | null = null;
      value.workLogs.forEach((dto) => {
        if (moment(dto.logDate).isSame(moment(date), "day")) {
          const hours = Math.floor(dto.timeTrackLogsTotal / 3600);
          const minutes = Math.floor((dto.timeTrackLogsTotal % 3600) / 60);
          foundTime = `${hours}:${String(minutes).padStart(2, "0")}`;
        }
      });
      if (foundTime) {
        return foundTime;
      }
    }
    return "";
  }
}
