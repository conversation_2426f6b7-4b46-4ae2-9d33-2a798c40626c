import { DatePipe } from "@angular/common";
import { HttpErrorResponse } from "@angular/common/http";
import { Component, OnD<PERSON>roy, OnInit, ViewChild } from "@angular/core";
import { MatPaginator, MatSort, MatTableDataSource } from "@angular/material";
import { Subscription } from "rxjs";
import { finalize } from "rxjs/operators";
import { PageableQuery } from "src/app/shared";
import { AppConfig } from "../../app.config";
import { ErrorMessage, SharedService, SnackBarService, Variable } from "../../shared";
import {
  ActiveUserDTO,
  AttendanceDTO,
  ClockInOutDTO,
  DASHBOARD_CURRENT_PROJECT_COLUMNS,
  DASHBOARD_HOLIDAY_DISPLAY_COLUMNS,
  DASHBOARD_MISSING_TIME_SHEET_COLUMNS,
  DASHBOARD_NOTIFICATIONS_COLUMNS,
  FormNotification,
  Holiday,
  LeaveCount,
  LeavesTotalCount,
  MissingTimeSheet,
  MissingTimeSheetPageable,
  MyCurrentProject,
  MyCurrentProjectPageable,
} from "./dashboard.model";
import { DashboardService } from "./dashboard.service";

@Component({
  selector: "sfl-my-dashboard",
  templateUrl: "./dashboard.component.html",
})
export class DashboardComponent implements OnInit, OnDestroy {
  isButtonVisible: boolean;
  empAttandance: AttendanceDTO = new AttendanceDTO();
  ActiveEmpAttandance: ActiveUserDTO = new ActiveUserDTO();
  clockedInId: number;

  @ViewChild("notificationSort", { static: false }) set setNotificationSort(notificationSort: MatSort) {
    this.notificationDataSource.sort = notificationSort;
  }
  @ViewChild("myCurrentProjectSort", { static: false }) set setProjectSort(myCurrentProjectSort: MatSort) {
    this.myCurrentProjectDataSource.sort = myCurrentProjectSort;
  }
  @ViewChild("holidaySort", { static: false }) set setHolidaySort(holidaySort: MatSort) {
    this.holidayDataSource.sort = holidaySort;
  }
  @ViewChild("missingTimeSheetSort", { static: false }) set setMissingWorklogSort(missingTimeSheetSort: MatSort) {
    this.missingTimeSheetDataSource.sort = missingTimeSheetSort;
  }

  @ViewChild("notificationPaginator", { static: false }) set setNotificationPaginator(notificationPaginator: MatPaginator) {
    this.notificationDataSource.paginator = notificationPaginator;
  }
  @ViewChild("myCurrentProjectPaginator", { static: false }) set setProjectPaginator(myCurrentProjectPaginator: MatPaginator) {
    this.myCurrentProjectDataSource.paginator = myCurrentProjectPaginator;
  }
  @ViewChild("holidayPaginator", { static: false }) set setHolidayPaginator(holidayPaginator: MatPaginator) {
    this.holidayDataSource.paginator = holidayPaginator;
  }
  @ViewChild("missingTimeSheetPaginator", { static: false }) set setMissingWorklogPaginator(missingTimeSheetPaginator: MatPaginator) {
    this.missingTimeSheetDataSource.paginator = missingTimeSheetPaginator;
  }

  userId: number;
  missingTimeSheetDataSource = new MatTableDataSource<MissingTimeSheet>();
  myCurrentProjectDataSource = new MatTableDataSource<MyCurrentProject>();
  notificationDataSource = new MatTableDataSource<FormNotification>();
  holidayDataSource = new MatTableDataSource<Holiday>();
  missingTimeSheetPageable: MissingTimeSheetPageable = new MissingTimeSheetPageable();
  myCurrentProjectPageable: MyCurrentProjectPageable = new MyCurrentProjectPageable();
  pageableMissingTimeSheet: PageableQuery = new PageableQuery();
  pageableMyCurrentProject: PageableQuery = new PageableQuery();
  subscription: Subscription = new Subscription();
  leaveCount: LeaveCount = new LeaveCount();
  leavesTotalCount: LeavesTotalCount = new LeavesTotalCount();
  empAttendanceDTO: ClockInOutDTO = new ClockInOutDTO();
  monthDateYear = Variable.MM_DD_YYYY;
  pageSize = Variable.PAGE_SIZE;
  projectDetailsURL = AppConfig._PROJECT_DETAILS;
  formsFillURL = AppConfig._FORMS_FILL;
  // worklogURL = AppConfig._WORKLOG;
  leaveURL = AppConfig._LEAVE;
  displayMissingTimeSheetColumns = DASHBOARD_MISSING_TIME_SHEET_COLUMNS;
  displayedCurrentProjectColumns = DASHBOARD_CURRENT_PROJECT_COLUMNS;
  displayedNotificationColumns = DASHBOARD_NOTIFICATIONS_COLUMNS;
  displayedHolidayColumns = DASHBOARD_HOLIDAY_DISPLAY_COLUMNS;
  showNotificationLoader = true;
  showProjectsLoader = true;
  showHolidayLoader = true;
  showMissingWorklogTable = true;
  showNotificationTable = true;
  noNotificationData = true;
  noProjectData = true;
  noHolidayData = true;
  noMissingWorklogData = true;

  constructor(private sharedService: SharedService, private myDashboardService: DashboardService, private snakeBarService: SnackBarService, private datePipe: DatePipe) {
    this.pageableMissingTimeSheet.page = 0;
    this.pageableMissingTimeSheet.size = 10;
    this.pageableMissingTimeSheet.sort = Variable.Date;
    this.pageableMissingTimeSheet.direction = Variable.DESC;
    this.pageableMyCurrentProject.page = 0;
    this.pageableMyCurrentProject.size = 10;
    this.pageableMyCurrentProject.sort = Variable.PROJECT_NAME;
    this.pageableMyCurrentProject.direction = Variable.ASC;
  }

  ngOnInit() {
    this.userId = this.sharedService.getUserId();
    this.getTotalLeavesCountByUaaUser(this.userId);
    this.getLeaveCountByUaaUser(this.userId);
    this.getMissingTimeSheets(this.userId);
    this.getMyCurrentProjects(this.userId);
    this.getHolidays();
    this.getNotifications();
    this.getActiveUser();
  }

  getMissingTimeSheetSorting(event) {
    this.pageableMissingTimeSheet.sort = event.active;
    this.pageableMissingTimeSheet.direction = event.direction;
    this.pageableMissingTimeSheet.page = 0;
  }

  getMissingWorkSheetPagination(event) {
    this.pageableMissingTimeSheet.size = event.pageSize;
    this.pageableMissingTimeSheet.page = event.pageIndex;
  }

  getMyCurrentProjectSorting(event) {
    this.pageableMyCurrentProject.sort = event.active;
    this.pageableMyCurrentProject.direction = event.direction;
    this.pageableMyCurrentProject.page = 0;
  }

  getMyCurrentProjectPagination(event) {
    this.pageableMyCurrentProject.size = event.pageSize;
    this.pageableMyCurrentProject.page = event.pageIndex;
  }

  getHolidays() {
    this.showHolidayLoader = true;
    this.noHolidayData = true;
    this.subscription.add(
      this.myDashboardService
        .getHolidays()
        .pipe(
          finalize(() => {
            this.noHolidayData = this.holidayDataSource.data.length <= 0;
            this.showHolidayLoader = false;
          })
        )
        .subscribe((res: Holiday[]) => {
          this.holidayDataSource = new MatTableDataSource<Holiday>(res);
        })
    );
  }

  clockIn() {
    this.showProjectsLoader = true;
    this.empAttendanceDTO.clockIn = this.datePipe.transform(new Date(), "yyyy-MM-dd'T'HH:mm:ss");
    this.empAttendanceDTO.clockIn = this.empAttendanceDTO.clockIn + ".000Z";
    this.empAttendanceDTO.clockOut = null;
    this.isButtonVisible = !this.isButtonVisible;
    this.empAttendanceDTO.uaaUserId = this.userId;
    this.subscription.add(
      this.myDashboardService.empClockInOut(this.empAttendanceDTO).subscribe((res) => {
        this.showProjectsLoader = false;
        this.empAttandance = res;
        sessionStorage.setItem("clockedIn", res.clockIn);
        this.getActiveUser();
        this.snakeBarService.success("You've Clocked-In successfully!");
      })
    );
  }

  clockOut() {
    this.showProjectsLoader = true;
    this.empAttendanceDTO.id = this.ActiveEmpAttandance.clockedInId;
    this.empAttendanceDTO.clockOut = this.datePipe.transform(new Date(), "yyyy-MM-dd'T'HH:mm:ss");
    this.empAttendanceDTO.clockOut = this.empAttendanceDTO.clockOut + ".000Z";
    this.empAttendanceDTO.clockIn = sessionStorage.getItem("clockedIn");
    this.isButtonVisible = !this.isButtonVisible;
    this.empAttendanceDTO.uaaUserId = this.userId;
    this.subscription.add(
      this.myDashboardService.empClockInOut(this.empAttendanceDTO).subscribe((res) => {
        this.empAttandance = res;
        this.getActiveUser();
        this.snakeBarService.success("You've Clocked-Out successfully!");
        this.showProjectsLoader = false;
      })
    );
  }

  getActiveUser() {
    this.showProjectsLoader = true;
    this.subscription.add(
      this.myDashboardService.getActiveClockInUser(this.userId).subscribe((res) => {
        this.ActiveEmpAttandance = res;
        this.showProjectsLoader = false;
      })
    );
  }

  getNotifications() {
    this.showNotificationTable = true;
    this.noNotificationData = true;
    // this.subscription.add(this.myDashboardService.getNotifications(this.userId)
    //   .pipe(
    //     finalize(() => {
    //       this.noNotificationData = this.notificationDataSource.data.length <= 0;
    //       this.showNotificationLoader = false;
    //     })
    //   )
    //   .subscribe((res: FormNotification[]) => {
    //     this.notificationDataSource = new MatTableDataSource<FormNotification>(res);
    //   })
    // );
  }

  getLeaveCountByUaaUser(userId: number) {
    this.subscription.add(
      this.myDashboardService.getLeaveCountByUaaUser(userId).subscribe((res: LeaveCount) => {
        this.leaveCount = res;
      })
    );
  }

  getTotalLeavesCountByUaaUser(userId: number) {
    this.subscription.add(
      this.myDashboardService.getTotalLeavesCountByUaaUser(userId).subscribe((res: LeavesTotalCount) => {
        this.leavesTotalCount = res;
      })
    );
  }

  getMissingTimeSheets(userId: number) {
    this.showMissingWorklogTable = true;
    this.noMissingWorklogData = true;
    const currentSort = this.pageableMissingTimeSheet.sort;
    this.pageableMissingTimeSheet.sort = this.pageableMissingTimeSheet.sort + "," + this.pageableMissingTimeSheet.direction;
    this.subscription.add(
      this.myDashboardService
        .getMissingTimeSheets(userId, this.pageableMissingTimeSheet)
        .pipe(
          finalize(() => {
            this.noMissingWorklogData = this.missingTimeSheetDataSource.data.length <= 0;
            this.showMissingWorklogTable = false;
          })
        )
        .subscribe(
          (res: MissingTimeSheetPageable) => {
            this.missingTimeSheetPageable = res;
            this.missingTimeSheetDataSource = new MatTableDataSource<MissingTimeSheet>(this.missingTimeSheetPageable.content);
            this.pageableMissingTimeSheet.size = this.missingTimeSheetPageable.pageable.pageSize;
            this.pageableMissingTimeSheet.page = this.missingTimeSheetPageable.pageable.pageNumber;
          },
          (error: HttpErrorResponse) => {
            if (error.error.message && error.error.applicationStatusCode === 1004) {
              this.snakeBarService.error(ErrorMessage.NoJoiningDate);
            }
          }
        )
    );
    this.pageableMissingTimeSheet.sort = currentSort;
  }

  getMyCurrentProjects(userId: number) {
    this.showProjectsLoader = true;
    this.noProjectData = true;
    const currentSort = this.pageableMyCurrentProject.sort;
    this.pageableMyCurrentProject.sort = this.pageableMyCurrentProject.sort + "," + this.pageableMyCurrentProject.direction;

    this.subscription.add(
      this.myDashboardService
        .getMyCurrentProjects(userId, this.pageableMyCurrentProject)
        .pipe(
          finalize(() => {
            this.noProjectData = this.myCurrentProjectDataSource.data.length <= 0;
            this.showProjectsLoader = false;
          })
        )
        .subscribe((res: MyCurrentProjectPageable) => {
          this.myCurrentProjectPageable = res;
          this.myCurrentProjectDataSource = new MatTableDataSource<MyCurrentProject>(this.myCurrentProjectPageable.content);
          this.pageableMyCurrentProject.size = this.myCurrentProjectPageable.pageable.pageSize;
          this.pageableMyCurrentProject.page = this.myCurrentProjectPageable.pageable.pageNumber;
        })
    );
    this.pageableMyCurrentProject.sort = currentSort;
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
