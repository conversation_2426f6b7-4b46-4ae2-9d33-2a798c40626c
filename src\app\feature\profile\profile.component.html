<div fxFlex fxLayout="column">
  <div class="body-content p-25" fxLayout="row column" fxLayoutAlign="space-around center" fxFlex="100">
    <div fxLayout="row column" fxFlex="100" class="pos-rel">
      <div class="profile-img-card pos-rel" fxLayout="row" fxFlex.gt-lg="10" fxFlex.gt-md="15" fxFlex.gt-sm="100"
        fxFlex.gt-xs="100">
        <img [src]="imageUrl" class="profile-image" alt="profile" *ngIf="!imgLoader">
        <mat-progress-spinner *ngIf="imgLoader" color='warn' mode="indeterminate"></mat-progress-spinner>
        <div *ngIf="isCurrentUser" class="profile-upload-overlay cursor-pointer" (click)="fileInput.click()">
          <mat-icon class="mat-icon material-icons profile-upload-icon" role="img" aria-hidden="true">
            camera_alt
          </mat-icon>
          <input #fileInput type="file" class="d-none" (change)="setProfilePicture(fileInput.files)" />
          <p class="profile-upload-text">Change Image</p>
        </div>
      </div>

      <div fxLayout="row column" fxFlex.gt-lg="80" fxFlex.gt-md="80" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <div fxLayout="row" fxFlex="100">
          <h1 class="profile-title-name" *ngIf="basicProfile.fullName !== 'null null'">
            {{basicProfile?.fullName | uppercase}}</h1>
        </div>

        <div fxLayout="row column" fxFlex="100">
          <span class="profile-icon-text" fxLayout="row column" fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="50"
            fxFlex.gt-xs="100">
            <mat-icon class="mat-icon material-icons" role="img" aria-hidden="true">email</mat-icon>
            <span>{{basicProfile?.email}}</span>
          </span>

          <span class="profile-icon-text" fxLayout="row column" fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="50"
            fxFlex.gt-xs="100">
            <mat-icon class="mat-icon material-icons" role="img" aria-hidden="true">phone</mat-icon>
            <span>{{basicProfile?.phoneNo}}</span>
          </span>

          <span class="profile-icon-text" fxLayout="row column" fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="50"
            fxFlex.gt-xs="100">
            <mat-icon class="mat-icon material-icons" role="img" aria-hidden="true">wc</mat-icon>
            <span>{{basicProfile.maritalStatus === true ? 'Married' : 'Unmarried'}}</span>
          </span>

          <span class="profile-icon-text" fxLayout="row column" fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="50"
            fxFlex.gt-xs="100">
            <mat-icon class="mat-icon material-icons" role="img" aria-hidden="true">cake</mat-icon>
            <span>{{basicProfile?.birthDate | date:'longDate'}}</span>
          </span>

          <span class="profile-icon-text" fxLayout="row column" fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="50"
            fxFlex.gt-xs="100">
            <mat-icon class="mat-icon material-icons" role="img" aria-hidden="true">person_add</mat-icon>
            <span>{{basicProfile?.joinDate | date:'longDate'}}</span>
          </span>

          <span class="profile-icon-text" fxLayout="row column" fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="50"
            fxFlex.gt-xs="100" *ngIf="basicProfile.maritalStatus">
            <mat-icon class="mat-icon material-icons" role="img" aria-hidden="true">date_range</mat-icon>
            <span>{{basicProfile?.anniversaryDate | date:'longDate'}}</span>
          </span>

          <span class="profile-icon-text" fxLayout="row column" fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="50"
            fxFlex.gt-xs="100" *ngIf="basicProfile.secondaryPhoneNo">
            <mat-icon class="mat-icon material-icons" role="img" aria-hidden="true">phone</mat-icon>
            <span>{{basicProfile?.secondaryPhoneNo}}</span>
          </span>

          <span class="profile-icon-text" fxLayout="row column" fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="50"
            fxFlex.gt-xs="100" *ngFor="let address of basicProfile.addresses">
            <mat-icon class="mat-icon material-icons" role="img" aria-hidden="true">place</mat-icon>
            <span>
              {{address?.addressLine1 + ', '}}
              {{address.addressLine2 === null ? '' : address?.addressLine2 + ', '}}
              {{address?.city + ', ' + address?.state + ', ' + address?.country + '.' }}
            </span>
          </span>

          <span class="profile-icon-text" fxLayout="row column" fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="50"
            fxFlex.gt-xs="100">
            <mat-icon class="mat-icon material-icons" role="img" aria-hidden="true">work</mat-icon>
            <span>Level {{ basicProfile?.userLevelDTO?.empLevelId || ' ' }}</span>
          </span>

        </div>
      </div>
        <div *ngIf="isCurrentUser" fxLayout="row" fxLayoutAlign="end">
          <button mat-raised-button class="bt-sfl h-fit-content mt-40" (click)="openEditProfile()">Edit Profile</button>
        </div>
    </div>
</div>

<div class="body-content" fxLayout="column" fxFlex="100">
  <div fxFlex fxLayout="row" fxLayoutAlign="space-between" class="mb-1">
    <mat-card-title class="p-10">Kudos</mat-card-title>
    <button *ngIf="!isCurrentUser" mat-raised-button class="bt-sfl" (click)="openGiveKudos()">Give Kudos</button>
  </div>

  <hr class="header-divider">

  <div class="p-25" fxLayoutAlign="center center" fxFlex="100" *ngIf="showLoader">
    <mat-progress-spinner color="warn" mode="indeterminate"></mat-progress-spinner>
  </div>

  <div *ngIf="!showLoader && noDataFound" class="w-100 mt-40 no-data-found-text">No Kudos Found.</div>

  <div class="w-100" *ngIf="!showLoader && !noDataFound">
    <table class="w-auto" mat-table matSort matSortDisableClear [dataSource]="dataSource"
      [matSortActive]="pageable.sort" [matSortDirection]="pageable.direction" (matSortChange)="getSorting($event)">
      <ng-container matColumnDef="image">
        <mat-header-cell *matHeaderCellDef fxFlex.gt-sm="8"> Kudos </mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex.gt-sm="8">
          <img [src]="element?.awardsDTO?.imageUrl" class="kudos-image" alt="kudos-img" />
        </mat-cell>
      </ng-container>
      <ng-container matColumnDef="awards.name">
        <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-sm="11"> Kudos Name </mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex.gt-sm="11">
          {{ element?.awardsDTO?.name }}
        </mat-cell>
      </ng-container>
      <ng-container matColumnDef="awards.description">
        <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-sm="25"> Description
        </mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex.gt-sm="25">
          {{ element?.awardsDTO?.description }}
        </mat-cell>
      </ng-container>
      <ng-container matColumnDef="awardedDate">
        <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-sm="10"> Awarded Date </mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex.gt-sm="10"> {{ element?.awardedDate | date: dateFormat }}
        </mat-cell>
      </ng-container>
      <ng-container matColumnDef="awardedBy">
        <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-sm="10"> Awarded By </mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex.gt-sm="10">
          {{ element?.awardedByFirstName + ' ' + element?.awardedByLastName }} </mat-cell>
      </ng-container>
      <ng-container matColumnDef="purpose">
        <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-sm="35"> Purpose </mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex.gt-sm="35"> {{ element?.purpose }} </mat-cell>
      </ng-container>

      <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
      <mat-row *matRowDef="let row; columns: displayedColumns"></mat-row>
    </table>
    <mat-paginator [length]="kudosPageable.totalElements" [pageSizeOptions]="[10, 20, 25]" [pageIndex]="pageable.page"
      [pageSize]="pageable.size" (page)="getPagination($event)" showFirstLastButtons>
    </mat-paginator>
  </div>

</div>
</div>