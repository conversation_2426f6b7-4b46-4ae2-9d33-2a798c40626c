<h2 mat-dialog-title>{{ data.title }}</h2>
<hr class="mb-1">

<mat-dialog-content>
  <form #memberForm="ngForm">
    <mat-form-field>
      <mat-label>Member Type</mat-label>
      <mat-select name="selectedType" [(ngModel)]="selectedType" required [disabled] = "cannotChangeUserType">
        <mat-option *ngFor="let memberType of memberTypes" [value]="memberType.value">
          {{memberType.title}}
        </mat-option>
      </mat-select>
    </mat-form-field>

    <mat-form-field>
      <mat-label>Select Member</mat-label>
      <mat-select name="selectedUser" [(ngModel)]="selectedUser" required>
        <mat-option *ngFor="let user of users" [value]="user.id">
          {{user.fullName}}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </form>
</mat-dialog-content>
<hr>

<mat-dialog-actions fxLayoutAlign="end">
  <button class="bt-flat" (click)="closeModal()">Cancel</button>
  <button mat-raised-button class="bt-sfl" (click)="addUser()" [disabled]="memberForm.form.invalid">Add</button>
</mat-dialog-actions>
