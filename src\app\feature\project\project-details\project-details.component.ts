import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>nit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ProjectService } from '../project.service';
import { Project, ProjectDetails, ProjectDevelopers, ProjectDocument } from '../project.model';
import { ErrorMessage, Messages, ROLES, SharedService, SnackBarService, Variable } from 'src/app/shared';
import { MatDialog, MatDialogConfig } from '@angular/material';
import { UserModalComponent } from '../user-modal/user-modal.component';
import { DocumentModalComponent } from '../document-modal/document-modal.component';
import { Subscription } from 'rxjs';
import { SweetAlertService } from 'src/app/shared/service/sweetalert.service';
import { Location } from '@angular/common';
import { GenerateFormModalComponent } from '../../sfl-forms/generate-form/generate-form-modal/generate-form-modal.component';
import { AppConfig } from '../../../app.config';
import { finalize } from 'rxjs/operators';
import { WorkHoursReportComponent } from '../project-workhours-report-model/project-workhours-report-model.component';
import { WorkHoursComponent } from '../project-workhours-model/project-workhours-model.component';


@Component({
  selector: 'sfl-project-details',
  templateUrl: './project-details.component.html',
  styleUrls: ['./project-details.component.css']
})
export class ProjectDetailsComponent implements OnInit, OnDestroy {
  @ViewChild('_ref_generateFormModal', { static: false }) _ref_generateFormModal: GenerateFormModalComponent;

  readonly ACTIVE_PROJECT = Variable.ACTIVE_PROJECT;
  readonly INACTIVE_PROJECT = Variable.INACTIVE;
  projectId: number;
  project = new Project();
  projectDetails = new ProjectDetails();
  VARIABLE = Variable;
  subscription = new Subscription();
  empWorklogURL = AppConfig._EMP_WORKLOG;
  isActive = false;

  constructor(
    private sharedService: SharedService,
    private activatedRoute: ActivatedRoute,
    private projectService: ProjectService,
    public matDialog: MatDialog,
    private snackBarService: SnackBarService,
    private sweetAlertService: SweetAlertService,
    private location: Location
  ) { }

  ngOnInit() {
    const currentUserRole = this.sharedService.getRole();
    if (currentUserRole.includes(ROLES.SUPER_ADMIN) ||
      currentUserRole.includes(ROLES.ADMIN) ||
      currentUserRole.includes(ROLES.LEAD)) {
      this.isActive = true;
    }
    this.subscription.add(this.activatedRoute.paramMap.subscribe((params) => {
      this.projectId = Number(params.get(Variable.ID));
      this.getProjects();
      this.getProjectDetails();
    }));
  }

  getProjects() {
    this.subscription.add(this.projectService.getProjectById(this.projectId).subscribe((res: Project) => {
      this.project = res;
    }));
  }

  getProjectDetails() {
    this.subscription.add(
      this.projectService.getProjectDetailsById(this.projectId).subscribe((res: ProjectDetails) => {
        if (res) {
          this.projectDetails = res;
        } else {
          this.snackBarService.error(ErrorMessage.ProjectDetailsNotAvailable);
          this.location.back();
        }
      })
    );
  }

  addUser(addType: string) {
    const dialogRef = this.matDialog.open(UserModalComponent, {
      width: Variable.BOX_WIDTH_VALUE,
      data: {
        title: 'Add Member',
        type: addType,
        isEdit: false,
        userId: null
      }
    });
    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        if (result.type === Variable.ACCOUNT_MANAGER) {
          this.projectDetails.accountManagerId = result.id;
        } else if (result.type === Variable.DELIVERY_MANAGER) {
          this.projectDetails.deliveryManagerId = result.id;
        } else if (result.type === Variable.TECHNICAL_LEAD) {
          this.projectDetails.teamLeadId = result.id;
        } else if (result.type === Variable.DEVELOPER || result.type === Variable.QA) {
          const developer = new ProjectDevelopers();
          developer.projectDeveloperId = result.id;
          developer.type = result.type;
          developer.projectDetailsId = this.projectDetails.id;
          const index = this.projectDetails.projectDevelopers.findIndex((x) => x.projectDeveloperId === result.id);
          if (index === -1) {
            this.projectDetails.projectDevelopers.push(developer);
          } else {
            this.snackBarService.error(Messages.PROJECT.user_already_member);
          }
        }
        this.updateProjectDetails();
      }
    });
  }

  editUser(editType: string, userId: number) {
    const dialogRef = this.matDialog.open(UserModalComponent, {
      width: Variable.BOX_WIDTH_VALUE,
      data: {
        title: 'Edit Member',
        type: editType,
        isEdit: true,
        userId: userId
      }
    });
    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        if (result.type === Variable.ACCOUNT_MANAGER) {
          this.projectDetails.accountManagerId = result.id;
        } else if (result.type === Variable.DELIVERY_MANAGER) {
          this.projectDetails.deliveryManagerId = result.id;
        } else if (result.type === Variable.TECHNICAL_LEAD) {
          this.projectDetails.teamLeadId = result.id;
        }
        this.updateProjectDetails();
      }
    });
  }

  deleteUser(index) {
    const deleteConfirm = this.sweetAlertService.deleteAlert('this user?');
    deleteConfirm.then((result) => {
      if (result) {
         this.projectDetails.projectDevelopers[index].status = false;
      }
      this.updateProjectDetails();
    });
  }

  addDocument() {
    const dialogRef = this.matDialog.open(DocumentModalComponent, {
      width: Variable.BOX_WIDTH_VALUE,
      data: {}
    });
    dialogRef.afterClosed().subscribe((result: ProjectDocument) => {
      if (result) {
        result.projectDetailsId = this.projectDetails.id;
        this.projectDetails.projectDocuments.push(result);
        this.updateProjectDetails();
      }
    });
  }

  deleteDocument(event: any, index: number) {
    event.preventDefault();
    const deleteConfirm = this.sweetAlertService.deleteAlert('this document?');
    deleteConfirm.then((result) => {
      if (result) {
        this.projectDetails.projectDocuments[index].status = false;
        this.updateProjectDetails();
      }
    });
  }

  updateProjectDetails() {
    this.subscription.add(this.projectService.updateProjectDetails(this.projectDetails)
      .pipe(finalize(() => { this.getProjectDetails(); }))
      .subscribe()
    );
  }

  generateForm() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = {
      directory: {
        projectId: this.project.id
      },
      generateFormModel: null
    };
    matDataConfig.width = Variable.BOX_WIDTH_VALUE;
    const dialogRef = this.matDialog.open(GenerateFormModalComponent, matDataConfig);
    this.subscription.add(
      dialogRef.afterClosed().subscribe((res) => {
        if (res && this._ref_generateFormModal !== undefined) {
          this._ref_generateFormModal.getCreatedForms();
        }
      })
    );
  }

  generateWorkHoursReport() {
    this.matDialog.open(WorkHoursReportComponent, {
      width: Variable.BOX_WIDTH_VALUE,
      data: {
        projectId: this.projectDetails.projectId
      }
    });
  }

  addWorkHours() {
    const dialogRef = this.matDialog.open(WorkHoursComponent, {
      width: Variable.BOX_WIDTH_VALUE,
      data: {
        projectId: this.projectDetails.projectId
      }
    });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
