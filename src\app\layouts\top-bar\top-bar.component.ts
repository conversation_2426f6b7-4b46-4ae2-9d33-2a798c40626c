import { Component, EventEmitter, Output, OnInit, ViewChild, ElementRef, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import {SharedService} from '../../shared';
import { Subscription } from 'rxjs';
import { AppConfig } from '../../app.config';
import { ProjectService } from 'src/app/feature/project/project.service';
import { Project } from 'src/app/feature/project/project.model';
import {MatAutocompleteSelectedEvent} from '@angular/material/autocomplete';
import {EmployeeManagementService} from '../../feature/employee-management/employee-management.service';
import {Search} from '../../feature/employee-management/employee-management.model';

@Component({
  selector: 'sfl-top-bar',
  templateUrl: './top-bar.component.html'
})
export class TopBarComponent implements OnInit, OnDestroy {
  @Output() toggleSidenav = new EventEmitter<void>();
  @ViewChild('searchField', {static: false}) searchField: ElementRef;

  userName: string;
  searchTerm: string;
  users: Search[] = [];
  projects: Project[] = [];
  subscription: Subscription = new Subscription();
  employeeProfileURL = AppConfig._EMPLOYEE_PROFILE;
  changePasswordURL = AppConfig._CHANGE_PASSWORD;
  projectDetailsURL = AppConfig._PROJECT_DETAILS + '/';

  constructor(
    private router: Router,
    private sharedService: SharedService,
    private employeeService: EmployeeManagementService,
    private projectService: ProjectService
  ) {}

  ngOnInit() {
    this.userName = this.sharedService.getUserName();
  }

  logout() {
    this.sharedService.logout();
    this.router.navigate([AppConfig.LOGIN]).then();
  }

  search() {
    const trimmedSearchTerm = this.searchTerm.trim();
    if (trimmedSearchTerm && trimmedSearchTerm.length > 1) {
      this.employeeSearch(trimmedSearchTerm);
      this.projectSearch(trimmedSearchTerm);
    }
  }

  employeeSearch(employeeSearchTerm) {
    this.subscription.add(
      this.employeeService.getUserBySearchTerm(employeeSearchTerm).subscribe((res: Search[]) => {
        this.users = res;
      })
    );
  }

  projectSearch(projectSearchTerm) {
    this.subscription.add(
      this.projectService.getProjectByTerm(projectSearchTerm).subscribe((res: Project[]) => {
        this.projects = res;
      })
    );
  }

  goToView(event: MatAutocompleteSelectedEvent) {
    this.searchField.nativeElement.blur();
    this.router.navigate([event.option.value]).then();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
