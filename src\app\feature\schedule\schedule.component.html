<div class="body-content" fxLayout="column" fxFlex="100">
  <div fxFlex fxLayout="row" fxLayoutAlign="space-between" class="mb-1">
    <mat-card-title class="p-10">Schedule</mat-card-title>
    <div>
      <button mat-raised-button class="bt-sfl mr-10px" [disabled]="!allocatedSchedules.length" (click)="exportSchedule(schedulerComponent)">
        Export Employee Schedule
        <mat-progress-spinner *ngIf="isExporting" [diameter]="20" [strokeWidth]="3" mode="indeterminate" style="display: inline-block"></mat-progress-spinner>
      </button>
      <button mat-raised-button class="bt-sfl" (click)="openScheduleDialog(addText, false)">Add New Schedule</button>
    </div>
  </div>

  <hr class="header-divider" />

  <form #filter="ngForm" (ngSubmit)="getSchedules()">
    <div fxLayout="row column" fxLayoutAlign="start center" fxFlex="100" class="sfl-card">
      <mat-card-content class="filter-input ml-1 mr-1">
        <mat-form-field>
          <mat-select placeholder="Select Project" multiple name="projectName" [(ngModel)]="filters.projectName">
            <mat-option *ngFor="let project of projects" [value]="project?.name">{{ project?.name }}</mat-option>
          </mat-select>
          <button mat-icon-button matSuffix matTooltip="clear" *ngIf="filters.projectName" (click)="resetFilter('projectName')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-content>
      <mat-card-content class="filter-input ml-1 mr-1">
        <mat-form-field>
          <mat-select placeholder="Select Client" name="clientName" [(ngModel)]="filters.clientName">
            <mat-option *ngFor="let client of clients" [value]="client?.clientName">{{ client?.clientName }}</mat-option>
          </mat-select>
          <button mat-icon-button matSuffix matTooltip="clear" *ngIf="filters.clientName" (click)="resetFilter('clientName')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-content>
      <mat-card-content class="filter-input ml-1 mr-1">
        <mat-form-field>
          <mat-select placeholder="Select Employee" name="employeeName" [(ngModel)]="filters.uaaUserId">
            <mat-option *ngFor="let user of users" [value]="user?.id">{{ user?.fullName }}</mat-option>
          </mat-select>
          <button mat-icon-button matSuffix matTooltip="clear" *ngIf="filters.uaaUserId" (click)="resetFilter('uaaUserId')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-content>
      <mat-card-content class="filter-input ml-1 mr-1">
        <mat-form-field>
          <mat-select placeholder="Select Technology" multiple name="technology" [(ngModel)]="filters.technologyId" #technology="ngModel">
            <mat-option *ngFor="let technology of technologies" [value]="technology?.id">{{ technology?.name }}</mat-option>
          </mat-select>
          <button mat-icon-button matSuffix matTooltip="clear" *ngIf="filters.technologyId" (click)="resetFilter('technologyId')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-content>

      <mat-card-actions class="filter-input ml-1 mr-1">
        <mat-form-field>
          <mat-select placeholder="Workload" name="workload" [(ngModel)]="filters.workload">
            <mat-option *ngFor="let workload of workLoadFilter" [value]="workload">{{ workload }}</mat-option>
          </mat-select>
          <button mat-icon-button matSuffix class="float-center" matTooltip="clear" *ngIf="filters.workload" (click)="resetFilter('workload')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-button-group ml-1">
        <button mat-raised-button [disabled]="filter?.form?.invalid" class="bt-sfl mr-10px" type="submit">Filter</button>
        <button class="bt-flat" type="button" (click)="resetAllFilter()">Reset All</button>
      </mat-card-actions>
    </div>
  </form>

  <div #schedulerComponent class="sfl-card">
    <ngx-ts
      *ngIf="!showLoader && startDate"
      [items]="items"
      [periods]="periods"
      [sections]="sections"
      [events]="events"
      [text]="text"
      [start]="getStartingDate()"
      [showBusinessDayOnly]="true"
      [showCurrentTime]="false"
      [minRowHeight]="65"
      [currentPeriod]="currentPeriod"
    ></ngx-ts>
    <div fxLayoutAlign="center center" fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
      <mat-progress-spinner *ngIf="showLoader" color="warn" mode="indeterminate"></mat-progress-spinner>
    </div>
  </div>
</div>
