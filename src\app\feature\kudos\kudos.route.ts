import { Routes } from '@angular/router';
import { KudosComponent } from './kudos.component';
import {KudosDetailComponent} from './kudos-modals/kudos-detail/kudos-detail.component';
import {GiveKudosComponent} from './kudos-modals/give-kudos/give-kudos.component';
import {SaveKudosComponent} from './kudos-modals/save-kudos/save-kudos.component';
import {GivenKudosDetailComponent} from './kudos-modals/given-kudos-detail/given-kudos-detail.component';
import {KudosListComponent} from './kudos-list/kudos-list.component';
import {RoleGuardService} from '../../shared/service/role-guard.service';
import {ROLES} from '../../shared';


export const kudosRoutes: Routes = [
  {
    path: '',
    component: KudosComponent
  },
  {
    path: 'kudos-details',
    component: KudosDetailComponent
  },
  {
    path: 'give-kudos',
    component: GiveKudosComponent
  },
  {
    path: 'save-kudos',
    canActivate: [RoleGuardService],
    data: {
      allowedRoles: [ROLES.SUPER_ADMIN, ROLES.ADMIN]
    },
    component: SaveKudosComponent
  },
  {
    path: 'given-kudos-detail',
    canActivate: [RoleGuardService],
    data: {
      allowedRoles: [ROLES.SUPER_ADMIN, ROLES.ADMIN]
    },
    component: GivenKudosDetailComponent
  },
  {
    path: 'kudos-list',
    canActivate: [RoleGuardService],
    data: {
      allowedRoles: [ROLES.SUPER_ADMIN, ROLES.ADMIN]
    },
    component: KudosListComponent
  }
];
