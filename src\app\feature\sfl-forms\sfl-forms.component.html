<div class="body-content" fxLayout="column" fxFlex="100">
  <div fxFlex fxLayout="row" fxLayoutAlign="space-between" class="mb-1">
    <mat-card-title class="p-10">Forms</mat-card-title>
    <div>
      <button mat-raised-button class="bt-sfl mr-10px" [routerLink]="formCreateURL">Create New Form</button>
    </div>
  </div>

  <hr class="header-divider">

  <div class="sfl-card">
    <div class="p-25" fxLayoutAlign="center center" fxFlex="100" *ngIf="showLoader">
      <mat-progress-spinner color="warn" mode="indeterminate"></mat-progress-spinner>
    </div>

    <div *ngIf="!showLoader && noDataFound" class="w-100 mt-40 no-data-found-text">No Form Found.</div>

    <div class="w-100" *ngIf="!showLoader && !noDataFound">
      <table class="w-auto" mat-table matSort matSortDisableClear matSortActive="version" matSortDirection="desc" [dataSource]="dataSource">

        <ng-container matColumnDef="formName">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-sm="20"> Form Name &nbsp; <i>(Click to Preview)</i> </mat-header-cell>
          <mat-cell *matCellDef="let element" class="link cursor-pointer" (click)="previewForm(element)" fxFlex.gt-sm="20">
            {{ element?.formName }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="description">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-sm="25"> Description </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-sm="25">
            {{ element?.description || 'None' }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="formType">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-sm="15"> Form Type </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-sm="15">
            {{ element?.formType }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="createdBy">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxLayoutAlign="center center" fxFlex.gt-sm="10"> Created by </mat-header-cell>
          <mat-cell *matCellDef="let element" fxLayoutAlign="center center" fxFlex.gt-sm="10">
            {{ element?.createdBy | titlecase }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="generators">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxLayoutAlign="center center" fxFlex.gt-sm="15"> Generators &nbsp; <i>(Click to edit)</i> </mat-header-cell>
          <mat-cell *matCellDef="let element" class="pos-rel" fxLayoutAlign="center center" fxFlex.gt-sm="15">
            {{ element?.generators | arrayDisplay | replaceString: '-' | titlecase }}
            <span class="generated-edit-text" (click)="updateFormGenerator(element)"><i>Click to edit</i></span>
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="version">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxLayoutAlign="center center" fxFlex.gt-sm="5"> Version </mat-header-cell>
          <mat-cell *matCellDef="let element" fxLayoutAlign="center center" fxFlex.gt-sm="5">
            {{ element?.version }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxLayoutAlign="center center" fxFlex.gt-sm="10"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxLayoutAlign="center center" fxFlex.gt-sm="10">
            <button mat-icon-button [routerLink]="[formCreateURL, element.id]"><mat-icon>edit</mat-icon></button>
            <button mat-icon-button (click)="deleteForm(element)"><mat-icon>delete</mat-icon></button>
          </mat-cell>
        </ng-container>

        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns"></mat-row>
      </table>
      <mat-paginator [pageSize]="10" [pageSizeOptions]="[10, 20, 25]" showFirstLastButtons></mat-paginator>
    </div>
  </div>

</div>
