import { Component, OnInit, Inject, OnDestroy } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { Subscription } from 'rxjs';
import { Employee } from '../../employee-management/employee-management.model';
import { Variable } from 'src/app/shared';
import { MemberTypesSecondary, MemberTypesPrimary, UserModelDialogData } from '../project.model';
import { EmployeeManagementService } from '../../employee-management/employee-management.service';

@Component({
  selector: 'sfl-user-modal',
  templateUrl: './user-modal.component.html'
})
export class UserModalComponent implements OnInit, OnDestroy {
  memberTypes = null;
  subscription = new Subscription();
  users: Employee[] = [];
  selectedType: string = null;
  selectedUser: number = null;
  cannotChangeUserType = true;

  constructor(
    public dialogRef: MatDialogRef<UserModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: UserModelDialogData,
    private employeeService: EmployeeManagementService
  ) { }

  closeModal(): void {
    this.dialogRef.close();
  }

  ngOnInit() {
    if (this.data.userId && this.data.isEdit) {
      this.selectedUser = this.data.userId;
    }

    this.getUsers();
    if (this.data.type === Variable.PRIMARY) {
      this.memberTypes = MemberTypesPrimary;
      this.selectedType = Variable.ACCOUNT_MANAGER;
    } else if (this.data.type === Variable.ACCOUNT_MANAGER) {
      this.memberTypes = MemberTypesPrimary;
      this.selectedType = Variable.ACCOUNT_MANAGER;
    } else if (this.data.type === Variable.DELIVERY_MANAGER) {
      this.memberTypes = MemberTypesPrimary;
      this.selectedType = Variable.DELIVERY_MANAGER;
    } else if (this.data.type === Variable.TECHNICAL_LEAD) {
      this.memberTypes = MemberTypesPrimary;
      this.selectedType = Variable.TECHNICAL_LEAD;
    }
    else {
      this.memberTypes = MemberTypesSecondary;
      this.selectedType = Variable.DEVELOPER;
      this.cannotChangeUserType = false;
    }
  }

  getUsers() {
    this.subscription.add(this.employeeService.getEmployees().subscribe((res: Employee[]) => {
      this.users = res;
    }));
  }

  addUser() {
    const data = {
      type: this.selectedType,
      id: this.selectedUser
    };
    this.dialogRef.close(data);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
