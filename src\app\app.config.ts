export class AppConfig {
  public static UAA_API = 'uaa/api/';
  public static PORTAL = 'portal/api/';
  public static PROFILING = 'profiling/api/';
  public static AUTH_LOGIN = 'auth/login';
  public static AUTH_REFRESH_TOKEN = 'auth/refresh-token';

  // Account
  public static ACCOUNT_URL = AppConfig.UAA_API + 'account';
  public static FORGOT_PASSWORD_URL = AppConfig.ACCOUNT_URL + '/resetGenerated-password/init';
  public static CHANGE_PASSWORD_URL = AppConfig.ACCOUNT_URL + '/change-password';
  public static SET_UAA_USER = AppConfig.PORTAL + 'setUaaUser/';

  // Dashboard Charts
  public static PER_USER_TOTAL_HOURS = AppConfig.PORTAL + 'perUserTotalHours';
  public static PER_PROJECT_TOTAL_HOURS = AppConfig.PORTAL + 'perProjectTotalHours';
  public static PER_WORK_TYPE_TOTAL_HOURS = AppConfig.PORTAL + 'perWorkTypeTotalHours';
  public static DATE_WISE_TOTAL_HOURS = AppConfig.PORTAL + 'dateWiseTotalHours';

  // PROJECT
  public static GET_ALL_PROJECT_LIST = AppConfig.PORTAL + 'projects/all';
  public static ACTIVE_PROJECTS_ALL = AppConfig.PORTAL + 'project/active/all';
  public static UPDATE_PROJECT_STATUS = AppConfig.PORTAL + 'updateProjectStatus';
  public static CREATE_PROJECT_WITH_CLIENT = AppConfig.PORTAL + 'createProjectWithClient';
  public static UPDATE_PROJECT = AppConfig.PORTAL + 'updateProjects';
  public static FILTER_PROJECTS = AppConfig.PORTAL + 'filterAllProjects';
  public static GET_PROJECT_BY_ID = AppConfig.PORTAL + 'projects';
  public static GET_PROJECT_DETAILS_BY_ID = AppConfig.PORTAL + 'getAllByProjectId';
  public static GET_PROJECT_WORK_HOURS_BY_ID = AppConfig.PORTAL + 'getWorkHoursReportByProjectId';
  public static PROJECT_DETAILS = AppConfig.PORTAL + 'project-details';
  public static PROJECT_SEARCH_BY_NAME = AppConfig.GET_PROJECT_BY_ID + '/search/';
  public static DELETE_PROJECT_DEVELOPER_BY_ID = AppConfig.PORTAL + 'project-developers/';
  public static PROJECT_EST_HOURS_BY_PROJECT_ID = AppConfig.PORTAL + 'project-est-hours-projectId/';
  public static PROJECT_EST_HOURS = AppConfig.PORTAL + 'project-est-hours/';

  // Holiday
  public static HOLIDAYS = AppConfig.PORTAL + 'holidays/currentyear';

  // Work
  public static WORKLOG = AppConfig.PORTAL + 'worklog/';
  public static FILTER_WORKLOG = AppConfig.WORKLOG + 'filterAllWorklogs/';
  public static MISSED_WORKLOG = AppConfig.WORKLOG + 'worklog-not-filled-report';
  public static UPDATE_WORKLOG = AppConfig.PORTAL + 'updateWorklog';
  public static MISSED_WORKLOG_FILE = AppConfig.WORKLOG + 'worklog-not-filled-report-file/';
  public static WORKLOG_FILE = AppConfig.WORKLOG + 'DownloadExcelForWorklog/';
  public static WORK_TYPES_ALL = AppConfig.PORTAL + 'worktypes/all';
  public static GET_MISSING_TIME_SHEETS = AppConfig.PORTAL + 'worklog/missing-timesheets/';

  // Leave
  public static LEAVE = AppConfig.PORTAL + 'leaves';
  public static CREATE_LEAVE = AppConfig.PORTAL + 'createLeaves';
  public static UPDATE_LEAVE_STATUS = AppConfig.PORTAL + 'updateLeaveStatus';
  public static FILTER_LEAVES = AppConfig.PORTAL + 'filterAllLeaves';
  public static UPDATE_LEAVE = AppConfig.PORTAL + 'updateLeave';
  public static CAL_END_DATE_OR_NO_OF_DAYS = AppConfig.PORTAL + 'leaves/end-date-and-days-calculation';
  public static LEAVE_FILE = AppConfig.LEAVE + '/DownloadExcelForLeaves/';
  public static DELETE_LEAVE = AppConfig.PORTAL + 'leave';
  public static LEAVE_COUNTS = AppConfig.PORTAL + 'leave-counts/';
  public static LEAVES_TOTAL_COUNTS = AppConfig.PORTAL + 'leaves-total-counts/user/';

  // Client
  public static ALL_CLIENT = AppConfig.PORTAL + 'clients/all';
  public static CREATE_CLIENT = AppConfig.PORTAL + 'client';
  public static UPDATE_CLIENT = AppConfig.PORTAL + 'clients';
  public static FILTER_CLIENT = AppConfig.PORTAL + 'filterAllClients';

  //Clock-In/Out
  public static CLOCK_IN_OUT_EMP = AppConfig.PORTAL + 'clock-in-out';
  public static CLOCK_IN_OUT_PAGEABLE = AppConfig.PORTAL + 'clock-in-out/filterAll';
  public static ACTIVE_USER = AppConfig.PORTAL + 'check-clock-in/';

  // Technologies
  public static TECHNOLOGY = AppConfig.PORTAL + 'technologies';
  public static ALL_TECHNOLOGY = AppConfig.TECHNOLOGY + '/all';
  public static EMPLOYEE_TECHNOLOGIES = AppConfig.PORTAL + 'uaauser-technologies';

  // Schedule
  public static SCHEDULE = AppConfig.PORTAL + 'schedules/';
  public static GET_SCHEDULES = AppConfig.PORTAL + 'schedule/filterAllSchedule';
  public static GET_MY_CURRENT_PROJECTS = AppConfig.PORTAL + 'schedules/projects/';
  public static GET_EMPLOYEE_TECHNOLOGY = AppConfig.PORTAL + 'userNameTechnologiesList';

  //jobcodes
  public static JOB_CODE = AppConfig.PORTAL + 'jobCode';

  //worklogs
  public static TIME_TRACKER = AppConfig.PORTAL + 'timeTrack';

  // Employee Profile
  public static EMPLOYEE_DETAIL = AppConfig.PROFILING + 'employee-profiles/';
  public static GET_EMPLOYEE_DETAIL = AppConfig.PROFILING + 'get-employee-profiles/';
  public static SET_EMPLOYEE_PROFILE = AppConfig.PROFILING + 'set-employee-profile/';
  public static SET_EMPLOYEE_PROFILE_PICTURE = AppConfig.EMPLOYEE_DETAIL + 'upload-image/';
  public static SEARCH_EMPLOYEE_DETAIL = AppConfig.EMPLOYEE_DETAIL + 'search/';
  public static USER_ADDRESSES = AppConfig.PROFILING + 'addresses/';
  public static MASTER_LEVELS = AppConfig.PROFILING + 'master-levels/';
  public static DESIGNATIONS = AppConfig.PROFILING + 'designations';
  public static USER_LEVEL = AppConfig.PROFILING + 'user-levels/';
  public static EMPLOYEE_MANAGEMENT_DETAIL = AppConfig.UAA_API + 'users';
  public static GET_EMPLOYEE_MANAGEMENT_DETAIL = AppConfig.UAA_API + 'users/userId';


  // Awards
  public static USER_AWARDS_FILTER = AppConfig.PROFILING + 'user-awards/filter';
  public static AWARDS = AppConfig.PROFILING + 'awards/';
  public static EMPLOYEE_AWARDS = AppConfig.PROFILING + 'employee-awards';
  public static AWARDS_UPLOAD_IMAGE = AppConfig.AWARDS + 'upload-image/';

  // sfl-forms
  public static CREATE_FORM = AppConfig.PORTAL + 'create-form/';
  public static CREATE_FORM_TYPE = AppConfig.PORTAL + 'create-form-type/';
  public static GENERATED_FORM = AppConfig.PORTAL + 'generate-form/';
  public static FILTER_GENERATED_FORMS = AppConfig.PORTAL + 'filter-all-generated-forms';
  public static FORM_RESPONSE = AppConfig.PORTAL + 'response/';
  public static CREATE_FORM_FOR_PROJECT = AppConfig.PORTAL + 'create-form-for-project/';
  public static GENERATE_FORM_RESPONSE = AppConfig.PORTAL + 'generate-form-response/';
  // public static FORM_AVAILABLE = AppConfig.PORTAL + 'form-available/';

  /* ----------------------------------------------------------------------------------------------------------
       Front-end URLs
       ------------------------------------------------------------------------------------------------------- */
  public static LOGIN = '/login';
  public static _CHANGE_PASSWORD = '/account/change-password';

  public static _DASHBOARD = '/dashboard';
  public static _REPORT_DASHBOARD = '/report-dashboard';
  public static _CLIENT = '/client';
  public static _PROJECT = '/project';
  public static _TECHNOLOGY = '/technology';
  public static _EMPLOYEE_TECHNOLOGIES = '/employee-technologies';
  public static _WORKLOG = '/worklog';
  public static _DAILY_ATTENDANCE = '/daily-attendance';
  public static _EMP_WORKLOG = AppConfig._WORKLOG + '/emp';
  public static _EMPLOYEE_MANAGEMENT = '/employee-management';
  public static _LEAVE = '/leave';
  public static _SCHEDULE = '/schedule';
  public static _TIME_SHEET = '/time-sheet';
  public static _JOB_CODE = '/job-code';
  public static _KUDOS = '/kudos';
  public static _FORMS = '/forms';
  public static _FORMS_CREATE = AppConfig._FORMS + '/create';
  public static _FORMS_FILL = AppConfig._FORMS + '/fill';
  public static _FORMS_RESPONSE = AppConfig._FORMS + '/response';
  public static _WORKLOG_MISSED = AppConfig._WORKLOG + '/missed';
  public static _PROJECT_DETAILS = AppConfig._PROJECT + '/details';
  public static _EMPLOYEE_PROFILE = '/profile/';
  public static _KUDOS_LIST = AppConfig._KUDOS + '/kudos-list';
  public static _REPORTS = '/reports';

  // USER APIS
  public static ROLES = AppConfig.UAA_API + 'users/authorities';


}
