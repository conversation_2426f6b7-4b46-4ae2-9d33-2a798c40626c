<div class="body-content" fxLayout="column" fxFlex="100">

  <div fxFlex fxLayout="row" fxLayoutAlign="space-between" class="mb-1">
    <mat-card-title class="p-10">Clients</mat-card-title>
    <button mat-raised-button class="bt-sfl" (click)="saveClient(null)">Add New Client</button>
  </div>

  <hr class="header-divider">

  <form (ngSubmit)="applyFilter()">
    <div fxLayout="row column" fxLayoutAlign="start center" fxFlex="100" class="sfl-card">
      <mat-card-actions class="filter-input ml-1 mr-1">
        <mat-form-field>
          <mat-select placeholder="Select Client Name" name="clientName" [(ngModel)]="filterClient.clientName">
            <mat-option *ngFor="let client of clients" [value]="client.clientName">{{ client?.clientName }} </mat-option>
          </mat-select>
          <button mat-icon-button matSuffix matTooltip="clear" *ngIf="filterClient.clientName" (click)="resetFilter('clientName')">
            <mat-icon class="filter-clear-icon">highlight_off</mat-icon>
          </button>
        </mat-form-field>
      </mat-card-actions>

      <mat-card-actions class="filter-button-group ml-1">
        <button mat-raised-button class="bt-sfl mr-10px" type="submit">Filter</button>
        <button class="bt-flat" type="button" (click)="resetAllFilter()">Reset All</button>
      </mat-card-actions>
    </div>
  </form>

  <div class="sfl-card">
    <div class="p-25" fxLayoutAlign="center center" fxFlex="100" *ngIf="showLoader" >
      <mat-progress-spinner color="warn" mode="indeterminate"></mat-progress-spinner>
    </div>

    <div *ngIf="!showLoader && noDataFound" class="w-100 mt-40 no-data-found-text">No Client Found.</div>

    <div class="w-100" *ngIf="!showLoader && !noDataFound">
        <table
          class="w-auto"
          mat-table
          matSort
          matSortDisableClear
          [dataSource]="dataSource"
          [matSortActive]="pageable.sort"
          [matSortDirection]="pageable.direction"
          (matSortChange)="getSorting($event)"
        >
          <ng-container matColumnDef="clientName">
            <mat-header-cell *matHeaderCellDef mat-sort-header> Client Name </mat-header-cell>
            <mat-cell *matCellDef="let element">
              {{ element?.clientName }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="email">
            <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Email </mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="20">
              {{ element?.email }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="phoneNumber">
            <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="15"> Phone Number </mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="15">
              {{ element?.phoneNumber }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="status">
            <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="15"> Status </mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="15">
              <mat-slide-toggle (change)="updateClientStatus(element)" name="active" [(ngModel)]="element.status"> </mat-slide-toggle>
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="action">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Action </mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="15">
              <button mat-icon-button (click)="saveClient(element)">
                <mat-icon>edit</mat-icon>
              </button>
            </mat-cell>
          </ng-container>
          <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
          <mat-row *matRowDef="let row; columns: displayedColumns"></mat-row>
        </table>
        <mat-paginator
          [length]="clientPageable.totalElements"
          [pageSizeOptions]="[10, 20, 25]"
          [pageIndex]="pageable.page"
          [pageSize]="pageable.size"
          (page)="getPagination($event)"
          showFirstLastButtons
        >
        </mat-paginator>
      </div>
  </div>

</div>
