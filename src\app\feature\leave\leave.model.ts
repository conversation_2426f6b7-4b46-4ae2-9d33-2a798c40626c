import {Pageable, Sort} from 'src/app/shared/utils/utils';
import {Variable} from '../../shared';

export class Leave {
  id: number;
  userId: number;
  userName: string;
  noOfDay: number;
  dayStatus: string;
  leaveReason: string;
  fromDate: string;
  toDate: string;
  leaveType: string;
  status: boolean;
  uaaUserId: number;
  halfDay: boolean;
  whichHalf: string;

  constructor() {
    this.halfDay = false;
    this.status = false;
  }
}

export class FilterLeaves {
  uaaUserId: number;
  userName: string;
  leaveType: string;
  leaveStatus: string;
  startDate: any;
  endDate: any;
  filterBy: string;
  upToYear: number;
}

export class CalDates {
  fromDate: string;
  noOfDay: number;
  toDate: string;
  halfDay: boolean;
  whichHalf: string;

  constructor() {
    this.halfDay = false;
  }
}

export class LeavePageable {
  content: Leave[];
  pageable: Pageable;
  totalElements: number;
  totalPages: number;
  last: boolean;
  numberOfElements: number;
  first: boolean;
  sort: Sort;
  size: number;
  number: number;
}

export const LEAVE_TYPE_LIST = [
  'Sick leave(Illness or Injury)',
  'Personal leave',
  'Leave without pay'
];

export const LEAVE_DISPLAY_COLUMNS = [
  Variable.USERNAME,
  Variable.FROM_DATE,
  Variable.TO_DATE,
  Variable.NO_OF_DAY,
  Variable.WHICH_HALF,
  Variable.LEAVE_TYPE,
  Variable.LEAVE_REASON,
  Variable.STATUS,
  Variable.ACTION,
  Variable.EDIT_LEAVE
];

export const LEAVE_DATE_WISE_FILTER_LIST = [
  Variable.FREE_SEARCH,
  Variable.NEXT_WEEK,
  Variable.NEXT_MONTH,
  Variable.NEXT_SIX_MONTHS,
  Variable.LAST_WEEK,
  Variable.LAST_MONTH,
  Variable.LAST_YEAR,
  Variable.UPTO_YEAR
];

export const DayHalf = [
  {
    value: 'First Half',
    name: 'First Half'
  },
  {
    value: 'Second Half',
    name: 'Second Half'
  }
];
