<h2 mat-dialog-title>Technology</h2>
<hr class="mb-1">

<mat-dialog-content>
  <form #technologyUpdateForm="ngForm">
    <mat-card-content>

      <mat-form-field>
        <input type="text" matInput placeholder="Technology Name" name="name" aria-label="name"
               [(ngModel)]="technology.name" #technologyName="ngModel" required>
        <mat-error *ngIf="technologyName.touched && technologyName.invalid">
            <small class="mat-text-warn" *ngIf="technologyName?.errors.required">technologyName is required.</small>
        </mat-error>
    </mat-form-field>
    </mat-card-content>
  </form>
</mat-dialog-content>
<hr>

<mat-dialog-actions fxLayoutAlign="end">
  <button class="bt-flat" type="submit" (click)="closeDialog()">Cancel</button>
  <button mat-raised-button class="bt-sfl" type="submit" (click)="saveTechnology()"
          [style.cursor]="technologyUpdateForm.form.invalid ? 'not-allowed' : 'pointer'" [disabled]="technologyUpdateForm.form.invalid">
    Save
  </button>
</mat-dialog-actions>
