export class Variable {
  public static ADD = "ADD";
  public static EDIT = "EDIT";

  public static PERCENTAGE = "%";
  public static BOX_WIDTH_VALUE = "550px";
  public static PAGE_SIZE = [10, 20, 25];
  public static WORKING_HOURS = [1, 2, 3, 4, 5, 6, 7, 8];

  public static DESC = "desc";
  public static ASC = "asc";

  public static ID = "id";
  public static NAME = "name";
  public static EMAIL = "email";
  public static PHONE_NUMBER = "phoneNumber";
  public static PHONE_NO = "phoneNo";
  public static USERNAME = "userName";
  public static EMPLOYEE_NAME = "Employee Name";
  public static CLIENT_NAME = "clientName";
  public static CLIENT_CLIENT_NAME = "client.clientName";
  public static ACCOUNT_MANAGER = "account-manager";
  public static DELIVERY_MANAGER = "delivery-manager";
  public static TECHNICAL_LEAD = "techinical-lead";
  public static DEVELOPER = "developer";
  public static CLIENT = "client";
  public static QA = "qa";

  public static LOCAL_DATE = "localDate";
  public static FROM_DATE = "fromDate";
  public static TO_DATE = "toDate";
  public static START__DATE = "start_date";
  public static END__DATE = "end_date";
  public static START_DATE = "startDate";
  public static END_DATE = "endDate";
  public static Month = "month";
  public static MONTH = "Month";
  public static Date = "date";
  public static DATE = "Date";
  public static HOURS = "hours";
  public static LAST_YEAR = "Last Year";
  public static LAST_MONTH = "Last Month";
  public static LAST_WEEK = "Last Week";
  public static NEXT_WEEK = "Next Week";
  public static NEXT_MONTH = "Next Month";
  public static NEXT_SIX_MONTHS = "Next Six Months";
  public static SEVEN_DAYS = "7 days";
  public static FOURTEEN_DAYS = "14 days";
  public static TWENTY_EIGHT_DAYS = "28 days";
  public static FREE_SEARCH = "Free Search";
  public static UPTO_YEAR = "upto year";

  public static OVERL_OADED = "Overloaded";
  public static UNDER_LOADED = "Underloaded";
  public static NORMAL_LOADED = "Normal loaded";

  public static DATE_FORMAT = "yyyy-MM-dd";
  public static MMM_YYYY = "MMM YYYY";
  public static DD_DDD = "DD(ddd)";
  public static DD = "DD";
  public static YYYY_MM_DD = "YYYY-MM-DD";
  public static DD_MM_YYYY = "DD/MM/yyyy";
  public static MM_DD_YYYY = "MM/dd/yyyy";

  public static IMAGE = "image";
  public static FILE = "file";
  public static _BLANK = "_blank";

  public static INVALID_GRANT = "invalid_grant";
  public static EMAIL_SENT = "Email Sent Successfully";
  public static JOB_CODE_ADDED = "Job code added Successfully";
  public static JOB_CODE_UPDATED = "Job code updated Successfully";
  public static JOB_CODE_ALREADY_ADDED = "Job code is already added!";
  public static WORKLOG_ADDED = "Worklog added Successfully";
  public static WORKLOG_UPDATED = "Worklog updated Successfully";
  public static STATUS_UPDATED = "Status updated Successfully";
  public static EMAIL_NOT_SENT = "Email Not Sent";
  public static SELECTED_JOB_CODE_INACTIVE = "{jobCodeName} Job Code is no longer active.";

  public static STATUS = "status";
  public static ACTIVE = "active";
  public static ACTIVE_PROJECT = "Active";
  public static INACTIVE = "Inactive";
  public static DISABLED = "DISABLED";

  public static PRIMARY = "primary";
  public static SECONDARY = "secondary";

  public static BAR = "bar";
  public static PIE = "pie";
  public static BOTTOM = "bottom";

  public static CANVAS = "CANVAS";
  public static CHART_PER_USER_TOTAL_HOURS_ID = "chartPerUserTotalHours";
  public static CHART_DATE_WISE_TOTAL_HOURS_ID = "chartDateWiseTotalHours";
  public static CHART_MONTH_WISE_TOTAL_HOURS_ID = "chartMonthWiseTotalHours";
  public static CHART_PER_WORK_TYPE_TOTAL_HOURS_ID = "chartPerWorkTypeTotalHours";
  public static CHART_PER_PROJECT_TOTAL_HOURS_ID = "chartPerProjectTotalHours";
  public static CHART_HEIGHT = "450";
  public static RESPONSIVE_CHART_ENTITY_WIDTH = 20;
  public static PER_PROJECT_TOTAL_HOURS = "Per Project Total Hours";
  public static PER_USER_TOTAL_HOURS = "Per User Total Hours";
  public static PER_WORK_TYPE_TOTAL_HOURS = "Per Work type Total Hours";
  public static DATE_WISE_TOTAL_HOURS = "Date Wise Total Hours";
  public static MONTH_WISE_TOTAL_HOURS = "Month Wise Total Hours";
  public static NUMBER_OF_HOURS = "Number of Hours";

  public static ACTION = "action";
  public static NOTE = "note";
  public static DESCRIPTION = "description";
  public static PURPOSE = "purpose";

  public static PROJECT_NAME = "projectName";
  public static Project_Name = "Project Name";
  public static EMP_NAME = "empName";
  public static CLOCK_IN = "clockIn";
  public static CLOCK_OUT = "clockOut";

  public static LEAVE_TYPE = "leaveType";
  public static LEAVE_REASON = "leaveReason";
  public static NO_OF_DAY = "noOfDay";
  public static WHICH_HALF = "whichHalf";
  public static EDIT_LEAVE = "editleave";
  public static DEFAULT_INACTIVE_FILTER = "inactive";
  public static DEFAULT_ACTIVE_FILTER = "active";

  public static WORK_TYPE = "worktype";
  public static WORK_TYPE_NAME = "workTypeName";
  public static YOUR_WORKLOG = "your Worklog";

  public static FORM_NAME = "formName";
  public static CREATED_FORM_ID = "createdFormId";
  public static GENERATED_FORM_ID = "generatedFormId";

  public static THIS_WORK_LOG = "this worklog";
  public static THIS_SCHEDULE = "this schedule";
  public static THIS_FORM = "this form";
  public static THIS_AWARD = "this Award";
  public static AWARD = "awards";
  public static AWARDED_DATE = "awardedDate";
  public static AWARDED_BY = "awardedBy";

  public static IST_OFFSET = 330;

  public static BASIC_CHART_COLOR = "rgba(255, 89, 10, 0.5)";
  public static PIE_CHART_COLORS = ["#ffb999", "#ffa880", "#ff9666", "#ff854d", "#ff7433", "#ff621a", "#ff590a", "#ff5100", "#e64900"];

  public static DATE_WISE_FILTER_LIST = [Variable.FREE_SEARCH, Variable.LAST_WEEK, Variable.LAST_MONTH, Variable.LAST_YEAR, Variable.UPTO_YEAR];
}

export const ROLES = {
  SUPER_ADMIN: "ROLE_SUPER_ADMIN",
  ADMIN: "ROLE_ADMIN",
  LEAD: "ROLE_LEAD",
  HR: "ROLE_HR",
  USER: "ROLE_USER",
  CLIENT: "ROLE_CLIENT",
};
