<div class="account-wrapper">
  <div class="account">
    <mat-card>
      <mat-card-content>
        <div class="login-head">
          <h3>LOGIN</h3>
          <p class="sub-title-font-color">Log in with your credentials to continue.</p>
        </div>

        <div>
          <form #loginForm="ngForm" (ngSubmit)="login()" fxFlex="100" fxLayout="row column">
            <div class="p-2" fxFlex.gt-lg="48" fxFlex.gt-md="48" fxFlex.gt-sm="100" fxFlex.gt-xs="100" fxLayout="row" fxLayoutAlign="center center">
              <img class="w-100" src="../../../assets/images/logo.png" alt="logo"/>
            </div>

            <div class="vertical-divider"></div>

            <div fxFlex.gt-lg="50" fxFlex.gt-md="50" fxFlex.gt-sm="100" fxFlex.gt-xs="100" class="w-100 p-2">
              <div class="mat-badge-warn" *ngIf="isError">
                <strong>Failed to sign in!</strong> Please check your credentials and try again.
              </div>

              <div fxLayout="column">
                <mat-form-field>
                  <input matInput type="email" name="email" placeholder="Username" aria-label="email" [(ngModel)]="username" #emailInput="ngModel" required>
                </mat-form-field>
                <mat-error *ngIf="emailInput.touched && emailInput.invalid" class="login">
                  <small class="mat-text-warn" *ngIf="emailInput?.errors.required">Username is required.</small>
                </mat-error>

                <mat-form-field>
                  <input matInput type="password" placeholder="Password" name="password" aria-label="password" [(ngModel)]="password" #passwordInput="ngModel" required>
                </mat-form-field>
                <mat-error *ngIf="passwordInput.touched && passwordInput.invalid" class="login">
                  <small class="mat-text-warn" *ngIf="passwordInput?.errors.required">Password is required.</small>
                </mat-error>

                <div class="pb-1">
                  <mat-checkbox color="warn">Stay logged in</mat-checkbox>
                </div>

                <button mat-raised-button type="submit" color="primary" class="bg-sfl"
                        [style.cursor]="loginForm.form.invalid ? 'not-allowed' : 'pointer'" [disabled]="loginForm.form.invalid">Login
                </button>

                <div class="pt-1 pb-1" fxLayoutAlign="end">
                  <a [routerLink]="['/forgot-password']">Forgot password?</a>
                </div>
              </div>
            </div>
          </form>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
