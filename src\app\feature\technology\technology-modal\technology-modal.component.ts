import {Component, OnInit, Inject, OnDestroy} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material';
import {Technology} from '../technology.model';
import { Subscription } from 'rxjs';
import { TechnologyComponent } from '../technology.component';
import { TechnologyService } from '../technology.service';
import { Router } from '@angular/router';
import {AppConfig} from '../../../app.config';


@Component({
  selector: 'sfl-update-technology',
  templateUrl: './technology-modal.component.html'
})
export class TechnologyModalComponent implements OnInit, OnDestroy {
  technology: Technology = new Technology();
  subscription: Subscription = new Subscription;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: Technology,
    public dialogRef: MatDialogRef<TechnologyComponent>,
    private router: Router,
    private technologyService: TechnologyService
  ) { }

  ngOnInit() {
    if (this.data) {
      this.technology = this.data;
    }
  }

  saveTechnology() {
    if (this.data) {
      this.updateTechnology();
    } else {
      this.addTechnology();
    }
  }

  addTechnology() {
    this.subscription.add(this.technologyService.createTechnology(this.technology).subscribe(() => {
      this.technologyService.callMethodOfWorklogComponent();
      this.closeDialog();
      this.router.navigate([AppConfig._TECHNOLOGY]).then();
    }));
  }

  updateTechnology() {
    this.subscription.add(this.technologyService.updateTechnology(this.technology).subscribe(() => {
      this.technologyService.callMethodOfWorklogComponent();
      this.closeDialog();
      this.router.navigate([AppConfig._TECHNOLOGY]).then();
    }));
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
