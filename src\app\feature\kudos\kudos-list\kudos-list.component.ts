import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>nit, ViewChild} from '@angular/core';
import {MatDialog, MatPaginator, MatSort, MatTableDataSource} from '@angular/material';
import {Variable} from '../../../shared';
import {<PERSON><PERSON>, KUDOS_LIST_DISPLAY_COLUMNS} from '../kudos.model';
import {Subscription} from 'rxjs';
import {KudosService} from '../kudos.service';
import {SaveKudosComponent} from '../kudos-modals/save-kudos/save-kudos.component';
import {SweetAlertService} from '../../../shared/service/sweetalert.service';
import {finalize} from 'rxjs/operators';

@Component({
  selector: 'sfl-kudos-list',
  templateUrl: './kudos-list.component.html'
})
export class KudosListComponent implements OnInit, OnDestroy {
  @ViewChild(MatPaginator, {static: false}) set paginator(value: MatPaginator) {
    this.dataSource.paginator = value;
  }
  @ViewChild(MatSort, {static: false}) set sort(value: MatSort) {
    this.dataSource.sort = value;
  }

  showLoader = true;
  noDataFound = true;
  displayedColumns = KUDOS_LIST_DISPLAY_COLUMNS;
  dataSource = new MatTableDataSource<Kudos>([]);
  subscription: Subscription = new Subscription();

  constructor(
    private matDialog: MatDialog,
    private kudosListService: KudosService,
    private alertService: SweetAlertService
  ) { }

  ngOnInit() {
    this.getKudos();
  }

  getKudos() {
    this.showLoader = true;
    this.noDataFound = true;
    this.subscription.add(this.kudosListService.getKudos()
      .pipe(
        finalize(() => {
          this.noDataFound = this.dataSource.data.length <= 0;
          this.showLoader = false;
        })
      )
      .subscribe((res: Kudos[]) => {
        this.dataSource = new MatTableDataSource<Kudos>(res);
      })
    );
  }

  deleteKudos(kudos: Kudos) {
    const confirmation = this.alertService.deleteAlert(Variable.THIS_AWARD);
    confirmation.then(value => {
      if (value === true) {
        this.kudosListService.deleteKudos(kudos).subscribe(() => {
          this.getKudos();
        });
      }
    });
  }

  saveKudos(kudos: Kudos) {
    const dialogRef = this.matDialog.open(SaveKudosComponent, {
      width: Variable.BOX_WIDTH_VALUE,
      data: kudos
    });
    dialogRef.afterClosed().subscribe(() => {
      this.getKudos();
    });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
