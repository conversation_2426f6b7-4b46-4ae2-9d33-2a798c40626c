import {Component, Inject, OnDestroy, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {FormResponse, GenerateFormResponse, QUESTION_TYPE, SflFormsModel} from '../../sfl-forms.model';
import {FormResponseComponent} from '../form-response.component';
import {Subscription} from 'rxjs';
import {SflFormsService} from '../../sfl-forms.service';


@Component({
  selector: 'sfl-form-response-modal',
  templateUrl: './form-response-modal.component.html'
})
export class FormResponseModalComponent implements OnInit, OnDestroy {
  showLoader = false;
  questionType = QUESTION_TYPE;
  sflForm: SflFormsModel = new SflFormsModel();
  formResponse: FormResponse = new FormResponse();
  subscription: Subscription = new Subscription();

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: GenerateFormResponse,
    private sflFormsService: SflFormsService,
    public dialogRef: MatDialogRef<FormResponseComponent>
  ) { }

  ngOnInit() {
    this.getResponse();
  }

  getResponse() {
    this.showLoader = true;
    this.subscription.add(
      this.sflFormsService.getFormResponseById(this.data.responseId).subscribe((res: FormResponse) => {
        this.formResponse = res;
        this.getCreatedForm();
        this.showLoader = false;
      })
    );
  }

  getCreatedForm() {
    this.subscription.add(
      this.sflFormsService.getCreatedFormsById(this.formResponse.createdFormId).subscribe((res: SflFormsModel) => {
        this.sflForm = res;
      })
    );
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
